(()=>{var t={};jQuery(document).ready((function(t){var a,o;var e=wp.i18n.__;var r=_tutor_chatgpt.bubble_position;var i={bottom_right:"bottom_right",bottom_left:"bottom_left",top_right:"top_right",top_left:"top_left"};var n=null;var c=null;var u="tooltip-left";switch(r){case i.bottom_left:case i.top_left:u="tooltip-right";break;case i.bottom_right:case i.top_right:u="tooltip-left";break}var s='<div class="tutor-chatgpt-bubble tooltip-wrap">\n                        <span class="tooltip-txt '.concat(u,'">\n                            <div><span>Generate Content</span></div>\n                        </span>\n                      </div>');function l(a){var o=t("div.tutor-chatgpt-bubble");if(o.length===0)return;var e=0;var c=8;var u=c;if(a==="tinymce"){e=t(n).find(".mce-toolbar-grp").eq(0).height();u=e+c}switch(r){case i.bottom_left:o.css("left","".concat(c,"px"));break;case i.top_left:o.css({top:"".concat(u,"px"),left:"".concat(c,"px")});break;case i.top_right:o.css({top:"".concat(u,"px"),right:"".concat(c,"px")});break}}function p(a){t("div.tutor-chatgpt-bubble").remove();if(a.nodeName==="DIV"){if(t(a).parent().find("div.tutor-chatgpt-bubble").length===0){t(a).parent().append(s);t("div.tutor-chatgpt-bubble").css("bottom","25px");l("tinymce")}}if(a.nodeName==="TEXTAREA"){if(t(a).parent().is("div.tutor-chatgpt-wrapper")===false){t(a).wrap('<div class="tutor-chatgpt-wrapper"></div>');t(a).parent().append(s)}else{t(a).parent().append(s)}l("textarea")}}var d=["textarea.wp-editor-area",'textarea[name="topic_summery"]','textarea[name="quiz_description"]','textarea[data-name="meeting_summary"]','textarea[name="meeting_summary"]'];t("body").on("click",d.join(","),(function(t){n=t.target;p(t.target)}));function m(a){c=a.target;if(a.target.container){n=a.target.container;p(a.target.container)}else{a.target.editorContainer.parentElement.style.position="relative";n=a.target.editorContainer;p(a.target.editorContainer);t("div.tutor-chatgpt-bubble").css("bottom","35px")}}(a=tinymce.get("content"))===null||a===void 0?void 0:a.on("focus",m);t("#content-tmce").click((function(){setTimeout((function(){c=tinymce.get("content");p(c.container)}),500)}));t("#content-html").click((function(){c=null;setTimeout((function(){n=document.querySelector("textarea.wp-editor-area");p(n)}),500)}));(o=tinymce.get("course_description"))===null||o===void 0?void 0:o.on("focus",m);window.addEventListener("tutor_modal_shown",(function(){var t,a,o;(t=tinymce.get("tutor_lesson_modal_editor"))===null||t===void 0?void 0:t.on("focus",m);(a=tinymce.get("tutor_quiz_desc_text_editor"))===null||a===void 0?void 0:a.on("focus",m);(o=tinymce.get("tutor_assignments_modal_editor"))===null||o===void 0?void 0:o.on("focus",m)}));t("body").on("click","div.tutor-chatgpt-bubble",(function(){if(_tutor_chatgpt.has_api_key==="1"){t("#tutor-chatgpt-modal").addClass("tutor-is-active");t('input[name="tutor-chatgpt-word-limit"]').val("");setTimeout((function(){return t("textarea.tutor-chatgpt-input").val("").focus()}))}else{t("#tutor-chatgpt-api-key-modal").addClass("tutor-is-active");setTimeout((function(){return t("input.tutor-chatgpt-api-key").val("").focus()}))}}));t("body").on("click","#tutor-chatgpt-modal .tutor-modal-close",(function(){t("#tutor-chatgpt-modal").removeClass("tutor-is-active")}));t("#tutor-chatgpt-modal .tutor-btn-submit").on("click",v);function v(){var a=t("#tutor-chatgpt-modal .tutor-btn-submit");var o=t("textarea.tutor-chatgpt-input").val().trim();var e=t('input[name="tutor-chatgpt-word-limit"]').val();if(o!==""){if(parseInt(e)>0){o="".concat(o," in ").concat(e," words")}a.attr("disabled","disabled").addClass("is-loading");t.ajax({url:_tutorobject.ajaxurl,method:"POST",dataType:"JSON",data:{prompt:o,action:"tutor_pro_chatgpt"},success:g,complete:function t(){a.removeAttr("disabled").removeClass("is-loading")}})}}function g(a){if(!a.success){tutor_toast(e("Sorry!","tutor-pro"),a.data.message,"error")}if(a.success){t("input.tutor-chatgpt-input").val("");var o=a.data.text;var r="";if(n.nodeName==="TEXTAREA"){var i;r=((i=n.value)===null||i===void 0?void 0:i.trim())!==""?"\n":"";n.value="".concat(n.value).concat(r).concat(o)}else{var u;r=c.getContent({format:"text"}).trim()!==""?"<br>":"";(u=c)===null||u===void 0?void 0:u.execCommand("mceInsertContent",false,"".concat(r).concat(o))}t("#tutor-chatgpt-modal").removeClass("tutor-is-active")}}t("#tutor-chatgpt-api-key-modal .tutor-btn-submit").on("click",(function(a){var o=t("#tutor-chatgpt-api-key-modal .tutor-btn-submit");var r=t("input.tutor-chatgpt-api-key").val().trim();var i=t('input[name="tutor_pro_chatgpt_enable"]').is(":checked");if(r===""&&i){tutor_toast(e("Sorry!","tutor-pro"),e("API Key required","tutor-pro"),"error");return}o.attr("disabled","disabled").addClass("is-loading");t.ajax({url:_tutorobject.ajaxurl,method:"POST",dataType:"JSON",data:{api_key:r,chatgpt_enable:i,action:"tutor_pro_chatgpt_save_settings"},success:function a(o){if(!o.success){tutor_toast(e("Sorry!","tutor-pro"),o.data.message,"error")}if(o.success){tutor_toast(e("Success!","tutor-pro"),o.data.message,"success");if(i){_tutor_chatgpt.has_api_key="1";t("#tutor-chatgpt-api-key-modal").removeClass("tutor-is-active");t("#tutor-chatgpt-modal").addClass("tutor-is-active");t("#tutor-chatgpt-modal textarea.tutor-chatgpt-input").val("").focus()}else{window.location.reload(true)}}},complete:function t(){o.removeAttr("disabled").removeClass("is-loading")}})}))}))})();