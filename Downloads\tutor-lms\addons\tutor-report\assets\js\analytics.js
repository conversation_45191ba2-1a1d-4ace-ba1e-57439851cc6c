(()=>{var t={};function e(t,e){return n(t)||a(t,e)||i(t,e)||r()}function r(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,n,o,i,l=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(a=o.call(r)).done)&&(l.push(a.value),l.length!==e);s=!0);}catch(t){c=!0,n=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw n}}return l}}function n(t){if(Array.isArray(t))return t}function o(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=i(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var a=0;var n=function t(){};return{s:n,n:function e(){if(a>=t.length)return{done:true};return{done:false,value:t[a++]}},e:function t(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o=true,l=false,s;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();o=e.done;return e},e:function t(e){l=true;s=e},f:function t(){try{if(!o&&r["return"]!=null)r["return"]()}finally{if(l)throw s}}}}function i(t,e){if(!t)return;if(typeof t==="string")return l(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,e)}function l(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}window.onload=function(){var t=document.getElementById("tutor_analytics_search_icon");if(t){t.onclick=function(){var t=document.getElementById("tutor_analytics_search_form");t.submit()}}var r=document.querySelectorAll(".tutor-admin-report-frequency");var a=o(r),n;try{for(a.s();!(n=a.n()).done;){var i=n.value;i.onclick=function(t){var e=t.target.dataset.key;if(e==="custom"){return}var r=new URL(window.location.href);var a=r.searchParams;if(a.has("start_date")){a["delete"]("start_date")}if(a.has("end_date")){a["delete"]("end_date")}a.set("period",e);window.location=r}}}catch(t){a.e(t)}finally{a.f()}var l=o(_tutor_analytics),s;try{for(l.s();!(s=l.n()).done;){var c=s.value;var u=document.getElementById("".concat(c.id,"_canvas")).getContext("2d");var d=[];var f=[];var v=[];for(var m=0,g=Object.entries(c.data);m<g.length;m++){var y=e(g[m],2),b=y[0],h=y[1];var p={month:"short",day:"numeric"};var _=new Date(h.date_format);var A=_.toLocaleDateString("en-US",p);d.push(A);f.push(h.total);if(h.fees){v.push(h.fees)}}var w=[];w.push({label:c.label,backgroundColor:"#3057D5",borderColor:"#3057D5",data:f,borderWidth:2,fill:false,lineTension:0});if(v.length){w.push({label:c.label2,backgroundColor:"rgba(200, 0, 0, 1)",borderColor:"rgba(200, 0, 0, 1)",data:v,borderWidth:2,fill:false,lineTension:0})}new Chart(u,{type:"line",data:{labels:d,datasets:w},options:{scales:{yAxes:[{ticks:{min:0,beginAtZero:true,callback:function t(e,r,a){if(Math.floor(e)===e){return e}}}}]},legend:{display:false}}})}}catch(t){l.e(t)}finally{l.f()}(function t(){document.addEventListener("click",(function(t){var e="data-tutor-modal-target";var r="data-tutor-modal-close";var a="tutor-modal-overlay";if(t.target.hasAttribute(e)||t.target.closest("[".concat(e,"]"))){t.preventDefault();var n=t.target.hasAttribute(e)?t.target.getAttribute(e):t.target.closest("[".concat(e,"]")).getAttribute(e);var o=document.getElementById(n);if(o){}}if(t.target.hasAttribute(r)||t.target.classList.contains(a)||t.target.closest("[".concat(r,"]"))){t.preventDefault();var i=document.querySelectorAll(".tutor-modal.tutor-is-active");i.forEach((function(t){t.classList.remove("tutor-is-active")}))}}))})()};function s(t){var e="data-tutor-modal-target";var r="data-tutor-modal-close";var a="tutor-modal-overlay";if(t.target.hasAttribute(e)||t.target.closest("[".concat(e,"]"))){t.preventDefault();var n=t.target.hasAttribute(e)?t.target.getAttribute(e):t.target.closest("[".concat(e,"]")).getAttribute(e);var o=document.getElementById(n);if(o){o.classList.add("tutor-is-active")}}if(t.target.hasAttribute(r)||t.target.classList.contains(a)||t.target.closest("[".concat(r,"]"))){t.preventDefault();var i=document.querySelectorAll(".tutor-modal.tutor-is-active");i.forEach((function(t){t.classList.remove("tutor-is-active")}))}}jQuery(document).ready((function(t){function e(e){e.preventDefault();var r=e.target;r.classList.add("is-loading");r.setAttribute("disabled","disabled");t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:{course_id:e.target.dataset.course_id,total_progress:e.target.dataset.total_progress,total_lesson:e.target.dataset.total_lesson,completed_lesson:e.target.dataset.completed_lesson,total_assignment:e.target.dataset.total_assignment,completed_assignment:e.target.dataset.completed_assignment,total_quiz:e.target.dataset.total_quiz,completed_quiz:e.target.dataset.completed_quiz,student_id:e.target.dataset.student_id,action:"view_progress"},beforeSend:function t(){},success:function t(e){document.getElementById("tutor_progress_modal_content").innerHTML=e;var r=document.getElementById("modal-course-overview");r.classList.add("tutor-is-active")},complete:function t(){r.classList.remove("is-loading");r.removeAttribute("disabled");tutorAccordion()}})}t(".analytics_view_course_progress").on("click",(function(t){e(t)}))}))})();