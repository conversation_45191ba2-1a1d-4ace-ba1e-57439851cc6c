"use strict";(self["webpackChunktutor_pro"]=self["webpackChunktutor_pro"]||[]).push([[7770],{3195:(t,e,r)=>{r.r(e);r.d(e,{default:()=>Hh});var n=r(6657);var o=r(3297);var i=r(9640);var a=r(8003);var u=r(7363);var l=r.n(u);var c=r(7536);var s=r(960);var d=r(7935);var f=r(9660);var p=r(4805);var v=r(5885);var h=r(1605);var y=r(7976);var b=r(1457);function g(){g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return g.apply(this,arguments)}function m(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var w=l().forwardRef((function(t,e){var r=t.id,o=r===void 0?(0,b.x0)():r,i=t.name,a=t.labelCss,u=t.inputCss,c=t.label,s=c===void 0?"":c,d=t.checked,f=t.value,p=t.disabled,v=p===void 0?false:p,h=t.onChange,y=t.onBlur,m=t.isIndeterminate,w=m===void 0?false:m;var x=function t(e){h===null||h===void 0?void 0:h(!w?e.target.checked:true,e)};var Z=function t(e){if(typeof e==="string"){return e}if(typeof e==="number"||typeof e==="boolean"||e===null){return String(e)}if(e===undefined){return""}if(l().isValidElement(e)){var r;var n=(r=e.props)===null||r===void 0?void 0:r.children;if(typeof n==="string"){return n}if(Array.isArray(n)){return n.map((function(t){return typeof t==="string"?t:""})).filter(Boolean).join(" ")}}return""};return(0,n.tZ)("label",{htmlFor:o,css:[_.container({disabled:v}),a,true?"":0,true?"":0]},(0,n.tZ)("input",g({},t,{ref:e,id:o,name:i,type:"checkbox",value:f,checked:!!d,disabled:v,"aria-invalid":t["aria-invalid"],onChange:x,onBlur:y,css:[u,_.checkbox({label:!!s,isIndeterminate:w,disabled:v}),true?"":0,true?"":0]})),(0,n.tZ)("span",null),(0,n.tZ)("span",{css:[_.label({isDisabled:v}),a,true?"":0,true?"":0],title:Z(s)},s))}));var x=true?{name:"1sfig4b",styles:"cursor:not-allowed"}:0;var _={container:function t(e){var r=e.disabled,o=r===void 0?false:r;return(0,n.iv)("position:relative;display:flex;align-items:center;cursor:pointer;user-select:none;color:",h.Jv.text.title,";",o&&x,";"+(true?"":0),true?"":0)},label:function t(e){var r=e.isDisabled,o=r===void 0?false:r;return(0,n.iv)(y.c.caption(),";margin-top:",h.W0[2],";color:",h.Jv.text.title,";",o&&(0,n.iv)("color:",h.Jv.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},checkbox:function t(e){var r=e.label,o=e.isIndeterminate,i=e.disabled;return(0,n.iv)("position:absolute;opacity:0!important;height:0;width:0;&+span{position:relative;cursor:pointer;display:inline-flex;align-items:center;",r&&(0,n.iv)("margin-right:",h.W0[10],";"+(true?"":0),true?"":0),";}&+span::before{content:'';background-color:",h.Jv.background.white,";border:1px solid ",h.Jv.stroke["default"],";border-radius:3px;width:20px;height:20px;}&:checked+span::before{background-image:url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0wLjE2NTM0NCA0Ljg5OTQ2QzAuMTEzMjM1IDQuODQ0OTcgMC4wNzE3MzQ2IDQuNzgxMTUgMC4wNDI5ODg3IDQuNzExM0MtMC4wMTQzMjk2IDQuNTU1NjQgLTAuMDE0MzI5NiA0LjM4NDQ5IDAuMDQyOTg4NyA0LjIyODg0QzAuMDcxMTU0OSA0LjE1ODY4IDAuMTEyNzIzIDQuMDk0NzUgMC4xNjUzNDQgNC4wNDA2OEwxLjAzMzgyIDMuMjAzNkMxLjA4NDkzIDMuMTQzNCAxLjE0ODkgMy4wOTU1NyAxLjIyMDk2IDMuMDYzNjlDMS4yOTAzMiAzLjAzMjEzIDEuMzY1NTQgMy4wMTU2OSAxLjQ0MTY3IDMuMDE1NDRDMS41MjQxOCAzLjAxMzgzIDEuNjA2MDUgMy4wMzAyOSAxLjY4MTU5IDMuMDYzNjlDMS43NTYyNiAzLjA5NzA3IDEuODIzODYgMy4xNDQ1NyAxLjg4MDcxIDMuMjAzNkw0LjUwMDU1IDUuODQyNjhMMTAuMTI0MSAwLjE4ODIwNUMxMC4xNzk0IDAuMTI5NTQ0IDEwLjI0NTQgMC4wODIwNTQyIDEwLjMxODQgMC4wNDgyOTA4QzEwLjM5NDEgMC4wMTU0NjYxIDEwLjQ3NTkgLTAuMDAwOTcyMDU3IDEwLjU1ODMgNC40NDIyOGUtMDVDMTAuNjM1NyAwLjAwMDQ3NTMxOCAxMC43MTIxIDAuMDE3NDc5NSAxMC43ODI0IDAuMDQ5OTI0MkMxMC44NTI3IDAuMDgyMzY4OSAxMC45MTU0IDAuMTI5NTA5IDEwLjk2NjIgMC4xODgyMDVMMTEuODM0NyAxLjAzNzM0QzExLjg4NzMgMS4wOTE0MiAxMS45Mjg4IDEuMTU1MzQgMTEuOTU3IDEuMjI1NUMxMi4wMTQzIDEuMzgxMTYgMTIuMDE0MyAxLjU1MjMxIDExLjk1NyAxLjcwNzk2QzExLjkyODMgMS43Nzc4MSAxMS44ODY4IDEuODQxNjMgMTEuODM0NyAxLjg5NjEzTDQuOTIyOCA4LjgwOTgyQzQuODcxMjkgOC44NzAyMSA0LjgwNzQ3IDguOTE4NzUgNC43MzU2NiA4Ljk1MjE1QzQuNTgyMDIgOS4wMTU5NSA0LjQwOTQ5IDkuMDE1OTUgNC4yNTU4NCA4Ljk1MjE1QzQuMTg0MDQgOC45MTg3NSA0LjEyMDIyIDguODcwMjEgNC4wNjg3MSA4LjgwOTgyTDAuMTY1MzQ0IDQuODk5NDZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');background-repeat:no-repeat;background-size:10px 10px;background-position:center center;border-color:transparent;background-color:",h.Jv.icon.brand,";border-radius:",h.E0[4],";",i&&(0,n.iv)("background-color:",h.Jv.icon.disable["default"],";"+(true?"":0),true?"":0),";}",o&&(0,n.iv)("&+span::before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='2' fill='none'%3E%3Crect width='10' height='1.5' y='.25' fill='%23fff' rx='.75'/%3E%3C/svg%3E\");background-repeat:no-repeat;background-size:10px;background-position:center center;background-color:",h.Jv.brand.blue,";border:0.5px solid ",h.Jv.stroke.white,";}"+(true?"":0),true?"":0)," ",i&&(0,n.iv)("&+span{cursor:not-allowed;&::before{border-color:",h.Jv.stroke.disable,";}}"+(true?"":0),true?"":0)," &:focus-visible{&+span{border-radius:",h.E0[2],";outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0)}};const Z=w;var O=r(8015);var S=r(1007);var k=r(276);var j=r(2749);var E=r(4033);function C(t,e){return I(t)||L(t,e)||P(t,e)||A()}function A(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function P(t,e){if(!t)return;if(typeof t==="string")return W(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return W(t,e)}function W(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function L(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function I(t){if(Array.isArray(t))return t}var D=function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"";return(0,u.useMemo)((function(){var t;if(!(0,E.$K)(e)){return true}var r=(e===null||e===void 0?void 0:e.split("."))||[],n=C(r,2),o=n[0],i=n[1];if(!(0,E.$K)(o)||!(0,E.$K)(i)){return true}var a=j.y===null||j.y===void 0?void 0:(t=j.y.visibility_control)===null||t===void 0?void 0:t[o];if(!a){return true}var u=j.y.current_user.roles;var l=u.includes("administrator")?"admin":"instructor";var c="".concat(i,"_").concat(l);if(!Object.keys(a).includes(c)){return true}return a[c]==="on"}),[e])};const T=D;var M=["visibilityKey"];function J(t,e){if(t==null)return{};var r=N(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function N(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}var F=function t(e){return function(t){var r=t.visibilityKey,o=J(t,M);var i=T(r);if(!i){return null}return(0,n.tZ)(e,o)}};function B(t,e){return Q(t)||G(t,e)||R(t,e)||z()}function z(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function R(t,e){if(!t)return;if(typeof t==="string")return U(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return U(t,e)}function U(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function G(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Q(t){if(Array.isArray(t))return t}var Y=function t(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:300;var n=(0,u.useState)(e),o=B(n,2),i=o[0],a=o[1];(0,u.useEffect)((function(){var t=setTimeout((function(){a(e)}),r);return function(){clearTimeout(t)}}),[e,r]);return i};function q(t){"@babel/helpers - typeof";return q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},q(t)}function H(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function V(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?H(Object(r),!0).forEach((function(e){$(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function $(t,e,r){e=K(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function K(t){var e=X(t,"string");return q(e)==="symbol"?e:String(e)}function X(t,e){if(q(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(q(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function tt(t,e){return it(t)||ot(t,e)||rt(t,e)||et()}function et(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function rt(t,e){if(!t)return;if(typeof t==="string")return nt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nt(t,e)}function nt(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ot(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function it(t){if(Array.isArray(t))return t}var at=function t(e){var r=(0,u.useState)(),n=tt(r,2),o=n[0],i=n[1];var a=(0,p.cI)(e);return V(V({},a),{},{submitError:o,setSubmitError:i})};function ut(t){"@babel/helpers - typeof";return ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ut(t)}function lt(t,e){return pt(t)||ft(t,e)||st(t,e)||ct()}function ct(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function st(t,e){if(!t)return;if(typeof t==="string")return dt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dt(t,e)}function dt(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ft(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function pt(t){if(Array.isArray(t))return t}function vt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ht(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vt(Object(r),!0).forEach((function(e){yt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yt(t,e,r){e=bt(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function bt(t){var e=gt(t,"string");return ut(e)==="symbol"?e:String(e)}function gt(t,e){if(ut(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ut(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var mt={defaultValue:false};var wt=function t(e){var r=(0,u.useRef)(null);var n=ht(ht({},mt),e);var o=(0,u.useState)(n.defaultValue),i=lt(o,2),a=i[0],l=i[1];(0,u.useEffect)((function(){if(!(0,E.$K)(r.current)){return}if(r.current.scrollHeight<=r.current.clientHeight){l(false);return}var t=function t(e){var r=e.target;if(r.scrollTop+r.clientHeight>=r.scrollHeight){l(false);return}l(r.scrollTop>=0)};r.current.addEventListener("scroll",t);return function(){var e;(e=r.current)===null||e===void 0?void 0:e.removeEventListener("scroll",t)}}),[r.current]);return{ref:r,isScrolling:a}};var xt=r(4428);var _t=r(368);var Zt=r(5332);var Ot=r(7855);var St=r(7837);var kt=r(3639);var jt=function t(e){return St.B.get(kt.Z.CATEGORIES,e?{params:{per_page:100,search:e}}:{params:{per_page:100}})};var Et=function t(e){return(0,_t.a)({queryKey:["CategoryList",e],queryFn:function t(){return jt(e).then((function(t){return t.data}))}})};var Ct=function t(e){return St.B.post(kt.Z.CATEGORIES,e)};var At=function t(){var e=(0,o.NL)();var r=(0,Ot.p)(),n=r.showToast;return(0,Zt.D)({mutationFn:Ct,onSuccess:function t(){e.invalidateQueries({queryKey:["CategoryList"]})},onError:function t(e){n({type:"danger",message:(0,b.Mo)(e)})}})};var Pt=r(7901);var Wt=r(4329);function Lt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var It=function t(e){var r;var o=e.field,i=e.fieldState,u=e.children,l=e.disabled,c=l===void 0?false:l,s=e.readOnly,f=s===void 0?false:s,p=e.label,v=e.isInlineLabel,y=v===void 0?false:v,g=e.variant,m=e.loading,w=e.placeholder,x=e.helpText,_=e.isHidden,Z=_===void 0?false:_,S=e.removeBorder,j=S===void 0?false:S,C=e.characterCount,A=e.isSecondary,P=A===void 0?false:A,W=e.inputStyle,L=e.onClickAiButton,I=e.isMagicAi,D=I===void 0?false:I,T=e.generateWithAi,M=T===void 0?false:T,J=e.replaceEntireLabel,N=J===void 0?false:J;var F=(0,b.x0)();var B=[Bt.input({variant:g,hasFieldError:!!i.error,removeBorder:j,readOnly:f,hasHelpText:!!x,isSecondary:P,isMagicAi:D})];if((0,E.$K)(W)){B.push(W)}var z=(0,n.tZ)("div",{css:Bt.inputWrapper},u({id:F,name:o.name,css:B,"aria-invalid":i.error?"true":"false",disabled:c,readOnly:f,placeholder:w,className:"tutor-input-field"}),m&&(0,n.tZ)("div",{css:Bt.loader},(0,n.tZ)(O.ZP,{size:20,color:h.Jv.icon["default"]})));return(0,n.tZ)("div",{css:Bt.container({disabled:c,isHidden:Z}),"data-cy":"form-field-wrapper"},(0,n.tZ)("div",{css:Bt.inputContainer(y)},(p||x)&&(0,n.tZ)("div",{css:Bt.labelContainer},p&&(0,n.tZ)("label",{htmlFor:F,css:Bt.label(y,N)},p,(0,n.tZ)(k.Z,{when:M},(0,n.tZ)("button",{type:"button",onClick:function t(){L===null||L===void 0?void 0:L()},css:Bt.aiButton},(0,n.tZ)(d.Z,{name:"magicAiColorize",width:32,height:32})))),x&&!N&&(0,n.tZ)(Wt.Z,{content:x,placement:"top",allowHTML:true},(0,n.tZ)(d.Z,{name:"info",width:20,height:20}))),C?(0,n.tZ)(Wt.Z,{placement:"right",hideOnClick:false,content:C.maxLimit-C.inputCharacter>=0?C.maxLimit-C.inputCharacter:(0,a.__)("Limit exceeded","tutor")},z):z),((r=i.error)===null||r===void 0?void 0:r.message)&&(0,n.tZ)("p",{css:Bt.errorLabel(!!i.error,y)},(0,n.tZ)(d.Z,{style:Bt.alertIcon,name:"info",width:20,height:20})," ",i.error.message))};const Dt=It;var Tt=true?{name:"jab4lt",styles:"justify-content:end"}:0;var Mt=true?{name:"1oqqdjf",styles:"border-color:transparent"}:0;var Jt=true?{name:"ilexii",styles:"border-radius:0;border:none;box-shadow:none"}:0;var Nt=true?{name:"eivff4",styles:"display:none"}:0;var Ft=true?{name:"o9ww1u",styles:"opacity:0.5"}:0;var Bt={container:function t(e){var r=e.disabled,o=e.isHidden;return(0,n.iv)("display:flex;flex-direction:column;position:relative;background:inherit;width:100%;",r&&Ft," ",o&&Nt,";"+(true?"":0),true?"":0)},inputContainer:function t(e){return(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[4],";width:100%;",e&&(0,n.iv)("flex-direction:row;align-items:center;justify-content:space-between;gap:",h.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},input:function t(e){return(0,n.iv)("&.tutor-input-field{",y.c.body("regular"),";width:100%;border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[8]," ",h.W0[16],";color:",h.Jv.text.title,";appearance:textfield;",e.hasFieldError&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0)," ",e.readOnly&&(0,n.iv)("border-color:",h.Jv.background.disable,";background-color:",h.Jv.background.disable,";"+(true?"":0),true?"":0),";&:not(textarea){height:40px;}",e.hasHelpText&&(0,n.iv)("padding:0 ",h.W0[32]," 0 ",h.W0[12],";"+(true?"":0),true?"":0)," ",e.removeBorder&&Jt," ",e.isSecondary&&Mt," :focus{",Pt.i.inputFocus,";",e.isMagicAi&&(0,n.iv)("outline-color:",h.Jv.stroke.magicAi,";background-color:",h.Jv.background.magicAi[8],";"+(true?"":0),true?"":0)," ",e.hasFieldError&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}::-webkit-outer-spin-button,::-webkit-inner-spin-button{-webkit-appearance:none;margin:0;}::placeholder{",y.c.caption("regular"),";color:",h.Jv.text.hints,";",e.isSecondary&&(0,n.iv)("color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),";}}"+(true?"":0),true?"":0)},errorLabel:function t(e,r){return(0,n.iv)(y.c.small(),";line-height:",h.Nv[20],";display:flex;align-items:start;margin-top:",h.W0[4],";",r&&Tt," ",e&&(0,n.iv)("color:",h.Jv.text.status.onHold,";"+(true?"":0),true?"":0)," & svg{margin-right:",h.W0[2],";transform:rotate(180deg);}"+(true?"":0),true?"":0)},labelContainer:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";>div{display:flex;color:",h.Jv.color.black[30],";}"+(true?"":0),true?"":0),label:function t(e,r){return(0,n.iv)(y.c.caption(),";margin:0px;width:",r?"100%":"auto",";color:",h.Jv.text.title,";display:flex;align-items:center;gap:",h.W0[4],";",e&&(0,n.iv)(y.c.caption(),";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},aiButton:(0,n.iv)(Pt.i.resetButton,";width:32px;height:32px;border-radius:",h.E0[4],";display:flex;align-items:center;justify-content:center;:disabled{cursor:not-allowed;}&:focus,&:active,&:hover{background:none;}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";}"+(true?"":0),true?"":0),inputWrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,loader:(0,n.iv)("position:absolute;top:50%;right:",h.W0[12],";transform:translateY(-50%);display:flex;"+(true?"":0),true?"":0),alertIcon:true?{name:"ozd7xs",styles:"flex-shrink:0"}:0};var zt=r(5114);var Rt=["className","variant","size","children","type","disabled","roundedFull","loading"];function Ut(){Ut=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ut.apply(this,arguments)}function Gt(t,e){if(t==null)return{};var r=Qt(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function Qt(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function Yt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var qt=l().forwardRef((function(t,e){var r=t.className,o=t.variant,i=t.size,a=t.children,u=t.type,l=u===void 0?"button":u,c=t.disabled,s=c===void 0?false:c,d=t.roundedFull,f=d===void 0?true:d,p=t.loading,v=Gt(t,Rt);return(0,n.tZ)("button",Ut({type:l,ref:e,css:$t({variant:o,size:i,rounded:f?"true":"false"}),className:r,disabled:s},v),(0,n.tZ)("span",{css:Vt.buttonSpan},p?(0,n.tZ)(O.ZP,{size:24}):a))}));const Ht=qt;var Vt={buttonSpan:(0,n.iv)(Pt.i.flexCenter(),";z-index:",h.W5.positive,";"+(true?"":0),true?"":0),base:(0,n.iv)(Pt.i.resetButton,";",y.c.small("medium"),";display:flex;gap:",h.W0[4],";width:100%;justify-content:center;align-items:center;white-space:nowrap;position:relative;overflow:hidden;transition:box-shadow 0.5s ease;&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{cursor:not-allowed;background:",h.Jv.action.primary.disable,";pointer-events:none;color:",h.Jv.text.disable,";border-color:",h.Jv.stroke.disable,";}"+(true?"":0),true?"":0),default:function t(e){return(0,n.iv)("background:",!e?h.Jv.ai.gradient_1:h.Jv.ai.gradient_1_rtl,";color:",h.Jv.text.white,";&::before{content:'';position:absolute;inset:0;background:",!e?h.Jv.ai.gradient_2:h.Jv.ai.gradient_2_rtl,";opacity:0;transition:opacity 0.5s ease;}&:hover::before{opacity:1;}"+(true?"":0),true?"":0)},secondary:(0,n.iv)("background-color:",h.Jv.action.secondary["default"],";color:",h.Jv.text.brand,";border-radius:",h.E0[6],";&:hover{background-color:",h.Jv.action.secondary.hover,";}"+(true?"":0),true?"":0),outline:(0,n.iv)("position:relative;&::before{content:'';position:absolute;inset:0;background:",h.Jv.ai.gradient_1,";color:",h.Jv.text.primary,";border:1px solid transparent;-webkit-mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;}&:hover{&::before{background:",h.Jv.ai.gradient_2,";}}"+(true?"":0),true?"":0),primaryOutline:(0,n.iv)("border:1px solid ",h.Jv.brand.blue,";color:",h.Jv.brand.blue,";&:hover{background-color:",h.Jv.brand.blue,";color:",h.Jv.text.white,";}"+(true?"":0),true?"":0),primary:(0,n.iv)("background-color:",h.Jv.brand.blue,";color:",h.Jv.text.white,";"+(true?"":0),true?"":0),ghost:(0,n.iv)("background-color:transparent;color:",h.Jv.text.subdued,";border-radius:",h.E0[4],";&:hover{color:",h.Jv.text.primary,";}"+(true?"":0),true?"":0),plain:(0,n.iv)("span{background:",!S.dZ?h.Jv.text.ai.gradient:h.Jv.ai.gradient_1_rtl,";background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;&:hover{background:",!S.dZ?h.Jv.ai.gradient_2:h.Jv.ai.gradient_2_rtl,";background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;}}"+(true?"":0),true?"":0),size:{default:(0,n.iv)("height:32px;padding-inline:",h.W0[12],";padding-block:",h.W0[4],";"+(true?"":0),true?"":0),sm:(0,n.iv)("height:24px;padding-inline:",h.W0[10],";"+(true?"":0),true?"":0),icon:true?{name:"68x97p",styles:"width:32px;height:32px"}:0},rounded:{true:(0,n.iv)("border-radius:",h.E0[54],";&::before{border-radius:",h.E0[54],";}"+(true?"":0),true?"":0),false:(0,n.iv)("border-radius:",h.E0[4],";&::before{border-radius:",h.E0[4],";}"+(true?"":0),true?"":0)}};var $t=(0,zt.Y)({variants:{variant:{default:Vt["default"](S.dZ),primary:Vt.primary,secondary:Vt.secondary,outline:Vt.outline,primary_outline:Vt.primaryOutline,ghost:Vt.ghost,plain:Vt.plain},size:{default:Vt.size["default"],sm:Vt.size.sm,icon:Vt.size.icon},rounded:{true:Vt.rounded["true"],false:Vt.rounded["false"]}},defaultVariants:{variant:"default",size:"default",rounded:"true"}},Vt.base);var Kt=r(5404);var Xt=r(7363);function te(){te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return te.apply(this,arguments)}function ee(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var re=6;var ne=function t(e){var r;var o=e.label,i=e.rows,a=i===void 0?re:i,l=e.columns,c=e.maxLimit,s=e.field,d=e.fieldState,f=e.disabled,p=e.readOnly,v=e.loading,h=e.placeholder,y=e.helpText,b=e.onChange,g=e.onKeyDown,m=e.isHidden,w=e.enableResize,x=w===void 0?true:w,_=e.isSecondary,Z=_===void 0?false:_,O=e.isMagicAi,S=O===void 0?false:O,k=e.inputCss,j=e.maxHeight,E=e.autoResize,C=E===void 0?false:E;var A=(r=s.value)!==null&&r!==void 0?r:"";var P=(0,u.useRef)(null);var W=undefined;if(c){W={maxLimit:c,inputCharacter:A.toString().length}}var L=function t(){if(P.current){if(j){P.current.style.maxHeight="".concat(j,"px")}P.current.style.height="auto";P.current.style.height="".concat(P.current.scrollHeight,"px")}};(0,u.useLayoutEffect)((function(){if(C){L()}}),[]);return(0,n.tZ)(Dt,{label:o,field:s,fieldState:d,disabled:f,readOnly:p,loading:v,placeholder:h,helpText:y,isHidden:m,characterCount:W,isSecondary:Z,isMagicAi:S},(function(t){return(0,n.tZ)(Xt.Fragment,null,(0,n.tZ)("div",{css:ae.container(x,k)},(0,n.tZ)("textarea",te({},s,t,{ref:function t(e){s.ref(e);P.current=e},style:{maxHeight:j?"".concat(j,"px"):"none"},className:"tutor-input-field",value:A,onChange:function t(e){var r=e.target.value;if(c&&r.trim().length>c){return}s.onChange(r);if(b){b(r)}if(C){L()}},onKeyDown:function t(e){g===null||g===void 0?void 0:g(e.key)},autoComplete:"off",rows:a,cols:l}))))}))};const oe=F(ne);var ie=true?{name:"1dz94pb",styles:"resize:vertical"}:0;var ae={container:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var r=arguments.length>1?arguments[1]:undefined;return(0,n.iv)("position:relative;display:flex;textarea{",y.c.body(),";height:auto;padding:",h.W0[8]," ",h.W0[12],";resize:none;",e&&ie,";&.tutor-input-field{",r,";}}"+(true?"":0),true?"":0)}};var ue=function t(e){var r=e.each,n=e.children,o=e.fallback,i=o===void 0?null:o;if(r.length===0){return i}return r.map((function(t,e){return n(t,e)}))};const le=ue;var ce=function t(e){var r=e.options,o=e.onChange;return(0,n.tZ)("div",{css:se.wrapper},(0,n.tZ)(le,{each:r},(function(t,e){return(0,n.tZ)("button",{type:"button",key:e,onClick:function e(){return o(t.value)},css:se.item},t.label)})))};var se={wrapper:(0,n.iv)("display:flex;flex-direction:column;padding-block:",h.W0[8],";max-height:400px;overflow-y:auto;"+(true?"":0),true?"":0),item:(0,n.iv)(Pt.i.resetButton,";",y.c.caption(),";width:100%;padding:",h.W0[4]," ",h.W0[16],";color:",h.Jv.text.subdued,";display:flex;align-items:center;&:hover{background-color:",h.Jv.background.hover,";color:",h.Jv.text.title,";}"+(true?"":0),true?"":0)};function de(t,e){return ye(t)||he(t,e)||pe(t,e)||fe()}function fe(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function pe(t,e){if(!t)return;if(typeof t==="string")return ve(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ve(t,e)}function ve(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function he(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ye(t){if(Array.isArray(t))return t}var be=function t(e){var r=e.options,n=e.isOpen,o=e.onSelect,i=e.onClose,a=e.selectedValue;var l=(0,u.useState)(-1),c=de(l,2),s=c[0],d=c[1];var f=(0,u.useCallback)((function(t){if(!n)return;var e=function t(e,n){var o;var i=e;var a=n==="down"?1:-1;do{i+=a;if(i<0)i=r.length-1;if(i>=r.length)i=0}while(i>=0&&i<r.length&&r[i].disabled);if((o=r[i])!==null&&o!==void 0&&o.disabled){return e}return i};switch(t.key){case"ArrowDown":t.preventDefault();d((function(t){var r=e(t===-1?0:t,"down");return r}));break;case"ArrowUp":t.preventDefault();d((function(t){var r=e(t===-1?0:t,"up");return r}));break;case"Enter":t.preventDefault();t.stopPropagation();if(s>=0&&s<r.length){var a=r[s];if(!a.disabled){i();o(a)}}break;case"Escape":t.preventDefault();t.stopPropagation();i();break;default:break}}),[n,r,s,o,i]);(0,u.useEffect)((function(){if(n){if(s===-1){var t=r.findIndex((function(t){return t.value===a}));var e=t>=0?t:r.findIndex((function(t){return!t.disabled}));d(e)}document.addEventListener("keydown",f,true);return function(){return document.removeEventListener("keydown",f,true)}}}),[n,f,r,a,s]);(0,u.useEffect)((function(){if(!n){d(-1)}}),[n]);var p=(0,u.useCallback)((function(t){var e;if(!((e=r[t])!==null&&e!==void 0&&e.disabled)){d(t)}}),[r]);return{activeIndex:s,setActiveIndex:p}};function ge(t){"@babel/helpers - typeof";return ge="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ge(t)}var me=["css"];function we(){we=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return we.apply(this,arguments)}function xe(t,e){if(t==null)return{};var r=_e(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function _e(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function Ze(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Oe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ze(Object(r),!0).forEach((function(e){Se(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ze(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Se(t,e,r){e=ke(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ke(t){var e=je(t,"string");return ge(e)==="symbol"?e:String(e)}function je(t,e){if(ge(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ge(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ee(t,e){return Le(t)||We(t,e)||Ae(t,e)||Ce()}function Ce(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ae(t,e){if(!t)return;if(typeof t==="string")return Pe(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pe(t,e)}function Pe(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function We(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Le(t){if(Array.isArray(t))return t}function Ie(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var De=true?{name:"1d3w5wq",styles:"width:100%"}:0;var Te=function t(e){var r;var o=e.options,i=e.field,l=e.fieldState,c=e.onChange,s=c===void 0?b.ZT:c,f=e.label,p=e.placeholder,h=p===void 0?"":p,y=e.disabled,g=e.readOnly,m=e.loading,w=e.isSearchable,x=w===void 0?false:w,_=e.isInlineLabel,Z=e.hideCaret,O=e.listLabel,j=e.isClearable,C=j===void 0?false:j,A=e.helpText,P=e.removeOptionsMinWidth,W=P===void 0?false:P,L=e.leftIcon,I=e.removeBorder,D=e.dataAttribute,T=e.isSecondary,M=T===void 0?false:T,J=e.isMagicAi,N=J===void 0?false:J,F=e.isAiOutline,B=F===void 0?false:F,z=e.selectOnFocus;var R=(0,u.useCallback)((function(){return o.find((function(t){return t.value===i.value}))||{label:"",value:"",description:""}}),[i.value,o]);var U=(0,u.useMemo)((function(){return o.some((function(t){return(0,E.$K)(t.description)}))}),[o]);var G=(0,u.useState)((r=R())===null||r===void 0?void 0:r.label),Q=Ee(G,2),Y=Q[0],q=Q[1];var H=(0,u.useState)(false),V=Ee(H,2),$=V[0],K=V[1];var X=(0,u.useState)(""),tt=Ee(X,2),et=tt[0],rt=tt[1];var nt=(0,u.useState)(false),ot=Ee(nt,2),it=ot[0],at=ot[1];var ut=(0,u.useRef)(null);var lt=(0,u.useRef)(null);var ct=(0,u.useRef)(null);var st=(0,u.useMemo)((function(){if(x){return o.filter((function(t){var e=t.label;return e.toLowerCase().includes(et.toLowerCase())}))}return o}),[et,x,o]);var dt=(0,u.useMemo)((function(){return o.find((function(t){return t.value===i.value}))}),[i.value,o]);var ft=(0,xt.l)({isOpen:it,isDropdown:true,dependencies:[st.length]}),pt=ft.triggerRef,vt=ft.triggerWidth,ht=ft.position,yt=ft.popoverRef;var bt=Oe({},(0,E.$K)(D)&&Se({},D,true));(0,u.useEffect)((function(){var t;q((t=R())===null||t===void 0?void 0:t.label)}),[i.value,R]);(0,u.useEffect)((function(){if(it){var t;q((t=R())===null||t===void 0?void 0:t.label)}}),[R,it]);var gt=function t(e,r){r===null||r===void 0?void 0:r.stopPropagation();if(!e.disabled){i.onChange(e.value);s(e);rt("");K(false);at(false)}};var mt=be({options:st,isOpen:it,selectedValue:i.value,onSelect:gt,onClose:function t(){at(false);K(false);rt("")}}),wt=mt.activeIndex,_t=mt.setActiveIndex;(0,u.useEffect)((function(){if(it&&wt>=0&&ct.current){ct.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[it,wt]);return(0,n.tZ)(Dt,{fieldState:l,field:i,label:f,disabled:y||o.length===0,readOnly:g,loading:m,isInlineLabel:_,helpText:A,removeBorder:I,isSecondary:M,isMagicAi:N},(function(t){var e,r,u;var c=t.css,s=xe(t,me);return(0,n.tZ)("div",{css:Be.mainWrapper},(0,n.tZ)("div",{css:Be.inputWrapper(B),ref:pt},(0,n.tZ)("div",{css:Be.leftIcon},(0,n.tZ)(k.Z,{when:L},L),(0,n.tZ)(k.Z,{when:dt===null||dt===void 0?void 0:dt.icon},(function(t){return(0,n.tZ)(d.Z,{name:t,width:32,height:32})}))),(0,n.tZ)("div",{css:De},(0,n.tZ)("input",we({},s,bt,{ref:function t(e){i.ref(e);ut.current=e},className:"tutor-input-field",css:[c,Be.input({hasLeftIcon:!!L||!!(dt!==null&&dt!==void 0&&dt.icon),hasDescription:U,hasError:!!l.error,isMagicAi:N,isAiOutline:B}),true?"":0,true?"":0],autoComplete:"off",readOnly:g||!x,placeholder:h,value:$?et:Y,title:Y,onClick:function t(e){var r;e.stopPropagation();at((function(t){return!t}));(r=ut.current)===null||r===void 0?void 0:r.focus()},onKeyDown:function t(e){if(e.key==="Enter"){var r;e.preventDefault();at((function(t){return!t}));(r=ut.current)===null||r===void 0?void 0:r.focus()}if(e.key==="Tab"){at(false)}},onFocus:z&&x?function(t){t.target.select()}:undefined,onChange:function t(e){q(e.target.value);if(x){K(true);rt(e.target.value)}},"data-select":true})),(0,n.tZ)(k.Z,{when:U},(0,n.tZ)("span",{css:Be.description({hasLeftIcon:!!L}),title:(e=R())===null||e===void 0?void 0:e.description},(r=R())===null||r===void 0?void 0:r.description))),!Z&&!m&&(0,n.tZ)("button",{tabIndex:-1,type:"button",css:Be.caretButton({isOpen:it}),onClick:function t(){var e;at((function(t){return!t}));(e=ut.current)===null||e===void 0?void 0:e.focus()},disabled:y||g||o.length===0},(0,n.tZ)(d.Z,{name:"chevronDown",width:20,height:20}))),(0,n.tZ)(xt.h,{isOpen:it,onClickOutside:function t(){at(false);K(false);rt("")},onEscape:function t(){at(false);K(false);rt("")}},(0,n.tZ)("div",{css:[Be.optionsWrapper,(u={},Se(u,S.dZ?"right":"left",ht.left),Se(u,"top",ht.top),Se(u,"maxWidth",vt),u),true?"":0,true?"":0],ref:yt},(0,n.tZ)("ul",{css:[Be.options(W),true?"":0,true?"":0]},!!O&&(0,n.tZ)("li",{css:Be.listLabel},O),(0,n.tZ)(k.Z,{when:st.length>0,fallback:(0,n.tZ)("li",{css:Be.emptyState},(0,a.__)("No options available","tutor"))},st.map((function(t,e){return(0,n.tZ)("li",{key:String(t.value),ref:t.value===i.value?lt:wt===e?ct:null,css:Be.optionItem({isSelected:t.value===i.value,isActive:e===wt,isDisabled:!!t.disabled})},(0,n.tZ)("button",{type:"button",css:Be.label,onClick:function e(r){if(!t.disabled){gt(t,r)}},disabled:t.disabled,title:t.label,onMouseOver:function t(){return _t(e)},onMouseLeave:function t(){return e!==wt&&_t(-1)},onFocus:function t(){return _t(e)},"aria-selected":wt===e},(0,n.tZ)(k.Z,{when:t.icon},(0,n.tZ)(d.Z,{name:t.icon,width:32,height:32})),(0,n.tZ)("span",null,t.label)))}))),C&&(0,n.tZ)("div",{css:Be.clearButton({isDisabled:Y===""})},(0,n.tZ)(v.Z,{variant:"text",disabled:Y==="",icon:(0,n.tZ)(d.Z,{name:"delete"}),onClick:function t(){i.onChange(null);q("");rt("");at(false)}},(0,a.__)("Clear","tutor")))))))}))};const Me=Te;var Je=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var Ne=true?{name:"16gsvie",styles:"min-width:200px"}:0;var Fe=true?{name:"kqjaov",styles:"position:relative;border:none;background:transparent"}:0;var Be={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)("width:100%;display:flex;justify-content:space-between;align-items:center;position:relative;",e&&(0,n.iv)("&::before{content:'';position:absolute;inset:0;background:",h.Jv.ai.gradient_1,";color:",h.Jv.text.primary,";border:1px solid transparent;-webkit-mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;border-radius:6px;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},leftIcon:(0,n.iv)("position:absolute;left:",h.W0[8],";",Pt.i.display.flex(),";align-items:center;height:100%;color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),input:function t(e){var r=e.hasLeftIcon,o=e.hasDescription,i=e.hasError,a=i===void 0?false:i,u=e.isMagicAi,l=u===void 0?false:u,c=e.isAiOutline,s=c===void 0?false:c;return(0,n.iv)("&[data-select]{",y.c.body(),";width:100%;cursor:pointer;padding-right:",h.W0[32],";",Pt.i.textEllipsis,";background-color:transparent;background-color:",h.Jv.background.white,";",r&&(0,n.iv)("padding-left:",h.W0[48],";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("&.tutor-input-field{height:56px;padding-bottom:",h.W0[24],";}"+(true?"":0),true?"":0)," ",a&&(0,n.iv)("background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0)," ",s&&Fe," :focus{",Pt.i.inputFocus,";",l&&(0,n.iv)("outline-color:",h.Jv.stroke.magicAi,";background-color:",h.Jv.background.magicAi[8],";"+(true?"":0),true?"":0)," ",a&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";}}"+(true?"":0),true?"":0)},description:function t(e){var r=e.hasLeftIcon;return(0,n.iv)(y.c.small(),";",Pt.i.text.ellipsis(1)," color:",h.Jv.text.hints,";position:absolute;bottom:",h.W0[8],";padding-inline:calc(",h.W0[16]," + 1px) ",h.W0[32],";",r&&(0,n.iv)("padding-left:calc(",h.W0[48]," + 1px);"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},listLabel:(0,n.iv)(y.c.body(),";color:",h.Jv.text.subdued,";min-height:40px;display:flex;align-items:center;padding-left:",h.W0[16],";"+(true?"":0),true?"":0),clearButton:function t(e){var r=e.isDisabled,o=r===void 0?false:r;return(0,n.iv)("padding:",h.W0[4]," ",h.W0[8],";border-top:1px solid ",h.Jv.stroke["default"],";&>button{padding:0;width:100%;font-size:",h.JB[12],";",!o&&(0,n.iv)("color:",h.Jv.text.title,";&:hover{text-decoration:underline;}"+(true?"":0),true?"":0),";>span{justify-content:center;}}"+(true?"":0),true?"":0)},optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:function t(e){return(0,n.iv)("z-index:",h.W5.dropdown,";background-color:",h.Jv.background.white,";list-style-type:none;box-shadow:",h.AF.popover,";padding:",h.W0[4]," 0;margin:0;max-height:500px;border-radius:",h.E0[6],";",Pt.i.overflowYAuto,";scrollbar-gutter:auto;",!e&&Ne,";"+(true?"":0),true?"":0)},optionItem:function t(e){var r=e.isSelected,o=r===void 0?false:r,i=e.isActive,a=i===void 0?false:i,u=e.isDisabled,l=u===void 0?false:u;return(0,n.iv)(y.c.body(),";min-height:36px;height:100%;width:100%;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;cursor:",l?"not-allowed":"pointer",";opacity:",l?.5:1,";",a&&(0,n.iv)("background-color:",h.Jv.background.hover,";"+(true?"":0),true?"":0)," &:hover{background-color:",!l&&h.Jv.background.hover,";}",!l&&o&&(0,n.iv)("background-color:",h.Jv.background.active,";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:3px;height:100%;background-color:",h.Jv.action.primary["default"],";border-radius:0 ",h.E0[6]," ",h.E0[6]," 0;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},label:(0,n.iv)(Pt.i.resetButton,";",Pt.i.text.ellipsis(1),";color:",h.Jv.text.title,";width:100%;height:100%;display:flex;align-items:center;gap:",h.W0[8],";margin:0 ",h.W0[12],";padding:",h.W0[6]," 0;text-align:left;line-height:",h.Nv[24],";word-break:break-all;cursor:pointer;&:hover,&:focus,&:active{background-color:transparent;color:",h.Jv.text.title,";}span{flex-shrink:0;",Pt.i.text.ellipsis(1)," width:100%;}"+(true?"":0),true?"":0),arrowUpDown:(0,n.iv)("color:",h.Jv.icon["default"],";display:flex;justify-content:center;align-items:center;margin-top:",h.W0[2],";"+(true?"":0),true?"":0),optionsContainer:true?{name:"1ivsou8",styles:"position:absolute;overflow:hidden auto;min-width:16px;max-width:calc(100% - 32px)"}:0,caretButton:function t(e){var r=e.isOpen,o=r===void 0?false:r;return(0,n.iv)(Pt.i.resetButton,";position:absolute;right:",h.W0[4],";display:flex;align-items:center;transition:transform 0.3s ease-in-out;color:",h.Jv.icon["default"],";border-radius:",h.E0[4],";padding:",h.W0[6],";height:100%;&:focus,&:active,&:hover{background:none;color:",h.Jv.icon["default"],";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";}",o&&Je,";"+(true?"":0),true?"":0)},emptyState:(0,n.iv)(Pt.i.flexCenter(),";padding:",h.W0[8],";"+(true?"":0),true?"":0)};var ze=[{label:"English",value:"english"},{label:"简体中文",value:"simplified-chinese"},{label:"繁體中文",value:"traditional-chinese"},{label:"Español",value:"spanish"},{label:"Français",value:"french"},{label:"日本語",value:"japanese"},{label:"Deutsch",value:"german"},{label:"Português",value:"portuguese"},{label:"العربية",value:"arabic"},{label:"Русский",value:"russian"},{label:"Italiano",value:"italian"},{label:"한국어",value:"korean"},{label:"हिन्दी",value:"hindi"},{label:"Nederlands",value:"dutch"},{label:"Polski",value:"polish"},{label:"አማርኛ",value:"amharic"},{label:"Български",value:"bulgarian"},{label:"বাংলা",value:"bengali"},{label:"Čeština",value:"czech"},{label:"Dansk",value:"danish"},{label:"Ελληνικά",value:"greek"},{label:"Eesti",value:"estonian"},{label:"فارسی",value:"persian"},{label:"Filipino",value:"filipino"},{label:"Hrvatski",value:"croatian"},{label:"Magyar",value:"hungarian"},{label:"Bahasa Indonesia",value:"indonesian"},{label:"Lietuvių",value:"lithuanian"},{label:"Latviešu",value:"latvian"},{label:"Melayu",value:"malay"},{label:"Norsk",value:"norwegian"},{label:"Română",value:"romanian"},{label:"Slovenčina",value:"slovak"},{label:"Slovenščina",value:"slovenian"},{label:"Српски",value:"serbian"},{label:"Svenska",value:"swedish"},{label:"ภาษาไทย",value:"thai"},{label:"Türkçe",value:"turkish"},{label:"Українська",value:"ukrainian"},{label:"اردو",value:"urdu"},{label:"Tiếng Việt",value:"vietnamese"}];var Re=[{label:(0,a.__)("Formal","tutor"),value:"formal"},{label:(0,a.__)("Casual","tutor"),value:"casual"},{label:(0,a.__)("Professional","tutor"),value:"professional"},{label:(0,a.__)("Enthusiastic","tutor"),value:"enthusiastic"},{label:(0,a.__)("Informational","tutor"),value:"informational"},{label:(0,a.__)("Funny","tutor"),value:"funny"}];var Ue=[{label:(0,a.__)("Title","tutor"),value:"title"},{label:(0,a.__)("Essay","tutor"),value:"essay"},{label:(0,a.__)("Paragraph","tutor"),value:"paragraph"},{label:(0,a.__)("Outline","tutor"),value:"outline"}];function Ge(){Ge=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ge.apply(this,arguments)}var Qe=function t(e){var r=e.form;return(0,n.tZ)("div",{css:Ye.wrapper},(0,n.tZ)(p.Qr,{control:r.control,name:"characters",render:function t(e){return(0,n.tZ)(Zo,Ge({},e,{isMagicAi:true,label:(0,a.__)("Character Limit","tutor"),type:"number"}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"language",render:function t(e){return(0,n.tZ)(Me,Ge({},e,{isMagicAi:true,label:(0,a.__)("Language","tutor"),options:ze}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"tone",render:function t(e){return(0,n.tZ)(Me,Ge({},e,{isMagicAi:true,options:Re,label:(0,a.__)("Tone","tutor")}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"format",render:function t(e){return(0,n.tZ)(Me,Ge({},e,{isMagicAi:true,label:(0,a.__)("Format","tutor"),options:Ue}))}}))};var Ye={wrapper:(0,n.iv)("display:grid;grid-template-columns:repeat(2, 1fr);gap:",h.W0[16],";"+(true?"":0),true?"":0)};var qe;function He(t,e){if(!e){e=t.slice(0)}return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function Ve(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var $e=(0,u.forwardRef)((function(t,e){var r=t.width,o=r===void 0?"100%":r,i=t.height,a=i===void 0?16:i,u=t.animation,l=u===void 0?false:u,c=t.isMagicAi,s=c===void 0?false:c,d=t.isRound,f=d===void 0?false:d,p=t.animationDuration,v=p===void 0?1.6:p,h=t.className;return(0,n.tZ)("span",{ref:e,css:er.skeleton(o,a,l,s,f,v),className:h})}));const Ke=$e;var Xe={wave:(0,n.F4)(qe||(qe=He(["\n    0% {\n      transform: translateX(-100%);\n    }\n    50% {\n      transform: translateX(0%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  "])))};var tr=true?{name:"1q4m7z3",styles:"background:linear-gradient(89.17deg, #fef4ff 0.2%, #f9d3ff 50.09%, #fef4ff 96.31%)"}:0;var er={skeleton:function t(e,r,o,i,a,u){return(0,n.iv)("display:block;width:",(0,E.hj)(e)?"".concat(e,"px"):e,";height:",(0,E.hj)(r)?"".concat(r,"px"):r,";border-radius:",h.E0[6],";background-color:",!i?"rgba(0, 0, 0, 0.11)":h.Jv.background.magicAi.skeleton,";position:relative;-webkit-mask-image:-webkit-radial-gradient(center, white, black);overflow:hidden;",a&&(0,n.iv)("border-radius:",h.E0.circle,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)(":after{content:'';background:linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);position:absolute;transform:translateX(-100%);inset:0;",i&&tr," animation:",u,"s linear 0.5s infinite normal none running ",Xe.wave,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var rr=function t(){return(0,n.tZ)("div",{css:or.container},(0,n.tZ)("div",{css:or.wrapper},(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"20%",height:"12px"}),(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"40%",height:"12px"})),(0,n.tZ)("div",{css:or.wrapper},(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"80%",height:"12px"}),(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)(Ke,{animation:true,isMagicAi:true,width:"80%",height:"12px"})))};const nr=rr;var or={wrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),container:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[32],";"+(true?"":0),true?"":0)};var ir=r(8811);function ar(t){"@babel/helpers - typeof";return ar="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ar(t)}function ur(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function lr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ur(Object(r),!0).forEach((function(e){cr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ur(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function cr(t,e,r){e=sr(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function sr(t){var e=dr(t,"string");return ar(e)==="symbol"?e:String(e)}function dr(t,e){if(ar(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ar(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var fr=function t(e){return St.R.post(kt.Z.GENERATE_AI_IMAGE,e)};var pr=function t(){return(0,Zt.D)({mutationFn:fr})};var vr=function t(e){return St.R.post(kt.Z.MAGIC_FILL_AI_IMAGE,e).then((function(t){return t.data.data[0].b64_json}))};var hr=function t(){var e=(0,Ot.p)(),r=e.showToast;return(0,Zt.D)({mutationFn:vr,onError:function t(e){r({type:"danger",message:(0,b.Mo)(e)})}})};var yr=function t(e){return St.R.post(kt.Z.MAGIC_TEXT_GENERATION,e)};var br=function t(){var e=(0,Ot.p)(),r=e.showToast;return(0,Zt.D)({mutationFn:yr,onError:function t(e){r({type:"danger",message:(0,b.Mo)(e)})}})};var gr=function t(e){return St.R.post(kt.Z.MAGIC_AI_MODIFY_CONTENT,e)};var mr=function t(){var e=(0,Ot.p)(),r=e.showToast;return(0,Zt.D)({mutationFn:gr,onError:function t(e){r({type:"danger",message:(0,b.Mo)(e)})}})};var wr=function t(e){return St.R.post(kt.Z.USE_AI_GENERATED_IMAGE,e)};var xr=function t(){var e=(0,Ot.p)(),r=e.showToast;return(0,Zt.D)({mutationFn:wr,onError:function t(e){r({type:"danger",message:(0,b.Mo)(e)})}})};var _r=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_CONTENT,e,{signal:e.signal})};var Zr=function t(e){var r=useToast(),n=r.showToast;return useMutation({mutationKey:["GenerateCourseContent",e],mutationFn:_r,onError:function t(e){n({type:"danger",message:convertToErrorMessage(e)})}})};var Or=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_CONTENT,e,{signal:e.signal})};var Sr=function t(){var e=useToast(),r=e.showToast;return useMutation({mutationFn:Or,onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var kr=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_TOPIC_CONTENT,e,{signal:e.signal})};var jr=function t(){var e=useToast(),r=e.showToast;return useMutation({mutationFn:kr,onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var Er=function t(e){return wpAjaxInstance.post(endpoints.SAVE_AI_GENERATED_COURSE_CONTENT,e)};var Cr=function t(){var e=useToast(),r=e.showToast;var n=useQueryClient();return useMutation({mutationFn:Er,onSuccess:function t(){n.invalidateQueries({queryKey:["CourseDetails"]})},onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var Ar=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_QUIZ_QUESTIONS,e,{signal:e.signal})};var Pr=function t(){var e=useToast(),r=e.showToast;return useMutation({mutationFn:Ar,onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var Wr=function t(e){return St.R.post(kt.Z.OPEN_AI_SAVE_SETTINGS,lr({},e))};var Lr=function t(){var e=(0,Ot.p)(),r=e.showToast;return(0,Zt.D)({mutationFn:Wr,onSuccess:function t(e){r({type:"success",message:e.message})},onError:function t(e){r({type:"danger",message:(0,b.Mo)(e)})}})};var Ir=r(7602);function Dr(t){"@babel/helpers - typeof";return Dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(t)}function Tr(){Tr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Tr.apply(this,arguments)}function Mr(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Mr=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Dr(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Jr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Nr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Jr(Object(r),!0).forEach((function(e){Fr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Jr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Fr(t,e,r){e=Br(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Br(t){var e=zr(t,"string");return Dr(e)==="symbol"?e:String(e)}function zr(t,e){if(Dr(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Dr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Rr(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Ur(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Rr(i,n,o,a,u,"next",t)}function u(t){Rr(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Gr(t){return qr(t)||Yr(t)||$r(t)||Qr()}function Qr(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Yr(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function qr(t){if(Array.isArray(t))return Kr(t)}function Hr(t,e){return tn(t)||Xr(t,e)||$r(t,e)||Vr()}function Vr(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $r(t,e){if(!t)return;if(typeof t==="string")return Kr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Kr(t,e)}function Kr(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Xr(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function tn(t){if(Array.isArray(t))return t}function en(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var rn=[(0,a.__)("Mastering Digital Marketing: A Complete Guide","tutor"),(0,a.__)("The Ultimate Photoshop Course for Beginners","tutor"),(0,a.__)("Python Programming: From Zero to Hero","tutor"),(0,a.__)("Creative Writing Essentials: Unlock Your Storytelling Potential","tutor"),(0,a.__)("The Complete Guide to Web Development with React","tutor"),(0,a.__)("Master Public Speaking: Deliver Powerful Presentations","tutor"),(0,a.__)("Excel for Business: From Basics to Advanced Analytics","tutor"),(0,a.__)("Fitness Fundamentals: Build Strength and Confidence","tutor"),(0,a.__)("Photography Made Simple: Capture Stunning Shots","tutor"),(0,a.__)("Financial Freedom: Learn the Basics of Investing","tutor")];var nn=function t(e){var r=e.title,o=e.icon,i=e.closeModal,l=e.field,c=e.format,s=c===void 0?"essay":c,f=e.characters,y=f===void 0?250:f,g=e.is_html,m=g===void 0?false:g,w=e.fieldLabel,x=w===void 0?"":w,_=e.fieldPlaceholder,Z=_===void 0?"":_;var O=at({defaultValues:{prompt:"",characters:y,language:"english",tone:"formal",format:s}});var j=br();var E=mr();var C=(0,u.useState)([]),A=Hr(C,2),P=A[0],W=A[1];var L=(0,u.useState)(0),I=Hr(L,2),D=I[0],T=I[1];var M=(0,u.useState)(false),J=Hr(M,2),N=J[0],F=J[1];var B=(0,u.useState)(null),z=Hr(B,2),R=z[0],U=z[1];var G=(0,u.useRef)(null);var Q=(0,u.useRef)(null);var Y=(0,u.useMemo)((function(){return P[D]}),[P,D]);var q=O.watch("prompt");function H(t){W((function(e){return[t].concat(Gr(e))}));T(0)}function V(t,e){return $.apply(this,arguments)}function $(){$=Ur(Mr().mark((function t(e,r){var n,o,i,a;return Mr().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:if(!(P.length===0)){u.next=2;break}return u.abrupt("return");case 2:n=P[D];if(!(e==="translation"&&!!r)){u.next=9;break}u.next=6;return E.mutateAsync({type:"translation",content:n,language:r,is_html:m});case 6:o=u.sent;if(o.data){H(o.data)}return u.abrupt("return");case 9:if(!(e==="change_tone"&&!!r)){u.next=15;break}u.next=12;return E.mutateAsync({type:"change_tone",content:n,tone:r,is_html:m});case 12:i=u.sent;if(i.data){H(i.data)}return u.abrupt("return");case 15:u.next=17;return E.mutateAsync({type:e,content:n,is_html:m});case 17:a=u.sent;if(a.data){H(a.data)}case 19:case"end":return u.stop()}}),t)})));return $.apply(this,arguments)}(0,u.useEffect)((function(){O.setFocus("prompt")}),[]);return(0,n.tZ)(Ir.Z,{onClose:i,title:r,icon:o,maxWidth:524},(0,n.tZ)("form",{onSubmit:O.handleSubmit(function(){var t=Ur(Mr().mark((function t(e){var r;return Mr().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:n.next=2;return j.mutateAsync(Nr(Nr({},e),{},{is_html:m}));case 2:r=n.sent;if(r.data){H(r.data)}case 4:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},(0,n.tZ)("div",{css:an.container},(0,n.tZ)("div",{css:an.fieldsWrapper},(0,n.tZ)(p.Qr,{control:O.control,name:"prompt",render:function t(e){return(0,n.tZ)(oe,Tr({},e,{label:x||(0,a.__)("Craft Your Course Description","tutor"),placeholder:Z||(0,a.__)("Provide a brief overview of your course topic, target audience, and key takeaways","tutor"),rows:4,isMagicAi:true}))}}),(0,n.tZ)("button",{type:"button",css:an.inspireButton,onClick:function t(){var e=rn.length;var r=Math.floor(Math.random()*e);O.reset(Nr(Nr({},O.getValues()),{},{prompt:rn[r]}))}},(0,n.tZ)(d.Z,{name:"bulbLine"}),(0,a.__)("Inspire Me","tutor"))),(0,n.tZ)(k.Z,{when:!j.isPending&&!E.isPending,fallback:(0,n.tZ)(nr,null)},(0,n.tZ)(k.Z,{when:P.length>0,fallback:(0,n.tZ)(Qe,{form:O})},(0,n.tZ)("div",null,(0,n.tZ)("div",{css:an.actionBar},(0,n.tZ)("div",{css:an.navigation},(0,n.tZ)(k.Z,{when:P.length>1},(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){return T((function(t){return Math.max(0,t-1)}))},disabled:D===0},(0,n.tZ)(d.Z,{name:!S.dZ?"chevronLeft":"chevronRight",width:20,height:20})),(0,n.tZ)("div",{css:an.pageInfo},(0,n.tZ)("span",null,D+1)," / ",P.length),(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){return T((function(t){return Math.min(P.length-1,t+1)}))},disabled:D===P.length-1},(0,n.tZ)(d.Z,{name:!S.dZ?"chevronRight":"chevronLeft",width:20,height:20})))),(0,n.tZ)(v.Z,{variant:"text",onClick:Ur(Mr().mark((function t(){var e;return Mr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:if(!(P.length===0)){r.next=2;break}return r.abrupt("return");case 2:e=P[D];r.next=5;return(0,b.vQ)(e);case 5:F(true);setTimeout((function(){F(false)}),1500);case 7:case"end":return r.stop()}}),t)})))},(0,n.tZ)(k.Z,{when:N,fallback:(0,n.tZ)(d.Z,{name:"copy",width:20,height:20})},(0,n.tZ)(d.Z,{name:"checkFilled",width:20,height:20,style:(0,n.iv)("color:",h.Jv.text.success,"!important;"+(true?"":0),true?"":0)})))),(0,n.tZ)("div",{css:an.content,dangerouslySetInnerHTML:{__html:Y}})),(0,n.tZ)("div",{css:an.otherActions},(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,onClick:function t(){return V("rephrase")}},(0,a.__)("Rephrase","tutor")),(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,onClick:function t(){return V("make_shorter")}},(0,a.__)("Make Shorter","tutor")),(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,ref:G,onClick:function t(){return U("tone")}},(0,a.__)("Change Tone","tutor"),(0,n.tZ)(d.Z,{name:"chevronDown",width:16,height:16})),(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,ref:Q,onClick:function t(){return U("translate")}},(0,a.__)("Translate to","tutor"),(0,n.tZ)(d.Z,{name:"chevronDown",width:16,height:16})),(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,onClick:function t(){return V("write_as_bullets")}},(0,a.__)("Write as Bullets","tutor")),(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,onClick:function t(){return V("make_longer")}},(0,a.__)("Make Longer","tutor")),(0,n.tZ)(Ht,{variant:"outline",roundedFull:false,onClick:function t(){return V("simplify_language")}},(0,a.__)("Simplify Language","tutor")))))),(0,n.tZ)(Kt.Z,{isOpen:R==="tone",triggerRef:G,closePopover:function t(){return U(null)},maxWidth:"160px",animationType:ir.ru.slideUp},(0,n.tZ)(ce,{options:Re,onChange:function(){var t=Ur(Mr().mark((function t(e){return Mr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:U(null);r.next=3;return V("change_tone",e);case 3:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,n.tZ)(Kt.Z,{isOpen:R==="translate",triggerRef:Q,closePopover:function t(){return U(null)},maxWidth:"160px",animationType:ir.ru.slideUp},(0,n.tZ)(ce,{options:ze,onChange:function(){var t=Ur(Mr().mark((function t(e){return Mr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:U(null);r.next=3;return V("translation",e);case 3:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,n.tZ)("div",{css:an.footer},(0,n.tZ)(k.Z,{when:P.length>0,fallback:(0,n.tZ)(Ht,{type:"submit",disabled:j.isPending||!q||E.isPending},(0,n.tZ)(d.Z,{name:"magicWand",width:24,height:24}),(0,a.__)("Generate Now","tutor"))},(0,n.tZ)(Ht,{variant:"outline",type:"submit",disabled:j.isPending||!q||E.isPending},(0,a.__)("Generate Again","tutor")),(0,n.tZ)(Ht,{variant:"primary",disabled:j.isPending||P.length===0||E.isPending,onClick:function t(){l.onChange(P[D]);i()}},(0,a.__)("Use This","tutor"))))))};const on=nn;var an={container:(0,n.iv)("padding:",h.W0[20],";display:flex;flex-direction:column;gap:",h.W0[16],";"+(true?"":0),true?"":0),fieldsWrapper:(0,n.iv)("position:relative;textarea{padding-bottom:",h.W0[40],"!important;}"+(true?"":0),true?"":0),footer:(0,n.iv)("padding:",h.W0[12]," ",h.W0[16],";display:flex;align-items:center;justify-content:end;gap:",h.W0[10],";box-shadow:0px 1px 0px 0px #e4e5e7 inset;button{width:fit-content;}"+(true?"":0),true?"":0),pageInfo:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.hints,";&>span{font-weight:",h.Ue.medium,";color:",h.Jv.text.primary,";}"+(true?"":0),true?"":0),inspireButton:(0,n.iv)(Pt.i.resetButton,";",y.c.small(),";position:absolute;height:28px;bottom:",h.W0[12],";left:",h.W0[12],";border:1px solid ",h.Jv.stroke.brand,";border-radius:",h.E0[4],";display:flex;align-items:center;gap:",h.W0[4],";color:",h.Jv.text.brand,";padding-inline:",h.W0[12],";background-color:",h.Jv.background.white,";&:hover{background-color:",h.Jv.background.brand,";color:",h.Jv.text.white,";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{background-color:",h.Jv.background.disable,";color:",h.Jv.text.disable,";}"+(true?"":0),true?"":0),navigation:(0,n.iv)("margin-left:-",h.W0[8],";display:flex;align-items:center;"+(true?"":0),true?"":0),content:(0,n.iv)(y.c.caption(),";height:180px;overflow-y:auto;background-color:",h.Jv.background.magicAi["default"],";border-radius:",h.E0[6],";padding:",h.W0[6]," ",h.W0[12],";color:",h.Jv.text.magicAi,";"+(true?"":0),true?"":0),actionBar:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,otherActions:(0,n.iv)("display:flex;gap:",h.W0[10],";flex-wrap:wrap;&>button{width:fit-content;}"+(true?"":0),true?"":0)};var un=r(7145);var ln=r(7363);var cn={title:(0,n.tZ)(ln.Fragment,null,(0,a.__)("Upgrade to Tutor LMS Pro today and experience the power of ","tutor"),(0,n.tZ)("span",{css:Pt.i.aiGradientText},(0,a.__)("AI Studio","tutor"))),message:(0,a.__)("Upgrade your plan to access the AI feature","tutor"),featuresTitle:(0,a.__)("Don’t miss out on this game-changing feature!","tutor"),features:[(0,a.__)("Generate a complete course outline in seconds!","tutor"),(0,a.__)("Let the AI Studio create Quizzes on your behalf and give your brain a well-deserved break.","tutor"),(0,a.__)("Generate images, customize backgrounds, and even remove unwanted objects with ease.","tutor"),(0,a.__)("Say goodbye to typos and grammar errors with AI-powered copy editing.","tutor")],footer:(0,n.tZ)(v.Z,{onClick:function t(){return window.open(j.Z.TUTOR_PRICING_PAGE,"_blank","noopener")},icon:(0,n.tZ)(d.Z,{name:"crown",width:24,height:24})},(0,a.__)("Get Tutor LMS Pro","tutor"))};var sn=function t(e){var r=e.title,o=r===void 0?cn.title:r,i=e.message,u=i===void 0?cn.message:i,l=e.featuresTitle,c=l===void 0?cn.featuresTitle:l,s=e.features,f=s===void 0?cn.features:s,p=e.closeModal,v=e.image,h=e.image2x,y=e.footer,b=y===void 0?cn.footer:y;return(0,n.tZ)(Ir.Z,{onClose:p,entireHeader:(0,n.tZ)("span",{css:fn.message},u),maxWidth:496},(0,n.tZ)("div",{css:fn.wrapper},(0,n.tZ)(k.Z,{when:o},(0,n.tZ)("h4",{css:fn.title},o)),(0,n.tZ)(k.Z,{when:v},(0,n.tZ)("img",{css:fn.image,src:v,alt:typeof o==="string"?o:(0,a.__)("Illustration"),srcSet:h?"".concat(v," ").concat(h," 2x"):undefined})),(0,n.tZ)(k.Z,{when:c},(0,n.tZ)("h6",{css:fn.featuresTiTle},c)),(0,n.tZ)(k.Z,{when:f.length},(0,n.tZ)("div",{css:fn.features},(0,n.tZ)(le,{each:f},(function(t,e){return(0,n.tZ)("div",{key:e,css:fn.feature},(0,n.tZ)(d.Z,{name:"materialCheck",width:20,height:20,style:fn.checkIcon}),(0,n.tZ)("span",null,t))})))),(0,n.tZ)(k.Z,{when:b},b)))};const dn=sn;var fn={wrapper:(0,n.iv)("padding:0 ",h.W0[24]," ",h.W0[32]," ",h.W0[24],";",Pt.i.display.flex("column"),";gap:",h.W0[16],";"+(true?"":0),true?"":0),message:(0,n.iv)(y.c.small(),";color:",h.Jv.text.subdued,";padding-left:",h.W0[8],";padding-top:",h.W0[24],";padding-bottom:",h.W0[4],";"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.heading6("medium"),";color:",h.Jv.text.primary,";text-wrap:pretty;"+(true?"":0),true?"":0),image:(0,n.iv)("height:270px;width:100%;object-fit:cover;object-position:center;border-radius:",h.E0[8],";"+(true?"":0),true?"":0),featuresTiTle:(0,n.iv)(y.c.body("medium"),";color:",h.Jv.text.primary,";text-wrap:pretty;"+(true?"":0),true?"":0),features:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[4],";padding-right:",h.W0[48],";"+(true?"":0),true?"":0),feature:(0,n.iv)(Pt.i.display.flex(),";gap:",h.W0[12],";",y.c.small(),";color:",h.Jv.text.title,";span{text-wrap:pretty;}"+(true?"":0),true?"":0),checkIcon:(0,n.iv)("flex-shrink:0;color:",h.Jv.text.success,";"+(true?"":0),true?"":0)};var pn={text:{warning:"#D47E00",success:"#D47E00",danger:"#f44337",info:"#D47E00",primary:"#D47E00"},icon:{warning:"#FAB000",success:"#FAB000",danger:"#f55e53",info:"#FAB000",primary:"#FAB000"},background:{warning:"#FBFAE9",success:"#FBFAE9",danger:"#fdd9d7",info:"#FBFAE9",primary:"#FBFAE9"}};var vn=function t(e){var r=e.children,o=e.type,i=o===void 0?"warning":o,a=e.icon;return(0,n.tZ)("div",{css:yn.wrapper({type:i})},(0,n.tZ)(k.Z,{when:a},(function(t){return(0,n.tZ)(d.Z,{style:yn.icon({type:i}),name:t,height:24,width:24})})),(0,n.tZ)("span",null,r))};const hn=vn;var yn={wrapper:function t(e){var r=e.type;return(0,n.iv)(y.c.caption(),";display:flex;align-items:start;padding:",h.W0[8]," ",h.W0[12],";width:100%;border-radius:",h.E0.card,";gap:",h.W0[4],";background-color:",pn.background[r],";color:",pn.text[r],";"+(true?"":0),true?"":0)},icon:function t(e){var r=e.type;return(0,n.iv)("color:",pn.icon[r],";flex-shrink:0;"+(true?"":0),true?"":0)}};function bn(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var gn=true?{name:"1wge6iy",styles:"left:3px"}:0;var mn=true?{name:"c7mfxx",styles:"right:3px"}:0;var wn=true?{name:"1pf4cml",styles:"left:11px"}:0;var xn=true?{name:"ovq9sj",styles:"top:2px;left:3px;width:12px;height:12px"}:0;var _n=true?{name:"16g29gd",styles:"width:26px;height:16px"}:0;var Zn={switchStyles:function t(e){return(0,n.iv)("&[data-input]{all:unset;appearance:none;border:0;width:40px;height:24px;background:",h.Jv.color.black[10],";border-radius:12px;position:relative;display:inline-block;vertical-align:middle;cursor:pointer;transition:background-color 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);",e==="small"&&_n," &::before{display:none!important;}&:focus{border:none;outline:none;box-shadow:none;}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:after{content:'';position:absolute;top:3px;left:",h.W0[4],";width:18px;height:18px;background:",h.Jv.background.white,";border-radius:",h.E0.circle,";box-shadow:",h.AF["switch"],";transition:left 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);",e==="small"&&xn,";}&:checked{background:",h.Jv.primary.main,";&:after{left:18px;",e==="small"&&wn,";}}&:disabled{pointer-events:none;filter:none;opacity:0.5;}}"+(true?"":0),true?"":0)},labelStyles:function t(e){return(0,n.iv)(y.c.caption(),";color:",e?h.Jv.text.title:h.Jv.text.subdued,";"+(true?"":0),true?"":0)},wrapperStyle:function t(e){return(0,n.iv)("display:flex;align-items:center;justify-content:space-between;width:fit-content;flex-direction:",e==="left"?"row":"row-reverse",";column-gap:",h.W0[12],";position:relative;"+(true?"":0),true?"":0)},spinner:function t(e){return(0,n.iv)("display:flex;position:absolute;top:50%;transform:translateY(-50%);",e&&mn," ",!e&&gn,";"+(true?"":0),true?"":0)}};var On=l().forwardRef((function(t,e){var r=t.id,o=r===void 0?(0,b.x0)():r,i=t.name,a=t.label,u=t.value,l=t.checked,c=t.disabled,s=t.loading,d=t.onChange,f=t.labelPosition,p=f===void 0?"left":f,v=t.labelCss,h=t.size,y=h===void 0?"regular":h;var g=function t(e){d===null||d===void 0?void 0:d(e.target.checked,e)};return(0,n.tZ)("div",{css:Zn.wrapperStyle(p)},a&&(0,n.tZ)("label",{css:[Zn.labelStyles(l||false),v,true?"":0,true?"":0],htmlFor:o},a),(0,n.tZ)("input",{ref:e,value:u?String(u):undefined,type:"checkbox",name:i,id:o,checked:!!l,disabled:c,css:Zn.switchStyles(y),onChange:g,"data-input":true}),(0,n.tZ)(k.Z,{when:s},(0,n.tZ)("span",{css:Zn.spinner(!!l)},(0,n.tZ)(O.ZP,{size:y==="small"?12:20}))))}));const Sn=On;function kn(){kn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return kn.apply(this,arguments)}var jn=function t(e){var r=e.field,o=e.fieldState,i=e.label,a=e.disabled,u=e.loading,l=e.labelPosition,c=l===void 0?"left":l,s=e.helpText,d=e.isHidden,f=e.labelCss,p=e.onChange;return(0,n.tZ)(Dt,{label:i,field:r,fieldState:o,loading:u,helpText:s,isHidden:d,isInlineLabel:true},(function(t){return(0,n.tZ)("div",{css:Cn.wrapper},(0,n.tZ)(Sn,kn({},r,t,{disabled:a,checked:r.value,labelCss:f,labelPosition:c,onChange:function t(){r.onChange(!r.value);p===null||p===void 0?void 0:p(!r.value)}})))}))};const En=F(jn);var Cn={wrapper:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;gap:",h.W0[40],";"+(true?"":0),true?"":0)};var An=r(733);function Pn(t,e){return Tn(t)||Dn(t,e)||Ln(t,e)||Wn()}function Wn(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ln(t,e){if(!t)return;if(typeof t==="string")return In(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return In(t,e)}function In(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Dn(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Tn(t){if(Array.isArray(t))return t}var Mn=function t(){return{required:{value:true,message:(0,a.__)("This field is required","tutor")}}};var Jn=function t(e){var r=e.maxValue,n=e.message;return{maxLength:{value:r,message:n||__("Max. value should be ".concat(r))}}};var Nn=function t(){return{validate:function t(e){if((e===null||e===void 0?void 0:e.amount)===undefined){return __("The field is required","tutor")}return undefined}}};var Fn=function t(e){if(!(0,An.Z)(new Date(e||""))){return(0,a.__)("Invalid date entered!","tutor")}return undefined};var Bn=function t(e){return{validate:function t(r){if(r&&e<r.length){return __("Maximum ".concat(e," character supported"),"tutor")}return undefined}}};var zn=function t(e){if(!e){return undefined}var r=(0,a.__)("Invalid time entered!","tutor");var n=e.split(":"),o=Pn(n,2),i=o[0],u=o[1];if(!i||!u){return r}var l=u.split(" "),c=Pn(l,2),s=c[0],d=c[1];if(!s||!d){return r}if(i.length!==2||s.length!==2){return r}if(Number(i)<1||Number(i)>12){return r}if(Number(s)<0||Number(s)>59){return r}if(!["am","pm"].includes(d.toLowerCase())){return r}return undefined};var Rn=r(7363);function Un(t){"@babel/helpers - typeof";return Un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(t)}var Gn,Qn;function Yn(){Yn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Yn.apply(this,arguments)}function qn(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */qn=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Un(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Hn(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Vn(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Hn(i,n,o,a,u,"next",t)}function u(t){Hn(i,n,o,a,u,"throw",t)}a(undefined)}))}}var $n=((Gn=j.y.settings)===null||Gn===void 0?void 0:Gn.chatgpt_enable)==="on";var Kn=(Qn=j.y.current_user.roles)===null||Qn===void 0?void 0:Qn.includes(S.er.ADMINISTRATOR);var Xn=function t(e){var r=e.closeModal,o=e.image,i=e.image2x;var l=at({defaultValues:{openAIApiKey:"",enable_open_ai:$n},shouldFocusError:true});var c=Lr();var s=function(){var t=Vn(qn().mark((function t(e){var n;return qn().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:o.next=2;return c.mutateAsync({chatgpt_api_key:e.openAIApiKey,chatgpt_enable:e.enable_open_ai?1:0});case 2:n=o.sent;if(n.status_code===200){r({action:"CONFIRM"});window.location.reload()}case 4:case"end":return o.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();(0,u.useEffect)((function(){l.setFocus("openAIApiKey")}),[]);return(0,n.tZ)(Ir.Z,{onClose:function t(){return r({action:"CLOSE"})},title:Kn?(0,a.__)("Set OpenAI API key","tutor"):undefined,entireHeader:Kn?undefined:(0,n.tZ)(Rn.Fragment,null," "),maxWidth:560},(0,n.tZ)("div",{css:eo.wrapper({isCurrentUserAdmin:Kn})},(0,n.tZ)(k.Z,{when:Kn,fallback:(0,n.tZ)(Rn.Fragment,null,(0,n.tZ)("img",{css:eo.image,src:o,srcSet:i?"".concat(o," 1x, ").concat(i," 2x"):"".concat(o," 1x"),alt:(0,a.__)("Connect API KEY","tutor")}),(0,n.tZ)("div",null,(0,n.tZ)("div",{css:eo.message},(0,a.__)("API is not connected","tutor")),(0,n.tZ)("div",{css:eo.title},(0,a.__)("Please, ask your Admin to connect the API with Tutor LMS Pro.","tutor"))))},(0,n.tZ)(Rn.Fragment,null,(0,n.tZ)("form",{css:eo.formWrapper,onSubmit:l.handleSubmit(s)},(0,n.tZ)("div",{css:eo.infoText},(0,n.tZ)("div",{dangerouslySetInnerHTML:{__html:(0,a.sprintf)((0,a.__)("Find your Secret API key in your %1$sOpenAI User settings%2$s and paste it here to connect OpenAI with your Tutor LMS website.","tutor"),'<a href="'.concat(j.Z.CHATGPT_PLATFORM_URL,'" target="_blank" rel="noopener noreferrer">'),"</a>")}}),(0,n.tZ)(hn,{type:"info",icon:"warning"},(0,a.__)("The page will reload after submission. Make sure to save the course information.","tutor"))),(0,n.tZ)(p.Qr,{name:"openAIApiKey",control:l.control,rules:Mn(),render:function t(e){return(0,n.tZ)(Zo,Yn({},e,{type:"password",isPassword:true,label:(0,a.__)("OpenAI API key","tutor"),placeholder:(0,a.__)("Enter your OpenAI API key","tutor")}))}}),(0,n.tZ)(p.Qr,{name:"enable_open_ai",control:l.control,render:function t(e){return(0,n.tZ)(En,Yn({},e,{label:(0,a.__)("Enable OpenAI","tutor")}))}})),(0,n.tZ)("div",{css:eo.formFooter},(0,n.tZ)(v.Z,{onClick:function t(){return r({action:"CLOSE"})},variant:"text",size:"small"},(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{size:"small",onClick:l.handleSubmit(s),loading:c.isPending},(0,a.__)("Save","tutor")))))))};const to=Xn;var eo={wrapper:function t(e){var r=e.isCurrentUserAdmin;return(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[20],";",!r&&(0,n.iv)("padding:",h.W0[24],";padding-top:",h.W0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},formWrapper:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[20],";padding:",h.W0[16]," ",h.W0[16]," 0 ",h.W0[16],";"+(true?"":0),true?"":0),infoText:(0,n.iv)(y.c.small(),";",Pt.i.display.flex("column"),";gap:",h.W0[8],";color:",h.Jv.text.subdued,";a{",Pt.i.resetButton," color:",h.Jv.text.brand,";}"+(true?"":0),true?"":0),formFooter:(0,n.iv)(Pt.i.display.flex(),";justify-content:flex-end;gap:",h.W0[16],";border-top:1px solid ",h.Jv.stroke.divider,";padding:",h.W0[16],";"+(true?"":0),true?"":0),image:(0,n.iv)("height:310px;width:100%;object-fit:cover;object-position:center;border-radius:",h.E0[8],";"+(true?"":0),true?"":0),message:(0,n.iv)(y.c.small(),";color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.heading4("medium"),";color:",h.Jv.text.primary,";margin-top:",h.W0[4],";text-wrap:pretty;"+(true?"":0),true?"":0)};const ro=r.p+"images/6d34e8c6da0e2b4bfbd21a38bf7bbaf0-generate-text-2x.webp";const no=r.p+"images/1cc4846c27ec533c869242e997e1c783-generate-text.webp";var oo=r(7363);function io(t){"@babel/helpers - typeof";return io="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},io(t)}var ao;function uo(){uo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return uo.apply(this,arguments)}function lo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function co(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lo(Object(r),!0).forEach((function(e){so(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function so(t,e,r){e=fo(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function fo(t){var e=po(t,"string");return io(e)==="symbol"?e:String(e)}function po(t,e){if(io(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(io(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function vo(t,e){return mo(t)||go(t,e)||yo(t,e)||ho()}function ho(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function yo(t,e){if(!t)return;if(typeof t==="string")return bo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bo(t,e)}function bo(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function go(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function mo(t){if(Array.isArray(t))return t}var wo=!!j.y.tutor_pro_url;var xo=(ao=j.y.settings)===null||ao===void 0?void 0:ao.chatgpt_key_exist;var _o=function t(e){var r;var o=e.label,i=e.type,l=i===void 0?"text":i,c=e.maxLimit,s=e.field,f=e.fieldState,p=e.disabled,h=e.readOnly,y=e.loading,g=e.placeholder,m=e.helpText,w=e.onChange,x=e.onKeyDown,_=e.isHidden,Z=e.isClearable,O=Z===void 0?false:Z,S=e.isSecondary,j=S===void 0?false:S,C=e.removeBorder,A=e.dataAttribute,P=e.isInlineLabel,W=P===void 0?false:P,L=e.isPassword,I=L===void 0?false:L,D=e.style,T=e.selectOnFocus,M=T===void 0?false:T,J=e.autoFocus,N=J===void 0?false:J,F=e.generateWithAi,B=F===void 0?false:F,z=e.isMagicAi,R=z===void 0?false:z,U=e.allowNegative,G=U===void 0?false:U,Q=e.onClickAiButton;var Y=(0,u.useState)(l),q=vo(Y,2),H=q[0],V=q[1];var $=(0,un.d)(),K=$.showModal;var X=(0,u.useRef)(null);var tt=(r=s.value)!==null&&r!==void 0?r:"";var et=undefined;if(H==="number"){tt=(0,b.jv)("".concat(tt),G).replace(/(\..*)\./g,"$1")}if(c){et={maxLimit:c,inputCharacter:tt.toString().length}}var rt=co({},(0,E.$K)(A)&&so({},A,true));var nt=function t(){if(!wo){K({component:dn,props:{image:no,image2x:ro}})}else if(!xo){K({component:to,props:{image:no,image2x:ro}})}else{K({component:on,isMagicAi:true,props:{title:(0,a.__)("AI Studio","tutor"),icon:(0,n.tZ)(d.Z,{name:"magicAiColorize",width:24,height:24}),characters:120,field:s,fieldState:f,format:"title",is_html:false,fieldLabel:(0,a.__)("Create a Compelling Title","tutor"),fieldPlaceholder:(0,a.__)("Describe the main focus of your course in a few words","tutor")}});Q===null||Q===void 0?void 0:Q()}};return(0,n.tZ)(Dt,{label:o,field:s,fieldState:f,disabled:p,readOnly:h,loading:y,placeholder:g,helpText:m,isHidden:_,characterCount:et,isSecondary:j,removeBorder:C,isInlineLabel:W,inputStyle:D,generateWithAi:B,onClickAiButton:nt,isMagicAi:R},(function(t){return(0,n.tZ)(oo.Fragment,null,(0,n.tZ)("div",{css:Oo.container(O||I)},(0,n.tZ)("input",uo({},s,t,rt,{type:H==="number"?"text":H,value:tt,autoFocus:N,onChange:function t(e){var r=e.target.value;var n=H==="number"?(0,b.jv)(r):r;s.onChange(n);if(w){w(n)}},onClick:function t(e){e.stopPropagation()},onKeyDown:function t(e){e.stopPropagation();x===null||x===void 0?void 0:x(e.key)},autoComplete:"off",ref:function t(e){s.ref(e);X.current=e},onFocus:function t(){if(!M||!X.current){return}X.current.select()}})),(0,n.tZ)(k.Z,{when:I},(0,n.tZ)("div",{css:Oo.eyeButtonWrapper},(0,n.tZ)("button",{type:"button",css:Oo.eyeButton({type:H}),onClick:function t(){return V((function(t){return t==="password"?"text":"password"}))}},(0,n.tZ)(d.Z,{name:"eye",height:24,width:24})))),(0,n.tZ)(k.Z,{when:O&&!!s.value&&H!=="password"},(0,n.tZ)("div",{css:Oo.clearButton},(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){return s.onChange("")}},(0,n.tZ)(d.Z,{name:"timesAlt"}))))))}))};const Zo=F(_o);var Oo={container:function t(e){return(0,n.iv)("position:relative;display:flex;input{&.tutor-input-field{",e&&"padding-right: ".concat(h.W0[36],";"),";}}"+(true?"":0),true?"":0)},clearButton:(0,n.iv)("position:absolute;right:",h.W0[4],";top:",h.W0[4],";width:32px;height:32px;background:transparent;button{padding:",h.W0[8],";border-radius:",h.E0[2],";}"+(true?"":0),true?"":0),eyeButtonWrapper:(0,n.iv)("position:absolute;display:flex;right:",h.W0[4],";top:50%;transform:translateY(-50%);"+(true?"":0),true?"":0),eyeButton:function t(e){var r=e.type;return(0,n.iv)(Pt.i.resetButton," ",Pt.i.flexCenter()," color:",h.Jv.icon["default"],";padding:",h.W0[4],";border-radius:",h.E0[2],";background:transparent;",r!=="password"&&(0,n.iv)("color:",h.Jv.icon.brand,";"+(true?"":0),true?"":0)," &:focus,&:active,&:hover{background:none;color:",h.Jv.icon["default"],";}:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:2px;}"+(true?"":0),true?"":0)}};var So=r(7363);function ko(t){"@babel/helpers - typeof";return ko="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ko(t)}function jo(t,e,r){e=Eo(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Eo(t){var e=Co(t,"string");return ko(e)==="symbol"?e:String(e)}function Co(t,e){if(ko(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ko(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ao(){Ao=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ao.apply(this,arguments)}function Po(t,e){return To(t)||Do(t,e)||Lo(t,e)||Wo()}function Wo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Lo(t,e){if(!t)return;if(typeof t==="string")return Io(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Io(t,e)}function Io(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Do(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function To(t){if(Array.isArray(t))return t}function Mo(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Jo=function t(e){var r;var o=e.label,i=e.field,l=e.fieldState,c=e.disabled,s=e.loading,f=e.placeholder,p=e.helpText,h=e.isInlineLabel,y=e.clearable,g=e.listItemsLabel,m=e.optionsWrapperStyle;var w=(0,u.useState)(false),x=Po(w,2),_=x[0],Z=x[1];var O=(0,u.useState)(""),j=Po(O,2),E=j[0],C=j[1];var A=Y(E,300);var P=Et(A);var W=(0,b.TQ)((r=P.data)!==null&&r!==void 0?r:[]);var L=(0,xt.l)({isOpen:_,isDropdown:true,dependencies:[W.length]}),I=L.triggerRef,D=L.triggerWidth,T=L.position,M=L.popoverRef;(0,u.useEffect)((function(){if(!_){C("")}}),[_]);return(0,n.tZ)(Dt,{label:o,field:i,fieldState:l,disabled:c||W.length===0,loading:s,placeholder:f,helpText:p,isInlineLabel:h},(function(t){var e,r,o;return(0,n.tZ)(So.Fragment,null,(0,n.tZ)("div",{css:zo.inputWrapper,ref:I},(0,n.tZ)("input",Ao({},t,{type:"text",onClick:function t(e){e.stopPropagation();Z(true)},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();Z(true)}if(e.key==="Tab"){Z(false)}},autoComplete:"off",readOnly:true,disabled:c||W.length===0,value:i.value?(e=P.data)===null||e===void 0?void 0:(r=e.find((function(t){return t.id===i.value})))===null||r===void 0?void 0:r.name:"",placeholder:f})),(0,n.tZ)("button",{tabIndex:-1,type:"button",disabled:c||W.length===0,"aria-label":(0,a.__)("Toggle options","tutor"),css:zo.toggleIcon(_),onClick:function t(){Z((function(t){return!t}))}},(0,n.tZ)(d.Z,{name:"chevronDown",width:20,height:20}))),(0,n.tZ)(xt.h,{isOpen:_,onClickOutside:function t(){return Z(false)},onEscape:function t(){return Z(false)}},(0,n.tZ)("div",{css:[zo.categoryWrapper,(o={},jo(o,S.dZ?"right":"left",T.left),jo(o,"top",T.top),o),true?"":0,true?"":0],ref:M,style:{maxWidth:D}},!!g&&(0,n.tZ)("p",{css:zo.listItemLabel},g),(0,n.tZ)("div",{css:zo.searchInput},(0,n.tZ)("div",{css:zo.searchIcon},(0,n.tZ)(d.Z,{name:"search",width:24,height:24})),(0,n.tZ)("input",{type:"text",placeholder:(0,a.__)("Search","tutor"),value:E,onChange:function t(e){C(e.target.value)}})),(0,n.tZ)(k.Z,{when:W.length>0,fallback:(0,n.tZ)("div",{css:zo.notFound},(0,a.__)("No categories found.","tutor"))},(0,n.tZ)("div",{css:[zo.options,m,true?"":0,true?"":0]},W.map((function(t){return(0,n.tZ)(Fo,{key:t.id,option:t,onChange:function t(e){i.onChange(e);Z(false)}})})))),y&&(0,n.tZ)("div",{css:zo.clearButton},(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){i.onChange(null);Z(false)}},(0,a.__)("Clear selection","tutor"))))))}))};const No=Jo;var Fo=function t(e){var r=e.option,o=e.onChange,i=e.level,a=i===void 0?0:i;var u=r.children.length>0;var l=function e(){if(!u){return null}return r.children.map((function(e){return(0,n.tZ)(t,{key:e.id,option:e,onChange:o,level:a+1})}))};return(0,n.tZ)("div",{css:zo.branchItem(a)},(0,n.tZ)("button",{type:"button",onClick:function t(){return o(r.id)},title:r.name},(0,b.aV)(r.name)),l())};var Bo=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var zo={categoryWrapper:(0,n.iv)("position:absolute;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke.border,";padding:",h.W0[8]," 0;min-width:275px;"+(true?"":0),true?"":0),options:(0,n.iv)("max-height:455px;",Pt.i.overflowYAuto,";"+(true?"":0),true?"":0),notFound:(0,n.iv)(Pt.i.display.flex(),";align-items:center;",y.c.caption("regular"),";padding:",h.W0[8]," ",h.W0[16],";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),searchInput:(0,n.iv)("position:sticky;top:0;padding:",h.W0[8]," ",h.W0[16],";input{",y.c.body("regular"),";width:100%;border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[4]," ",h.W0[16]," ",h.W0[4]," ",h.W0[32],";color:",h.Jv.text.title,";appearance:textfield;:focus{",Pt.i.inputFocus,";}}"+(true?"":0),true?"":0),searchIcon:(0,n.iv)("position:absolute;left:",h.W0[24],";top:50%;transform:translateY(-50%);color:",h.Jv.icon["default"],";display:flex;"+(true?"":0),true?"":0),branchItem:function t(e){return(0,n.iv)("position:relative;z-index:",h.W5.positive,";button{",Pt.i.resetButton,";",y.c.body("regular"),";",Pt.i.text.ellipsis(1),";color:",h.Jv.text.title,";padding-left:calc(",h.W0[24]," + ",h.W0[24]," * ",e,");line-height:",h.Nv[36],";padding-right:",h.W0[16],";width:100%;&:hover,&:focus,&:active{background-color:",h.Jv.background.hover,";color:",h.Jv.text.title,";}}"+(true?"":0),true?"":0)},toggleIcon:function t(e){return(0,n.iv)(Pt.i.resetButton,";position:absolute;top:",h.W0[4],";right:",h.W0[4],";display:flex;align-items:center;transition:transform 0.3s ease-in-out;color:",h.Jv.icon["default"],";padding:",h.W0[6],";&:focus,&:active,&:hover{background:none;color:",h.Jv.icon["default"],";}",e&&Bo,";"+(true?"":0),true?"":0)},inputWrapper:true?{name:"137a3a8",styles:"position:relative;input:read-only{background-color:inherit;}"}:0,clearButton:(0,n.iv)("padding:",h.W0[8]," ",h.W0[24],";box-shadow:",h.AF.dividerTop,";&>button{padding:0;}"+(true?"":0),true?"":0),listItemLabel:(0,n.iv)(y.c.caption(),";font-weight:",h.Ue.medium,";background-color:",h.Jv.background.white,";color:",h.Jv.text.hints,";padding:",h.W0[10]," ",h.W0[16],";"+(true?"":0),true?"":0),radioLabel:(0,n.iv)("line-height:",h.Nv[32],";padding-left:",h.W0[2],";"+(true?"":0),true?"":0)};var Ro=r(7363);function Uo(t){"@babel/helpers - typeof";return Uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Uo(t)}function Go(){Go=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Go.apply(this,arguments)}function Qo(t){return Ho(t)||qo(t)||ni(t)||Yo()}function Yo(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function qo(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Ho(t){if(Array.isArray(t))return oi(t)}function Vo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function $o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vo(Object(r),!0).forEach((function(e){Ko(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ko(t,e,r){e=Xo(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Xo(t){var e=ti(t,"string");return Uo(e)==="symbol"?e:String(e)}function ti(t,e){if(Uo(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Uo(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ei(t,e){return ai(t)||ii(t,e)||ni(t,e)||ri()}function ri(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ni(t,e){if(!t)return;if(typeof t==="string")return oi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oi(t,e)}function oi(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ii(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ai(t){if(Array.isArray(t))return t}var ui=function t(e){var r;var o=e.label,i=e.field,l=e.fieldState,c=e.disabled,s=e.loading,h=e.placeholder,y=e.helpText,g=e.optionsWrapperStyle;var m=at({shouldFocusError:true});var w=m.watch("search");var x=Y(w,300);var _=Et(x);var Z=At();var j=(0,u.useState)(false),E=ei(j,2),C=E[0],A=E[1];var P=(0,u.useState)(false),W=ei(P,2),L=W[0],I=W[1];var D=wt(),T=D.ref,M=D.isScrolling;(0,u.useEffect)((function(){if(!_.isLoading&&(_.data||[]).length>0){I(true)}}),[_.isLoading,_.data]);(0,u.useEffect)((function(){if(C){var t=setTimeout((function(){m.setFocus("name")}),250);return function(){clearTimeout(t)}}}),[C]);var J=(0,xt.l)({isOpen:C}),N=J.triggerRef,F=J.position,B=J.popoverRef;var z=(0,b.TQ)((r=_.data)!==null&&r!==void 0?r:[]);var R=function t(){A(false);m.reset({name:"",parent:null,search:w})};var U=function t(e){if(e.name){Z.mutate($o({name:e.name},e.parent&&{parent:e.parent}));R()}};return(0,n.tZ)(Dt,{label:o,field:i,fieldState:l,loading:s,placeholder:h,helpText:y},(function(){var t;return(0,n.tZ)(Ro.Fragment,null,(0,n.tZ)("div",{css:[di.options,g,true?"":0,true?"":0]},(0,n.tZ)("div",{css:di.categoryListWrapper,ref:T},(0,n.tZ)(k.Z,{when:!c&&(L||x)},(0,n.tZ)(p.Qr,{name:"search",control:m.control,render:function t(e){return(0,n.tZ)("div",{css:di.searchInput},(0,n.tZ)("div",{css:di.searchIcon},(0,n.tZ)(d.Z,{name:"search",width:24,height:24})),(0,n.tZ)("input",{type:"text",placeholder:(0,a.__)("Search","tutor"),value:w,disabled:c||s,onChange:function t(r){e.field.onChange(r.target.value)}}))}})),(0,n.tZ)(k.Z,{when:!_.isLoading&&!s,fallback:(0,n.tZ)(O.g4,null)},(0,n.tZ)(k.Z,{when:z.length>0,fallback:(0,n.tZ)("span",{css:di.notFound},(0,a.__)("No categories found.","tutor"))},z.map((function(t,e){return(0,n.tZ)(si,{key:t.id,disabled:c,option:t,value:i.value,isLastChild:e===z.length-1,onChange:function t(e){i.onChange((0,f.Uy)(i.value,(function(t){if(Array.isArray(t)){return t.includes(e)?t.filter((function(t){return t!==e})):[].concat(Qo(t),[e])}return[e]})))}})}))))),(0,n.tZ)(k.Z,{when:!c},(0,n.tZ)("div",{ref:N,css:di.addButtonWrapper({isActive:M,hasCategories:_.isLoading||z.length>0})},(0,n.tZ)("button",{disabled:c||s,type:"button",css:di.addNewButton,onClick:function t(){return A(true)}},(0,n.tZ)(d.Z,{width:24,height:24,name:"plus"})," ",(0,a.__)("Add","tutor"))))),(0,n.tZ)(xt.h,{isOpen:C,onClickOutside:R,onEscape:R},(0,n.tZ)("div",{css:[di.categoryFormWrapper,(t={},Ko(t,S.dZ?"right":"left",F.left),Ko(t,"top",F.top),t),true?"":0,true?"":0],ref:B},(0,n.tZ)(p.Qr,{name:"name",control:m.control,rules:{required:(0,a.__)("Category name is required","tutor")},render:function t(e){return(0,n.tZ)(Zo,Go({},e,{placeholder:(0,a.__)("Category name","tutor"),selectOnFocus:true}))}}),(0,n.tZ)(p.Qr,{name:"parent",control:m.control,render:function t(e){return(0,n.tZ)(No,Go({},e,{placeholder:(0,a.__)("Select parent","tutor"),clearable:!!e.field.value}))}}),(0,n.tZ)("div",{css:di.categoryFormButtons},(0,n.tZ)(v.Z,{variant:"text",size:"small",onClick:R},(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{variant:"secondary",size:"small",loading:Z.isPending,onClick:m.handleSubmit(U)},(0,a.__)("Ok","tutor"))))))}))};const li=F(ui);var ci=function t(e){return e.children.reduce((function(e,r){return e+t(r)}),e.children.length)};var si=function t(e){var r=e.option,o=e.value,i=e.onChange,a=e.isLastChild,u=e.disabled;var l=ci(r);var c=l>0;var s=(0,b.VH)(a,l);var d=function e(){if(!c){return null}return r.children.map((function(e,a){return(0,n.tZ)(t,{key:e.id,option:e,value:o,onChange:i,isLastChild:a===r.children.length-1,disabled:u})}))};return(0,n.tZ)("div",{css:di.branchItem({leftBarHeight:s,hasParent:r.parent!==0})},(0,n.tZ)(Z,{checked:Array.isArray(o)?o.includes(r.id):o===r.id,label:(0,b.aV)(r.name),onChange:function t(){i(r.id)},labelCss:di.checkboxLabel,disabled:u}),d())};var di={options:(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";padding:",h.W0[8]," 0;background-color:",h.Jv.bg.white,";"+(true?"":0),true?"":0),categoryListWrapper:(0,n.iv)(Pt.i.overflowYAuto,";max-height:208px;"+(true?"":0),true?"":0),notFound:(0,n.iv)(Pt.i.display.flex(),";align-items:center;",y.c.caption("regular"),";padding:",h.W0[8]," ",h.W0[16],";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),searchInput:(0,n.iv)("position:sticky;top:0;padding:",h.W0[4]," ",h.W0[16],";background-color:",h.Jv.background.white,";z-index:",h.W5.dropdown,";input{",y.c.body("regular"),";width:100%;border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[4]," ",h.W0[16]," ",h.W0[4]," ",h.W0[32],";color:",h.Jv.text.title,";appearance:textfield;:focus{",Pt.i.inputFocus,";}}"+(true?"":0),true?"":0),searchIcon:(0,n.iv)("position:absolute;left:",h.W0[24],";top:50%;transform:translateY(-50%);color:",h.Jv.icon["default"],";display:flex;"+(true?"":0),true?"":0),checkboxLabel:(0,n.iv)("line-height:1.88rem!important;span:last-of-type{",Pt.i.text.ellipsis(1),";}"+(true?"":0),true?"":0),branchItem:function t(e){var r=e.leftBarHeight,o=e.hasParent;return(0,n.iv)("line-height:",h.W0[32],";position:relative;z-index:",h.W5.positive,";margin-inline:",h.W0[20]," ",h.W0[16],";&:after{content:'';position:absolute;height:",r,";width:1px;left:9px;top:26px;background-color:",h.Jv.stroke.divider,";z-index:",h.W5.level,";}",o&&(0,n.iv)("&:before{content:'';position:absolute;height:1px;width:10px;left:-10px;top:",h.W0[16],";background-color:",h.Jv.stroke.divider,";z-index:",h.W5.level,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},addNewButton:(0,n.iv)(Pt.i.resetButton,";",y.c.small("medium"),";color:",h.Jv.brand.blue,";padding:0 ",h.W0[8],";display:flex;align-items:center;border-radius:",h.E0[2],";&:focus,&:active,&:hover{background:none;color:",h.Jv.brand.blue,";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{color:",h.Jv.text.disable,";}"+(true?"":0),true?"":0),categoryFormWrapper:(0,n.iv)("position:absolute;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke.border,";padding:",h.W0[16],";min-width:306px;display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),categoryFormButtons:(0,n.iv)("display:flex;justify-content:end;gap:",h.W0[8],";"+(true?"":0),true?"":0),addButtonWrapper:function t(e){var r=e.isActive,o=r===void 0?false:r,i=e.hasCategories,a=i===void 0?false:i;return(0,n.iv)("transition:box-shadow 0.3s ease-in-out;padding-inline:",h.W0[8],";padding-block:",a?h.W0[4]:"0px",";",o&&(0,n.iv)("box-shadow:",h.AF.scrollable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var fi=r(5581);var pi=r.n(fi);function vi(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var hi={large:"regular",regular:"small",small:"small"};var yi=function t(e){var r=e.buttonText,o=r===void 0?(0,a.__)("Upload Media","tutor"):r,i=e.infoText,u=e.size,l=u===void 0?"regular":u,c=e.value,s=e.uploadHandler,f=e.clearHandler,p=e.emptyImageCss,y=e.previewImageCss,b=e.overlayCss,g=e.replaceButtonText,m=e.loading,w=e.disabled,x=w===void 0?false:w,_=e.isClearAble,Z=_===void 0?true:_;return(0,n.tZ)(k.Z,{when:!m,fallback:(0,n.tZ)("div",{css:wi.emptyMedia({size:l,isDisabled:x})},(0,n.tZ)(O.fz,null))},(0,n.tZ)(k.Z,{when:c===null||c===void 0?void 0:c.url,fallback:(0,n.tZ)("div",{"aria-disabled":x,css:[wi.emptyMedia({size:l,isDisabled:x}),p,true?"":0,true?"":0],onClick:function t(e){e.stopPropagation();if(x){return}s()},onKeyDown:function t(e){if(!x&&e.key==="Enter"){e.preventDefault();s()}}},(0,n.tZ)(d.Z,{name:"addImage",width:32,height:32}),(0,n.tZ)(v.Z,{disabled:x,size:hi[l],variant:"secondary",buttonContentCss:wi.buttonText,"data-cy":"upload-media"},o),(0,n.tZ)(k.Z,{when:i},(0,n.tZ)("p",{css:wi.infoTexts},i)))},(function(t){return(0,n.tZ)("div",{css:[wi.previewWrapper({size:l,isDisabled:x}),y,true?"":0,true?"":0],"data-cy":"media-preview"},(0,n.tZ)("img",{src:t,alt:c===null||c===void 0?void 0:c.title,css:wi.imagePreview}),(0,n.tZ)("div",{css:[wi.hoverPreview,b,true?"":0,true?"":0],"data-hover-buttons-wrapper":true},(0,n.tZ)(v.Z,{disabled:x,variant:"secondary",size:hi[l],buttonCss:(0,n.iv)("margin-top:",Z&&h.W0[16],";"+(true?"":0),true?"":0),onClick:function t(e){e.stopPropagation();s()},"data-cy":"replace-media"},g||(0,a.__)("Replace Image","tutor")),(0,n.tZ)(k.Z,{when:Z},(0,n.tZ)(v.Z,{disabled:x,variant:"text",size:hi[l],onClick:function t(e){e.stopPropagation();f()},"data-cy":"clear-media"},(0,a.__)("Remove","tutor")))))})))};const bi=yi;var gi=true?{name:"1kn988u",styles:"width:168px"}:0;var mi=true?{name:"1kn988u",styles:"width:168px"}:0;var wi={emptyMedia:function t(e){var r=e.size,o=e.isDisabled;return(0,n.iv)("width:100%;height:168px;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:",h.W0[8],";border:1px dashed ",h.Jv.stroke.border,";border-radius:",h.E0[8],";background-color:",h.Jv.bg.white,";overflow:hidden;cursor:",o?"not-allowed":"pointer",";",r==="small"&&mi," svg{color:",h.Jv.icon["default"],";}&:hover svg{color:",!o&&h.Jv.brand.blue,";}"+(true?"":0),true?"":0)},buttonText:(0,n.iv)("color:",h.Jv.text.brand,";"+(true?"":0),true?"":0),infoTexts:(0,n.iv)(y.c.tiny(),";color:",h.Jv.text.subdued,";text-align:center;"+(true?"":0),true?"":0),previewWrapper:function t(e){var r=e.size,o=e.isDisabled;return(0,n.iv)("width:100%;height:168px;border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";overflow:hidden;position:relative;background-color:",h.Jv.bg.white,";",r==="small"&&gi," &:hover{[data-hover-buttons-wrapper]{display:",o?"none":"flex",";opacity:",o?0:1,";}}"+(true?"":0),true?"":0)},imagePreview:true?{name:"1obrg50",styles:"height:100%;width:100%;object-fit:contain"}:0,hoverPreview:(0,n.iv)("display:flex;flex-direction:column;justify-content:center;align-items:center;gap:",h.W0[8],";opacity:0;position:absolute;inset:0;background-color:",pi()(h.Jv.color.black.main,.6),";button:first-of-type{box-shadow:",h.AF.button,";}button:last-of-type:not(:only-of-type){color:",h.Jv.text.white,";box-shadow:none;}"+(true?"":0),true?"":0)};function xi(t,e){return ki(t)||Si(t,e)||Zi(t,e)||_i()}function _i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Zi(t,e){if(!t)return;if(typeof t==="string")return Oi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Oi(t,e)}function Oi(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Si(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ki(t){if(Array.isArray(t))return t}var ji=[(0,a.__)("A serene classroom setting with books and a chalkboard","tutor"),(0,a.__)("An abstract representation of innovation and creativity","tutor"),(0,a.__)("A vibrant workspace with a laptop and coffee cup","tutor"),(0,a.__)("A modern design with digital learning icons","tutor"),(0,a.__)("A futuristic cityscape with a glowing pathway","tutor"),(0,a.__)("A peaceful nature scene with soft colors","tutor"),(0,a.__)("A professional boardroom with sleek visuals","tutor"),(0,a.__)("A stack of books with warm, inviting lighting","tutor"),(0,a.__)("A dynamic collage of technology and education themes","tutor"),(0,a.__)("A bold and minimalistic design with striking colors","tutor")];var Ei=l().createContext(null);var Ci=function t(){var e=(0,u.useContext)(Ei);if(!e){throw new Error("useMagicImageGeneration must be used within MagicImageGenerationProvider.")}return e};var Ai=function t(e){var r=e.children,o=e.field,i=e.fieldState,a=e.onCloseModal;var l=at({defaultValues:{prompt:"",style:"none"}});var c=(0,u.useState)("generation"),s=xi(c,2),d=s[0],f=s[1];var v=(0,u.useState)(""),h=xi(v,2),y=h[0],b=h[1];var g=(0,u.useState)([null,null,null,null]),m=xi(g,2),w=m[0],x=m[1];var _=(0,u.useCallback)((function(t){f(t)}),[]);return(0,n.tZ)(Ei.Provider,{value:{state:d,onDropdownMenuChange:_,images:w,setImages:x,currentImage:y,setCurrentImage:b,field:o,fieldState:i,onCloseModal:a}},(0,n.tZ)(p.RV,l,r))};var Pi=function t(e){var r=e.field,o=e.fieldState,i=e.label,a=e.options,u=a===void 0?[]:a,l=e.disabled;return(0,n.tZ)(Dt,{field:r,fieldState:o,label:i,disabled:l},(function(){return(0,n.tZ)("div",{css:Li.wrapper},u.map((function(t,e){return(0,n.tZ)("button",{type:"button",key:e,css:Li.item(r.value===t.value),onClick:function e(){r.onChange(t.value)},disabled:l},(0,n.tZ)("img",{src:t.image,alt:t.label,width:64,height:64}),(0,n.tZ)("p",null,t.label))})))}))};const Wi=Pi;var Li={wrapper:(0,n.iv)("display:grid;grid-template-columns:repeat(4, minmax(64px, 1fr));gap:",h.W0[12],";margin-top:",h.W0[4],";"+(true?"":0),true?"":0),item:function t(e){return(0,n.iv)(Pt.i.resetButton,";display:flex;flex-direction:column;gap:",h.W0[4],";align-items:center;width:100%;cursor:pointer;input{appearance:none;}p{",y.c.small(),";width:100%;",Pt.i.textEllipsis,";color:",h.Jv.text.subdued,";text-align:center;}&:hover,&:focus-visible{",!e&&(0,n.iv)("img{border-color:",h.Jv.stroke.hover,";}"+(true?"":0),true?"":0),";}img{border-radius:",h.E0[6],";border:2px solid ",h.Jv.stroke.border,";outline:2px solid transparent;outline-offset:2px;transition:border-color 0.3s ease;",e&&(0,n.iv)("outline-color:",h.Jv.stroke.magicAi,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)}};const Ii=r.p+"images/56f20c93d8e28423f724fe4e914fbd21-3d.png";const Di=r.p+"images/7a53b07b7f13e48b7b7b47dff35d9946-black-and-white.png";const Ti=r.p+"images/9613f2a35fc147cbde38998fc279f6e9-concept.png";const Mi=r.p+"images/ff5a8a3d6c18c02f00d659da3824176b-dreamy.png";const Ji=r.p+"images/bff40839481a6e109932774fea006137-filmic.png";const Ni=r.p+"images/dec5e33b385ba1a7c841dde2b6c1a5af-illustration.png";const Fi=r.p+"images/83571e85f649c56b82349466a5b4c844-neon.png";const Bi=r.p+"images/9dcf3f4907036dd08b31bf2a7181bed0-none.jpg";const zi=r.p+"images/fc8edfd709e8f6ed349b59a0f0a00647-painting.png";const Ri=r.p+"images/32925d4873712d856f4abc340b3334cb-photo.png";const Ui=r.p+"images/fb8df26f9102747dfafc31d912d6d074-retro.png";const Gi=r.p+"images/7c935ca7690aecae8c42142d8cec660e-sketch.png";function Qi(t,e){t.lineTo(e.x,e.y);t.stroke()}function Yi(t,e){var r=e.x-t.x;var n=e.y-t.y;return Math.sqrt(r*r+n*n)}function qi(t){var e=atob(t.split(",")[1]);var r=t.split(",")[0].split(":")[1].split(";")[0];var n=new ArrayBuffer(e.length);var o=new Uint8Array(n);for(var i=0;i<e.length;i++){o[i]=e.charCodeAt(i)}return new Blob([n],{type:r})}function Hi(t,e){var r=qi(t);var n=document.createElement("a");n.href=URL.createObjectURL(r);n.download=e;document.body.appendChild(n);n.click();document.body.removeChild(n)}function Vi(t,e){var r=document.createElement("canvas");r.width=1024;r.height=1024;var n=r.getContext("2d");n===null||n===void 0?void 0:n.putImageData(t,0,0);n===null||n===void 0?void 0:n.drawImage(r,0,0,1024,1024);return new Promise((function(t){r.toBlob((function(r){if(!r){t(null);return}t(new File([r],e,{type:"image/png"}))}))}))}var $i=function t(e){if(e&&typeof e!=="function"&&e.current){var r=e.current;var n=r.getContext("2d");return{canvas:r,context:n}}return{canvas:null,context:null}};var Ki=function t(e){return e.toDataURL("image/png")};var Xi=r(7363);function ta(t){"@babel/helpers - typeof";return ta="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ta(t)}var ea;function ra(t,e){if(!e){e=t.slice(0)}return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function na(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */na=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==ta(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function oa(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function ia(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){oa(i,n,o,a,u,"next",t)}function u(t){oa(i,n,o,a,u,"throw",t)}a(undefined)}))}}function aa(t,e){return da(t)||sa(t,e)||la(t,e)||ua()}function ua(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function la(t,e){if(!t)return;if(typeof t==="string")return ca(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ca(t,e)}function ca(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function sa(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function da(t){if(Array.isArray(t))return t}function fa(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var pa=[{label:(0,a.__)("Magic Fill","tutor"),value:"magic-fill",icon:(0,n.tZ)(d.Z,{name:"magicWand",width:24,height:24})},{label:(0,a.__)("Download","tutor"),value:"download",icon:(0,n.tZ)(d.Z,{name:"download",width:24,height:24})}];var va=function t(e){var r=e.src,o=e.loading,i=e.index;var l=(0,u.useRef)(null);var c=(0,u.useState)(false),s=aa(c,2),f=s[0],p=s[1];var v=Ci(),h=v.onDropdownMenuChange,y=v.setCurrentImage,g=v.onCloseModal,m=v.field;var w=xr();if(o||!r){return(0,n.tZ)("div",{css:wa.loader(i+1)})}return(0,n.tZ)(Xi.Fragment,null,(0,n.tZ)("div",{css:wa.image({isActive:w.isPending})},(0,n.tZ)("img",{src:r,alt:(0,a.__)("Generated Image","tutor")}),(0,n.tZ)("div",{"data-actions":true},(0,n.tZ)("div",{css:wa.useButton},(0,n.tZ)(Ht,{variant:"primary",disabled:w.isPending,onClick:ia(na().mark((function t(){var e;return na().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(r){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return w.mutateAsync({image:r});case 4:e=n.sent;if(e.data){m.onChange(e.data);g()}case 6:case"end":return n.stop()}}),t)}))),loading:w.isPending},(0,n.tZ)(d.Z,{name:"download",width:24,height:24}),(0,a.__)("Use This","tutor"))),(0,n.tZ)(Ht,{variant:"primary",size:"icon",css:wa.threeDots,ref:l,onClick:function t(){return p(true)}},(0,n.tZ)(d.Z,{name:"threeDotsVertical",width:24,height:24})))),(0,n.tZ)(Kt.Z,{triggerRef:l,isOpen:f,closePopover:function t(){p(false)},animationType:ir.ru.slideDown,maxWidth:"160px"},(0,n.tZ)("div",{css:wa.dropdownOptions},(0,n.tZ)(le,{each:pa},(function(t,e){return(0,n.tZ)("button",{type:"button",key:e,css:wa.dropdownItem,onClick:function e(){switch(t.value){case"magic-fill":{y(r);h(t.value);break}case"download":{var n="".concat((0,b.x0)(),".png");Hi(r,n);break}default:break}p(false)}},t.icon,t.label)})))))};var ha=(0,n.F4)(ea||(ea=ra(["\n\t\t0% {\n      opacity: 0.3;\n    }\n\t\t25% {\n\t\t\topacity: 0.5;\n\t\t}\n    50% {\n      opacity: 0.7;\n    }\n\t\t75% {\n\t\t\topacity: 0.5;\n\t\t}\n    100% {\n      opacity: 0.3;\n    }\n"])));var ya=true?{name:"net8hi",styles:"background-position:bottom right;animation-delay:1s"}:0;var ba=true?{name:"15lylfh",styles:"background-position:bottom left;animation-delay:1.5s"}:0;var ga=true?{name:"1ooocct",styles:"background-position:top right;animation-delay:0.5s"}:0;var ma=true?{name:"1auq8ax",styles:"background-position:top left"}:0;var wa={loader:function t(e){return(0,n.iv)("border-radius:",h.E0[12],";background:linear-gradient(\n      73.09deg,\n      #ff9645 18.05%,\n      #ff6471 30.25%,\n      #cf6ebd 55.42%,\n      #a477d1 71.66%,\n      #3e64de 97.9%\n    );position:relative;width:100%;height:100%;background-size:612px 612px;opacity:0.3;transition:opacity 0.5s ease;animation:",ha," 2s linear infinite;",e===1&&ma," ",e===2&&ga," ",e===3&&ba," ",e===4&&ya,";"+(true?"":0),true?"":0)},image:function t(e){var r=e.isActive;return(0,n.iv)("width:100%;height:100%;overflow:hidden;border-radius:",h.E0[12],";position:relative;outline:2px solid transparent;outline-offset:2px;transition:border-radius 0.3s ease;[data-actions]{opacity:0;transition:opacity 0.3s ease;}img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}",r&&(0,n.iv)("outline-color:",h.Jv.stroke.brand,";[data-actions]{opacity:1;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{outline-color:",h.Jv.stroke.brand,";[data-actions]{opacity:1;}}"+(true?"":0),true?"":0)},threeDots:(0,n.iv)("position:absolute;top:",h.W0[8],";right:",h.W0[8],";border-radius:",h.E0[4],";"+(true?"":0),true?"":0),useButton:(0,n.iv)("position:absolute;left:50%;bottom:",h.W0[12],";transform:translateX(-50%);button{display:inline-flex;align-items:center;gap:",h.W0[4],";}"+(true?"":0),true?"":0),dropdownOptions:(0,n.iv)("display:flex;flex-direction:column;padding-block:",h.W0[8],";"+(true?"":0),true?"":0),dropdownItem:(0,n.iv)(y.c.small(),";",Pt.i.resetButton,";height:40px;display:flex;gap:",h.W0[10],";align-items:center;transition:background-color 0.3s ease;color:",h.Jv.text.title,";padding-inline:",h.W0[8],";cursor:pointer;svg{color:",h.Jv.icon["default"],";}&:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0)};var xa={wrapper:(0,n.iv)("min-width:1000px;display:grid;grid-template-columns:1fr 330px;",h.Uo.tablet,"{min-width:auto;grid-template-columns:1fr;width:100%;}"+(true?"":0),true?"":0),left:(0,n.iv)("display:flex;justify-content:center;align-items:center;background-color:#f7f7f7;z-index:",h.W5.level,";"+(true?"":0),true?"":0),right:(0,n.iv)("padding:",h.W0[20],";display:flex;flex-direction:column;align-items:space-between;z-index:",h.W5.positive,";"+(true?"":0),true?"":0),rightFooter:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";margin-top:auto;padding-top:80px;"+(true?"":0),true?"":0)};function _a(t){"@babel/helpers - typeof";return _a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_a(t)}function Za(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Oa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Za(Object(r),!0).forEach((function(e){Sa(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Za(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Sa(t,e,r){e=ka(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ka(t){var e=ja(t,"string");return _a(e)==="symbol"?e:String(e)}function ja(t,e){if(_a(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(_a(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ea(){Ea=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ea.apply(this,arguments)}function Ca(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ca=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==_a(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Aa(t){return La(t)||Wa(t)||Ja(t)||Pa()}function Pa(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Wa(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function La(t){if(Array.isArray(t))return Na(t)}function Ia(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Da(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Ia(i,n,o,a,u,"next",t)}function u(t){Ia(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Ta(t,e){return Ba(t)||Fa(t,e)||Ja(t,e)||Ma()}function Ma(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ja(t,e){if(!t)return;if(typeof t==="string")return Na(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Na(t,e)}function Na(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Fa(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ba(t){if(Array.isArray(t))return t}var za=[{label:(0,a.__)("None","tutor"),value:"none",image:Bi},{label:(0,a.__)("Photo","tutor"),value:"photo",image:Ri},{label:(0,a.__)("Neon","tutor"),value:"neon",image:Fi},{label:(0,a.__)("3D","tutor"),value:"3d",image:Ii},{label:(0,a.__)("Painting","tutor"),value:"painting",image:zi},{label:(0,a.__)("Sketch","tutor"),value:"sketch",image:Gi},{label:(0,a.__)("Concept","tutor"),value:"concept_art",image:Ti},{label:(0,a.__)("Illustration","tutor"),value:"illustration",image:Ni},{label:(0,a.__)("Dreamy","tutor"),value:"dreamy",image:Mi},{label:(0,a.__)("Filmic","tutor"),value:"filmic",image:Ji},{label:(0,a.__)("Retro","tutor"),value:"retrowave",image:Ui},{label:(0,a.__)("Black & White","tutor"),value:"black-and-white",image:Di}];var Ra=function t(){var e=(0,p.cI)({defaultValues:{style:"none",prompt:""}});var r=Ci(),o=r.images,i=r.setImages;var l=pr();var c=(0,Ot.p)(),s=c.showToast;var f=(0,u.useState)(o.every((function(t){return t===null}))),v=Ta(f,2),h=v[0],y=v[1];var b=(0,u.useState)([false,false,false,false]),g=Ta(b,2),m=g[0],w=g[1];var x=e.watch("style");var _=e.watch("prompt");var Z=!x||!_;var O=o.some(E.$K);(0,u.useEffect)((function(){if(l.isError){s({type:"danger",message:l.error.response.data.message})}}),[l.isError]);(0,u.useEffect)((function(){e.setFocus("prompt")}),[]);return(0,n.tZ)("form",{css:xa.wrapper,onSubmit:e.handleSubmit(function(){var t=Da(Ca().mark((function t(e){return Ca().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:w([true,true,true,true]);y(false);r.prev=2;r.next=5;return Promise.all(Array.from({length:4}).map((function(t,r){return l.mutateAsync(e).then((function(t){i((function(e){var n,o,i;var a=Aa(e);a[r]=(n=(o=t.data.data)===null||o===void 0?void 0:(i=o[0])===null||i===void 0?void 0:i.b64_json)!==null&&n!==void 0?n:null;return a}));w((function(t){var e=Aa(t);e[r]=false;return e}))}))["catch"]((function(t){w((function(t){var e=Aa(t);e[r]=false;return e}));throw t}))})));case 5:r.next=11;break;case 7:r.prev=7;r.t0=r["catch"](2);w([false,false,false,false]);y(true);case 11:case"end":return r.stop()}}),t,null,[[2,7]])})));return function(e){return t.apply(this,arguments)}}())},(0,n.tZ)("div",{css:xa.left},(0,n.tZ)(k.Z,{when:!h,fallback:(0,n.tZ)(d.Z,{name:"magicAiPlaceholder",width:72,height:72})},(0,n.tZ)("div",{css:Ua.images},(0,n.tZ)(le,{each:o},(function(t,e){return(0,n.tZ)(va,{key:e,src:t,loading:m[e],index:e})}))))),(0,n.tZ)("div",{css:xa.right},(0,n.tZ)("div",{css:Ua.fields},(0,n.tZ)("div",{css:Ua.promptWrapper},(0,n.tZ)(p.Qr,{control:e.control,name:"prompt",render:function t(e){return(0,n.tZ)(oe,Ea({},e,{label:(0,a.__)("Visualize Your Course","tutor"),placeholder:(0,a.__)("Describe the image you want for your course thumbnail","tutor"),rows:4,isMagicAi:true,disabled:l.isPending,enableResize:false}))}}),(0,n.tZ)("button",{type:"button",css:Ua.inspireButton,onClick:function t(){var r=ji.length;var n=Math.floor(Math.random()*r);e.reset(Oa(Oa({},e.getValues()),{},{prompt:ji[n]}))},disabled:l.isPending},(0,n.tZ)(d.Z,{name:"bulbLine"}),(0,a.__)("Inspire Me","tutor"))),(0,n.tZ)(p.Qr,{control:e.control,name:"style",render:function t(e){return(0,n.tZ)(Wi,Ea({},e,{label:(0,a.__)("Styles","tutor"),options:za,disabled:l.isPending}))}})),(0,n.tZ)("div",{css:xa.rightFooter},(0,n.tZ)(Ht,{type:"submit",disabled:l.isPending||Z},(0,n.tZ)(d.Z,{name:O?"reload":"magicAi",width:24,height:24}),O?(0,a.__)("Generate Again","tutor"):(0,a.__)("Generate Now","tutor")))))};var Ua={images:(0,n.iv)("display:grid;grid-template-columns:repeat(2, minmax(150px, 1fr));grid-template-rows:repeat(2, minmax(150px, 1fr));gap:",h.W0[12],";align-self:start;padding:",h.W0[24],";width:100%;height:100%;>div{aspect-ratio:1/1;}"+(true?"":0),true?"":0),fields:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),promptWrapper:(0,n.iv)("position:relative;textarea{padding-bottom:",h.W0[40],"!important;}"+(true?"":0),true?"":0),inspireButton:(0,n.iv)(Pt.i.resetButton,";",y.c.small(),";position:absolute;height:28px;bottom:",h.W0[12],";left:",h.W0[12],";border:1px solid ",h.Jv.stroke.brand,";border-radius:",h.E0[4],";display:flex;align-items:center;gap:",h.W0[4],";color:",h.Jv.text.brand,";padding-inline:",h.W0[12],";background-color:",h.Jv.background.white,";&:hover{background-color:",h.Jv.background.brand,";color:",h.Jv.text.white,";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{background-color:",h.Jv.background.disable,";color:",h.Jv.text.disable,";}"+(true?"":0),true?"":0)};function Ga(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Qa=l().forwardRef((function(t,e){var r=t.className,o=t.variant;return(0,n.tZ)("div",{className:r,ref:e,css:qa({variant:o})})}));Qa.displayName="Separator";var Ya={horizontal:true?{name:"d6rgw1",styles:"height:1px;width:100%"}:0,vertical:true?{name:"cw4fps",styles:"height:100%;width:1px"}:0,base:(0,n.iv)("flex-shrink:0;background-color:",h.Jv.stroke.divider,";"+(true?"":0),true?"":0)};var qa=(0,zt.Y)({variants:{variant:{horizontal:Ya.horizontal,vertical:Ya.vertical}},defaultVariants:{variant:"horizontal"}},Ya.base);function Ha(t,e){return tu(t)||Xa(t,e)||$a(t,e)||Va()}function Va(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $a(t,e){if(!t)return;if(typeof t==="string")return Ka(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ka(t,e)}function Ka(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Xa(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function tu(t){if(Array.isArray(t))return t}function eu(t,e,r,n){if(!e.current){return 0}var o=e.current.getBoundingClientRect();var i=o.width;var a=t-o.left;var u=Math.max(0,Math.min(a,i));var l=u/i*100;var c=Math.floor(r+l/100*(n-r));return c}var ru=function t(e){var r=e.field,o=e.fieldState,i=e.label,a=e.min,l=a===void 0?0:a,c=e.max,s=c===void 0?100:c,d=e.isMagicAi,f=d===void 0?false:d,p=e.hasBorder,v=p===void 0?false:p;var h=(0,u.useRef)(null);var y=(0,u.useState)(r.value),g=Ha(y,2),m=g[0],w=g[1];var x=(0,u.useRef)(null);var _=(0,u.useRef)(null);var Z=Y(m);(0,u.useEffect)((function(){r.onChange(Z)}),[Z,r.onChange]);(0,u.useEffect)((function(){var t=false;var e=function e(r){if(r.target!==_.current){return}t=true;document.body.style.userSelect="none"};var r=function e(r){if(!t||!x.current){return}w(eu(r.clientX,x,l,s))};var n=function e(){t=false;document.body.style.userSelect="auto"};window.addEventListener("mousedown",e);window.addEventListener("mousemove",r);window.addEventListener("mouseup",n);return function(){window.removeEventListener("mousedown",e);window.removeEventListener("mousemove",r);window.removeEventListener("mouseup",n)}}),[l,s]);var O=(0,u.useMemo)((function(){return Math.floor((m-l)/(s-l)*100)}),[m,l,s]);return(0,n.tZ)(Dt,{field:r,fieldState:o,label:i,isMagicAi:f},(function(){return(0,n.tZ)("div",{css:ou.wrapper(v)},(0,n.tZ)("div",{css:ou.track,ref:x,onKeyDown:b.ZT,onClick:function t(e){w(eu(e.clientX,x,l,s))}},(0,n.tZ)("div",{css:ou.fill,style:{width:"".concat(O,"%")}}),(0,n.tZ)("div",{css:ou.thumb(f),style:{left:"".concat(O,"%")},ref:_})),(0,n.tZ)("input",{type:"text",css:ou.input,value:String(m),onChange:function t(e){w(Number(e.target.value))},ref:h,onFocus:function t(){var e;(e=h.current)===null||e===void 0?void 0:e.select()}}))}))};const nu=ru;var ou={wrapper:function t(e){return(0,n.iv)("display:grid;grid-template-columns:1fr 45px;gap:",h.W0[20],";align-items:center;",e&&(0,n.iv)("border:1px solid ",h.Jv.stroke.disable,";border-radius:",h.E0[6],";padding:",h.W0[12]," ",h.W0[10]," ",h.W0[12]," ",h.W0[16],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},track:(0,n.iv)("position:relative;height:4px;background-color:",h.Jv.bg.gray20,";border-radius:",h.E0[50],";width:100%;flex-shrink:0;cursor:pointer;"+(true?"":0),true?"":0),fill:(0,n.iv)("position:absolute;left:0;top:0;height:100%;background:",h.Jv.ai.gradient_1,";width:50%;border-radius:",h.E0[50],";"+(true?"":0),true?"":0),thumb:function t(e){return(0,n.iv)("position:absolute;top:50%;transform:translate(-50%, -50%);width:20px;height:20px;border-radius:",h.E0.circle,";&::before{content:'';position:absolute;top:50%;left:50%;width:8px;height:8px;transform:translate(-50%, -50%);border-radius:",h.E0.circle,";background-color:",h.Jv.background.white,";cursor:pointer;}",e&&(0,n.iv)("background:",h.Jv.ai.gradient_1,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},input:(0,n.iv)(y.c.caption("medium"),";height:32px;border:1px solid ",h.Jv.stroke.border,";border-radius:",h.E0[6],";text-align:center;color:",h.Jv.text.primary,";&:focus-visible{",Pt.i.inputFocus,";}"+(true?"":0),true?"":0)};function iu(t){return lu(t)||uu(t)||du(t)||au()}function au(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function uu(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function lu(t){if(Array.isArray(t))return fu(t)}function cu(t,e){return vu(t)||pu(t,e)||du(t,e)||su()}function su(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function du(t,e){if(!t)return;if(typeof t==="string")return fu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fu(t,e)}function fu(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pu(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function vu(t){if(Array.isArray(t))return t}function hu(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var yu=l().forwardRef((function(t,e){var r=t.src,o=t.width,i=t.height,a=t.brushSize,l=t.trackStack,c=t.pointer,s=t.setTrackStack,d=t.setPointer;var f=(0,u.useState)(false),p=cu(f,2),v=p[0],h=p[1];var y=(0,u.useState)({x:0,y:0}),b=cu(y,2),g=b[0],m=b[1];var w=(0,u.useRef)(null);var x=function t(r){var n=$i(e),o=n.canvas,i=n.context;if(!o||!i){return}var a=o.getBoundingClientRect();var u=(r.clientX-a.left)*(o.width/a.width);var l=(r.clientY-a.top)*(o.height/a.height);i.globalCompositeOperation="destination-out";i.beginPath();i.moveTo(u,l);h(true);m({x:u,y:l})};var _=function t(r){var n=$i(e),o=n.canvas,i=n.context;if(!o||!i||!w.current){return}var a=o.getBoundingClientRect();var u={x:(r.clientX-a.left)*(o.width/a.width),y:(r.clientY-a.top)*(o.height/a.height)};if(v){Qi(i,u)}w.current.style.left="".concat(u.x,"px");w.current.style.top="".concat(u.y,"px")};var Z=function t(r){var n=$i(e),o=n.canvas,i=n.context;if(!i||!o){return}h(false);i.closePath();var a=o.getBoundingClientRect();var u={x:(r.clientX-a.left)*(o.width/a.width),y:(r.clientY-a.top)*(o.height/a.height)};if(Yi(g,u)===0){Qi(i,{x:u.x+1,y:u.y+1})}s((function(t){var e=t.slice(0,c);return[].concat(iu(e),[i.getImageData(0,0,1024,1024)])}));d((function(t){return t+1}))};var O=function t(){var n=$i(e),o=n.canvas,i=n.context;if(!o||!i){return}var a=new Image;a.src=r;a.onload=function(){i.clearRect(0,0,o.width,o.height);var t=a.width/a.height;var e=o.width/o.height;var r;var n;if(e>t){n=o.height;r=o.height*t}else{r=o.width;n=o.width/t}var u=(o.width-r)/2;var c=(o.height-n)/2;i.drawImage(a,u,c,r,n);if(l.length===0){s([i.getImageData(0,0,o.width,o.height)])}};i.lineJoin="round";i.lineCap="round"};var S=function t(){if(!w.current){return}document.body.style.cursor="none";w.current.style.display="block"};var k=function t(){if(!w.current){return}document.body.style.cursor="auto";w.current.style.display="none"};(0,u.useEffect)((function(){O()}),[]);return(0,n.tZ)("div",{css:bu.wrapper},(0,n.tZ)("canvas",{ref:e,width:o,height:i,onMouseDown:x,onMouseMove:_,onMouseUp:Z,onMouseEnter:S,onMouseLeave:k}),(0,n.tZ)("div",{ref:w,css:bu.customCursor(a)}))}));var bu={wrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,customCursor:function t(e){return(0,n.iv)("position:absolute;width:",e,"px;height:",e,"px;border-radius:",h.E0.circle,";background:linear-gradient(\n      73.09deg,\n      rgba(255, 150, 69, 0.4) 18.05%,\n      rgba(255, 100, 113, 0.4) 30.25%,\n      rgba(207, 110, 189, 0.4) 55.42%,\n      rgba(164, 119, 209, 0.4) 71.66%,\n      rgba(62, 100, 222, 0.4) 97.9%\n    );border:3px solid ",h.Jv.stroke.white,";pointer-events:none;transform:translate(-50%, -50%);z-index:",h.W5.highest,";display:none;"+(true?"":0),true?"":0)}};function gu(t){"@babel/helpers - typeof";return gu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gu(t)}var mu,wu;function xu(t,e){if(!e){e=t.slice(0)}return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function _u(){_u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return _u.apply(this,arguments)}function Zu(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Zu=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==gu(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Ou(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Su(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Ou(i,n,o,a,u,"next",t)}function u(t){Ou(i,n,o,a,u,"throw",t)}a(undefined)}))}}function ku(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Cu(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var o=function t(){};return{s:o,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();i=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!i&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function ju(t,e){return Wu(t)||Pu(t,e)||Cu(t,e)||Eu()}function Eu(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Cu(t,e){if(!t)return;if(typeof t==="string")return Au(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Au(t,e)}function Au(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Pu(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Wu(t){if(Array.isArray(t))return t}function Lu(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Iu=620;var Du=620;var Tu=true?{name:"xdvdnl",styles:"margin-top:auto"}:0;var Mu=true?{name:"innv7",styles:"min-height:16px"}:0;var Ju=function t(){var e=at({defaultValues:{brush_size:40,prompt:""}});var r=hr();var o=(0,u.useRef)(null);var i=Ci(),l=i.onDropdownMenuChange,c=i.currentImage,s=i.field,f=i.onCloseModal;var v=xr();var h=Y(e.watch("brush_size",40));var y=(0,u.useState)([]),g=ju(y,2),m=g[0],w=g[1];var x=(0,u.useState)(1),_=ju(x,2),Z=_[0],O=_[1];var S=(0,u.useCallback)((function(t,e){var r;var n=(r=o.current)===null||r===void 0?void 0:r.getContext("2d");if(!n){return}var i=ku(e.slice(0,t)),a;try{for(i.s();!(a=i.n()).done;){var u=a.value;n.putImageData(u,0,0)}}catch(t){i.e(t)}finally{i.f()}}),[]);(0,u.useEffect)((function(){var t;var e=(t=o.current)===null||t===void 0?void 0:t.getContext("2d");if(!e){return}e.lineWidth=h}),[h]);(0,u.useEffect)((function(){var t=function t(e){if(e.metaKey){if(e.shiftKey&&e.key.toUpperCase()==="Z"){S(Z+1,m);O((function(t){return Math.min(t+1,m.length)}));return}if(e.key.toUpperCase()==="Z"){S(Z-1,m);O((function(t){return Math.max(t-1,1)}));return}}};window.addEventListener("keydown",t);return function(){window.removeEventListener("keydown",t)}}),[Z,m,S]);if(!c){return null}return(0,n.tZ)("form",{css:xa.wrapper,onSubmit:e.handleSubmit(function(){var t=Su(Zu().mark((function t(e){var n,i,a,u,l;return Zu().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:n=o.current;i=n===null||n===void 0?void 0:n.getContext("2d");if(!(!n||!i)){c.next=4;break}return c.abrupt("return");case 4:a={prompt:e.prompt,image:Ki(n)};c.next=7;return r.mutateAsync(a);case 7:u=c.sent;if(u){l=new Image;l.onload=function(){n.width=Iu;n.height=Du;i.drawImage(l,0,0,n.width,n.height);i.lineWidth=h;i.lineJoin="round";i.lineCap="round"};l.src=u}case 9:case"end":return c.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},(0,n.tZ)("div",{css:xa.left},(0,n.tZ)("div",{css:Bu.leftWrapper},(0,n.tZ)("div",{css:Bu.actionBar},(0,n.tZ)("div",{css:Bu.backButtonWrapper},(0,n.tZ)("button",{type:"button",css:Bu.backButton,onClick:function t(){return l("generation")}},(0,n.tZ)(d.Z,{name:"arrowLeft"})),(0,a.__)("Magic Fill","tutor")),(0,n.tZ)("div",{css:Bu.actions},(0,n.tZ)(Ht,{variant:"ghost",disabled:m.length===0,onClick:function t(){S(1,m);w(m.slice(0,1));O(1)}},(0,a.__)("Revert to Original","tutor")),(0,n.tZ)(Qa,{variant:"vertical",css:Mu}),(0,n.tZ)("div",{css:Bu.undoRedo},(0,n.tZ)(Ht,{variant:"ghost",size:"icon",disabled:Z<=1,onClick:function t(){S(Z-1,m);O((function(t){return Math.max(t-1,1)}))}},(0,n.tZ)(d.Z,{name:"undo",width:20,height:20})),(0,n.tZ)(Ht,{variant:"ghost",size:"icon",disabled:Z===m.length,onClick:function t(){S(Z+1,m);O((function(t){return Math.min(t+1,m.length)}))}},(0,n.tZ)(d.Z,{name:"redo",width:20,height:20}))))),(0,n.tZ)("div",{css:Bu.canvasAndLoading},(0,n.tZ)(yu,{ref:o,width:Iu,height:Du,src:c,brushSize:h,trackStack:m,pointer:Z,setTrackStack:w,setPointer:O}),(0,n.tZ)(k.Z,{when:r.isPending},(0,n.tZ)("div",{css:Bu.loading}))),(0,n.tZ)("div",{css:Bu.footerActions},(0,n.tZ)("div",{css:Bu.footerActionsLeft},(0,n.tZ)(Ht,{variant:"secondary",onClick:function t(){var e="".concat((0,b.x0)(),".png");var r=$i(o),n=r.canvas;if(!n)return;Hi(Ki(n),e)}},(0,n.tZ)(d.Z,{name:"download",width:24,height:24})))))),(0,n.tZ)("div",{css:xa.right},(0,n.tZ)("div",{css:Bu.fields},(0,n.tZ)(p.Qr,{control:e.control,name:"brush_size",render:function t(e){return(0,n.tZ)(nu,_u({},e,{label:"Brush Size",min:1,max:100,isMagicAi:true,hasBorder:true}))}}),(0,n.tZ)(p.Qr,{control:e.control,name:"prompt",render:function t(e){return(0,n.tZ)(oe,_u({},e,{label:(0,a.__)("Describe the Fill","tutor"),placeholder:(0,a.__)("Write 5 words to describe...","tutor"),rows:4,isMagicAi:true}))}})),(0,n.tZ)("div",{css:[xa.rightFooter,Tu,true?"":0,true?"":0]},(0,n.tZ)("div",{css:Bu.footerButtons},(0,n.tZ)(Ht,{type:"submit",disabled:r.isPending||!e.watch("prompt")},(0,n.tZ)(d.Z,{name:"magicWand",width:24,height:24}),(0,a.__)("Generative Erase","tutor")),(0,n.tZ)(Ht,{variant:"primary_outline",disabled:r.isPending,loading:v.isPending,onClick:Su(Zu().mark((function t(){var e,r,n;return Zu().wrap((function t(i){while(1)switch(i.prev=i.next){case 0:e=$i(o),r=e.canvas;if(r){i.next=3;break}return i.abrupt("return");case 3:i.next=5;return v.mutateAsync({image:Ki(r)});case 5:n=i.sent;if(n.data){s.onChange(n.data);f()}case 7:case"end":return i.stop()}}),t)})))},(0,a.__)("Use Image","tutor"))))))};const Nu=Ju;var Fu={loading:(0,n.F4)(mu||(mu=xu(["\n    0% {\n      opacity: 0;\n    }\n    50% {\n      opacity: 0.6;\n    }\n    100% {\n      opacity: 0;\n    }\n  "]))),walker:(0,n.F4)(wu||(wu=xu(["\n    0% {\n      left: 0%;\n    }\n    100% {\n      left: 100%;\n    }\n  "])))};var Bu={canvasAndLoading:(0,n.iv)("position:relative;z-index:",h.W5.positive,";"+(true?"":0),true?"":0),loading:(0,n.iv)("position:absolute;top:0;left:0;width:100%;height:100%;background:",h.Jv.ai.gradient_1,";opacity:0.6;transition:0.5s ease opacity;animation:",Fu.loading," 1s linear infinite;z-index:0;&::before{content:'';position:absolute;top:0;left:0;width:200px;height:100%;background:linear-gradient(\n        270deg,\n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 0.6) 51.13%,\n        rgba(255, 255, 255, 0) 100%\n      );animation:",Fu.walker," 1s linear infinite;}"+(true?"":0),true?"":0),actionBar:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,fields:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),leftWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";padding-block:",h.W0[16],";"+(true?"":0),true?"":0),footerButtons:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),footerActions:true?{name:"1eoy87d",styles:"display:flex;justify-content:space-between"}:0,footerActionsLeft:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[12],";"+(true?"":0),true?"":0),actions:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[16],";"+(true?"":0),true?"":0),undoRedo:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[12],";"+(true?"":0),true?"":0),backButtonWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[8],";",y.c.body("medium"),";color:",h.Jv.text.title,";"+(true?"":0),true?"":0),backButton:(0,n.iv)(Pt.i.resetButton,";width:24px;height:24px;border-radius:",h.E0[4],";border:1px solid ",h.Jv.stroke["default"],";display:flex;align-items:center;justify-content:center;"+(true?"":0),true?"":0),image:true?{name:"gb1um3",styles:"width:492px;height:498px;position:relative;img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}"}:0,canvasWrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,customCursor:function t(e){return(0,n.iv)("position:absolute;width:",e,"px;height:",e,"px;border-radius:",h.E0.circle,";background:linear-gradient(\n      73.09deg,\n      rgba(255, 150, 69, 0.4) 18.05%,\n      rgba(255, 100, 113, 0.4) 30.25%,\n      rgba(207, 110, 189, 0.4) 55.42%,\n      rgba(164, 119, 209, 0.4) 71.66%,\n      rgba(62, 100, 222, 0.4) 97.9%\n    );border:3px solid ",h.Jv.stroke.white,";pointer-events:none;transform:translate(-50%, -50%);z-index:",h.W5.highest,";display:none;"+(true?"":0),true?"":0)}};function zu(){var t=Ci(),e=t.state;switch(e){case"generation":return(0,n.tZ)(Ra,null);case"magic-fill":return(0,n.tZ)(Nu,null);default:return null}}var Ru=function t(e){var r=e.title,o=e.icon,i=e.closeModal,a=e.field,u=e.fieldState;return(0,n.tZ)(Ir.Z,{onClose:i,title:r,icon:o,maxWidth:1e3},(0,n.tZ)(Ai,{field:a,fieldState:u,onCloseModal:i},(0,n.tZ)(zu,null)))};const Uu=Ru;function Gu(t){"@babel/helpers - typeof";return Gu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gu(t)}function Qu(t){return Hu(t)||qu(t)||Ku(t)||Yu()}function Yu(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function qu(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Hu(t){if(Array.isArray(t))return Xu(t)}function Vu(t,e){return el(t)||tl(t,e)||Ku(t,e)||$u()}function $u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ku(t,e){if(!t)return;if(typeof t==="string")return Xu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xu(t,e)}function Xu(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function tl(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function el(t){if(Array.isArray(t))return t}function rl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function nl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rl(Object(r),!0).forEach((function(e){ol(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ol(t,e,r){e=il(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function il(t){var e=al(t,"string");return Gu(e)==="symbol"?e:String(e)}function al(t,e){if(Gu(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Gu(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ul=function t(e){var r=e.options,n=r===void 0?{}:r,o=e.onChange,i=e.initialFiles;var l=(0,Ot.p)(),c=l.showToast;var s=(0,u.useMemo)((function(){return i?Array.isArray(i)?i:[i]:[]}),[i]);var d=(0,u.useMemo)((function(){return nl(nl(nl({},n),n.type?{library:{type:n.type}}:{}),{},{multiple:n.multiple?n.multiple===true?"add":n.multiple:false})}),[n]);var f=(0,u.useState)(s),p=Vu(f,2),v=p[0],h=p[1];(0,u.useEffect)((function(){if(s&&!v.length){h(s)}}),[v,s]);var y=(0,u.useCallback)((function(){var t;if(!((t=window.wp)!==null&&t!==void 0&&t.media)){console.error("WordPress media library is not available");return}var e=window.wp.media(d);e.on("close",(function(){if(e.$el){e.$el.parent().parent().remove()}}));e.on("open",(function(){var t=e.state().get("selection");e.$el.attr("data-focus-trap","true");t.reset();v.forEach((function(e){var r=window.wp.media.attachment(e.id);if(r){r.fetch();t.add(r)}}))}));e.on("select",(function(){var t=e.state().get("selection").toJSON();var r=new Set(t.map((function(t){return t.id})));var n=v.filter((function(t){return r.has(t.id)}));var i=t.reduce((function(t,e){if(n.some((function(t){return t.id===e.id}))){return t}if(d.maxFileSize&&e.filesizeInBytes>d.maxFileSize){c({message:(0,a.sprintf)((0,a.__)("%s size exceeds the maximum allowed size","tutor"),e.title),type:"danger"});return t}var r={id:e.id,title:e.title,url:e.url,name:e.title,size:e.filesizeHumanReadable,size_bytes:e.filesizeInBytes,ext:e.filename.split(".").pop()||""};t.push(r);return t}),[]);var u=d.multiple?[].concat(Qu(n),Qu(i)):i.slice(0,1);if(d.maxFiles&&u.length>d.maxFiles){c({message:(0,a.sprintf)((0,a.__)("Cannot select more than %d files","tutor"),d.maxFiles),type:"warning"});return}h(u);o===null||o===void 0?void 0:o(d.multiple?u:u[0]||null);e.close()}));e.open()}),[d,o,v,c]);var b=(0,u.useCallback)((function(){h([]);o===null||o===void 0?void 0:o(d.multiple?[]:null)}),[d.multiple,o]);return{openMediaLibrary:y,existingFiles:v,resetFiles:b}};const ll=ul;const cl=r.p+"images/e67e28356e87045281d41cd6583f5c41-generate-image-2x.webp";const sl=r.p+"images/9c13bda85170ee68f15380378d920fd1-generate-image.webp";var dl;var fl=!!j.y.tutor_pro_url;var pl=(dl=j.y.settings)===null||dl===void 0?void 0:dl.chatgpt_key_exist;var vl=function t(e){var r=e.field,o=e.fieldState,i=e.label,u=e.size,l=e.helpText,c=e.buttonText,s=c===void 0?(0,a.__)("Upload Media","tutor"):c,f=e.infoText,p=e.onChange,v=e.generateWithAi,h=v===void 0?false:v,y=e.previewImageCss,b=e.loading,g=e.onClickAiButton;var m=(0,un.d)(),w=m.showModal;var x=ll({options:{type:"image",multiple:false},onChange:function t(e){if(e&&!Array.isArray(e)){var n=e.id,o=e.url,i=e.title;r.onChange({id:n,url:o,title:i});if(p){p({id:n,url:o,title:i})}}},initialFiles:r.value}),_=x.openMediaLibrary,Z=x.resetFiles;var O=r.value;var S=function t(){_()};var k=function t(){Z();r.onChange(null);if(p){p(null)}};var j=function t(){if(!fl){w({component:dn,props:{image:sl,image2x:cl}})}else if(!pl){w({component:to,props:{image:sl,image2x:cl}})}else{w({component:Uu,isMagicAi:true,props:{title:(0,a.__)("AI Studio","tutor"),icon:(0,n.tZ)(d.Z,{name:"magicAiColorize",width:24,height:24}),field:r,fieldState:o}});g===null||g===void 0?void 0:g()}};return(0,n.tZ)(Dt,{label:i,field:r,fieldState:o,helpText:l,onClickAiButton:j,generateWithAi:h},(function(){return(0,n.tZ)("div",null,(0,n.tZ)(bi,{size:u,value:O,uploadHandler:S,clearHandler:k,buttonText:s,infoText:f,previewImageCss:y,loading:b}))}))};const hl=F(vl);var yl=["css"];function bl(){bl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return bl.apply(this,arguments)}function gl(t,e){if(t==null)return{};var r=ml(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function ml(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}var wl=function t(e){var r=e.label,o=e.content,i=e.contentPosition,a=i===void 0?"left":i,l=e.showVerticalBar,c=l===void 0?true:l,s=e.size,d=s===void 0?"regular":s,f=e.type,p=f===void 0?"text":f,v=e.field,h=e.fieldState,y=e.disabled,b=e.readOnly,g=e.loading,m=e.placeholder,w=e.helpText,x=e.onChange,_=e.onKeyDown,Z=e.isHidden,O=e.wrapperCss,S=e.contentCss,k=e.removeBorder,j=k===void 0?false:k,E=e.selectOnFocus,C=E===void 0?false:E;var A=(0,u.useRef)(null);return(0,n.tZ)(Dt,{label:r,field:v,fieldState:h,disabled:y,readOnly:b,loading:g,placeholder:m,helpText:w,isHidden:Z,removeBorder:j},(function(t){var e;var r=t.css,i=gl(t,yl);return(0,n.tZ)("div",{css:[_l.inputWrapper(!!h.error,j),O,true?"":0,true?"":0]},a==="left"&&(0,n.tZ)("div",{css:[_l.inputLeftContent(c,d),S,true?"":0,true?"":0]},o),(0,n.tZ)("input",bl({},v,i,{type:"text",value:(e=v.value)!==null&&e!==void 0?e:"",onChange:function t(e){var r=p==="number"?e.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):e.target.value;v.onChange(p==="number"?Number(r):r);if(x){x(r)}},onKeyDown:function t(e){return _===null||_===void 0?void 0:_(e.key)},css:[r,_l.input(a,c,d),true?"":0,true?"":0],autoComplete:"off",ref:function t(e){v.ref(e);A.current=e},onFocus:function t(){if(!C||!A.current){return}A.current.select()},"data-input":true})),a==="right"&&(0,n.tZ)("div",{css:[_l.inputRightContent(c,d),S,true?"":0,true?"":0]},o))}))};const xl=F(wl);var _l={inputWrapper:function t(e,r){return(0,n.iv)("display:flex;align-items:center;",!r&&(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[6],";box-shadow:",h.AF.input,";background-color:",h.Jv.background.white,";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",Pt.i.inputFocus,";",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function t(e,r,o){return(0,n.iv)("&[data-input]{",y.c.body(),";border:none;box-shadow:none;background-color:transparent;padding-",e,":0;",r&&(0,n.iv)("padding-",e,":",h.W0[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.iv)("font-size:",h.JB[24],";font-weight:",h.Ue.medium,";height:34px;",r&&(0,n.iv)("padding-",e,":",h.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},inputLeftContent:function t(e,r){return(0,n.iv)(y.c.small()," ",Pt.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(y.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-right:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function t(e,r){return(0,n.iv)(y.c.small()," ",Pt.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(y.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-left:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var Zl=r(5298);var Ol=r(5253);var Sl=r(7274);var kl=r(1533);var jl=r(2352);var El=r(4101);var Cl=r(7363);function Al(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Pl=function t(e){var r=e.children,o=e.onClose,i=e.title,a=e.subtitle,l=e.icon,c=e.headerChildren,s=e.entireHeader,f=e.actions,p=e.maxWidth,v=p===void 0?1218:p;(0,u.useEffect)((function(){document.body.style.overflow="hidden";return function(){document.body.style.overflow="initial"}}),[]);return(0,n.tZ)(El.Z,null,(0,n.tZ)("div",{css:Ll.container({maxWidth:v})},(0,n.tZ)("div",{css:Ll.header({hasHeaderChildren:!!c})},(0,n.tZ)(k.Z,{when:s,fallback:(0,n.tZ)(Cl.Fragment,null,(0,n.tZ)("div",{css:Ll.headerContent},(0,n.tZ)("div",{css:Ll.iconWithTitle},(0,n.tZ)(k.Z,{when:l},l),(0,n.tZ)(k.Z,{when:i},(0,n.tZ)("h6",{css:Ll.title,title:typeof i==="string"?i:""},i))),(0,n.tZ)(k.Z,{when:a},(0,n.tZ)("span",{css:Ll.subtitle},a))),(0,n.tZ)("div",{css:Ll.headerChildren},(0,n.tZ)(k.Z,{when:c},c)),(0,n.tZ)("div",{css:Ll.actionsWrapper},(0,n.tZ)(k.Z,{when:f,fallback:(0,n.tZ)("button",{type:"button",css:Ll.closeButton,onClick:o},(0,n.tZ)(d.Z,{name:"times",width:14,height:14}))},f)))},s)),(0,n.tZ)("div",{css:Ll.content},(0,n.tZ)(jl.Z,null,r))))};const Wl=Pl;var Ll={container:function t(e){var r=e.maxWidth;return(0,n.iv)("position:relative;background:",h.Jv.background.white,";margin:",S.oC.MARGIN_TOP,"px auto ",h.W0[24],";height:100%;max-width:",r,"px;box-shadow:",h.AF.modal,";border-radius:",h.E0[10],";overflow:hidden;bottom:0;z-index:",h.W5.modal,";width:100%;",h.Uo.smallTablet,"{width:90%;}"+(true?"":0),true?"":0)},header:function t(e){var r=e.hasHeaderChildren;return(0,n.iv)("display:grid;grid-template-columns:",r?"1fr auto 1fr":"1fr auto auto",";gap:",h.W0[8],";align-items:center;width:100%;height:",S.oC.HEADER_HEIGHT,"px;background:",h.Jv.background.white,";border-bottom:1px solid ",h.Jv.stroke.divider,";position:sticky;"+(true?"":0),true?"":0)},headerContent:(0,n.iv)("place-self:center start;display:inline-flex;align-items:center;gap:",h.W0[12],";padding-left:",h.W0[24],";",h.Uo.smallMobile,"{padding-left:",h.W0[16],";}"+(true?"":0),true?"":0),headerChildren:true?{name:"qdgqcx",styles:"place-self:center center"}:0,iconWithTitle:(0,n.iv)("display:inline-flex;align-items:center;gap:",h.W0[4],";flex-shrink:0;color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.heading6("medium"),";color:",h.Jv.text.title,";text-transform:none;letter-spacing:normal;"+(true?"":0),true?"":0),subtitle:(0,n.iv)(Pt.i.text.ellipsis(1)," ",y.c.caption(),";color:",h.Jv.text.hints,";padding-left:",h.W0[12],";border-left:1px solid ",h.Jv.icon.hints,";"+(true?"":0),true?"":0),actionsWrapper:(0,n.iv)("place-self:center end;display:inline-flex;gap:",h.W0[16],";padding-right:",h.W0[24],";",h.Uo.smallMobile,"{padding-right:",h.W0[16],";}"+(true?"":0),true?"":0),closeButton:(0,n.iv)(Pt.i.resetButton,";display:inline-flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",h.E0.circle,";background:",h.Jv.background.white,";&:focus,&:active,&:hover{background:",h.Jv.background.white,";}svg{color:",h.Jv.icon["default"],";transition:color 0.3s ease-in-out;}:hover{svg{color:",h.Jv.icon.hover,";}}:focus{box-shadow:",h.AF.focus,";}"+(true?"":0),true?"":0),content:(0,n.iv)("height:calc(100% - ",S.oC.HEADER_HEIGHT+S.oC.MARGIN_TOP,"px);background-color:",h.Jv.surface.courseBuilder,";overflow-x:hidden;",Pt.i.overflowYAuto,";"+(true?"":0),true?"":0)};const Il=r.p+"images/8883d834437ecd54063a38ba8ec0ef37-subscriptions-empty-state-2x.webp";const Dl=r.p+"images/026952ce6dfdf3da34dc55d99f241520-subscriptions-empty-state.webp";function Tl(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Ml=function t(e){var r=e.onCreateSubscription;return(0,n.tZ)("div",{css:Jl.wrapper},(0,n.tZ)("div",{css:Jl.banner},(0,n.tZ)("img",{src:Dl,srcSet:"".concat(Dl," ").concat(Il," 2x"),alt:(0,a.__)("Empty state banner","tutor")})),(0,n.tZ)("div",{css:Jl.content},(0,n.tZ)("h5",null,(0,a.__)("Boost Revenue with Subscriptions","tutor")),(0,n.tZ)("p",null,(0,a.__)("Offer flexible subscription plans to maximize your earnings and provide students with affordable access to your courses.","tutor"))),(0,n.tZ)("div",{css:Jl.action},(0,n.tZ)(v.Z,{variant:"secondary",icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),onClick:r},(0,a.__)("Add Subscription","tutor"))))};var Jl={wrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[32],";justify-content:center;max-width:640px;width:100%;padding-block:",h.W0[40],";margin-inline:auto;"+(true?"":0),true?"":0),content:(0,n.iv)("display:grid;gap:",h.W0[12],";text-align:center;max-width:566px;width:100%;margin:0 auto;h5{",y.c.heading5("medium"),";color:",h.Jv.text.primary,";}p{",y.c.caption(),";color:",h.Jv.text.hints,";}"+(true?"":0),true?"":0),action:true?{name:"zl1inp",styles:"display:flex;justify-content:center"}:0,banner:(0,n.iv)("width:100%;height:232px;background-color:",h.Jv.background.status.drip,";display:flex;align-items:center;justify-content:center;border-radius:",h.E0[8],";position:relative;overflow:hidden;img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}"+(true?"":0),true?"":0)};var Nl=r(3618);var Fl=r(6972);function Bl(t){"@babel/helpers - typeof";return Bl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bl(t)}function zl(t,e,r){e=Rl(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Rl(t){var e=Ul(t,"string");return Bl(e)==="symbol"?e:String(e)}function Ul(t,e){if(Bl(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Bl(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Gl=function t(e){var r,o,i,u,l,c;var s=e.arrow,d=e.triggerRef,f=e.isOpen,p=e.title,h=e.message,y=e.onConfirmation,b=e.onCancel,g=e.isLoading,m=g===void 0?false:g,w=e.gap,x=e.maxWidth,_=e.closePopover,Z=e.animationType,O=Z===void 0?ir.ru.slideLeft:Z,k=e.hideArrow,j=k===void 0?false:k,E=e.confirmButton,C=e.cancelButton,A=e.positionModifier;var P=(0,xt.l)({triggerRef:d,isOpen:f,arrow:s,gap:w,positionModifier:A}),W=P.position,L=P.triggerWidth,I=P.popoverRef;return(0,n.tZ)(xt.h,{isOpen:f,onClickOutside:_,animationType:O},(0,n.tZ)("div",{css:[Yl.wrapper(s?W.arrowPlacement:undefined,j),(r={},zl(r,S.dZ?"right":"left",W.left),zl(r,"top",W.top),zl(r,"maxWidth",x!==null&&x!==void 0?x:L),r),true?"":0,true?"":0],ref:I},(0,n.tZ)("div",{css:Yl.content},(0,n.tZ)("div",{css:Yl.body},(0,n.tZ)("div",{css:Yl.title},p),(0,n.tZ)("p",{css:Yl.description},h)),(0,n.tZ)("div",{css:Yl.footer({isDelete:(o=E===null||E===void 0?void 0:E.isDelete)!==null&&o!==void 0?o:false})},(0,n.tZ)(v.Z,{variant:(i=C===null||C===void 0?void 0:C.variant)!==null&&i!==void 0?i:"text",size:"small",onClick:b!==null&&b!==void 0?b:_},(u=C===null||C===void 0?void 0:C.text)!==null&&u!==void 0?u:(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{"data-cy":"confirm-button",variant:(l=E===null||E===void 0?void 0:E.variant)!==null&&l!==void 0?l:"text",onClick:function t(){y();_()},loading:m,size:"small"},(c=E===null||E===void 0?void 0:E.text)!==null&&c!==void 0?c:(0,a.__)("Ok","tutor"))))))};const Ql=Gl;var Yl={wrapper:function t(e,r){return(0,n.iv)("position:absolute;width:100%;z-index:",h.W5.dropdown,";&::before{",e&&!r&&(0,n.iv)("content:'';position:absolute;border:",h.W0[8]," solid transparent;",e==="left"&&Yl.arrowLeft," ",e==="right"&&Yl.arrowRight," ",e==="top"&&Yl.arrowTop," ",e==="bottom"&&Yl.arrowBottom,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},arrowLeft:(0,n.iv)("border-right-color:",h.Jv.surface.tutor,";top:50%;transform:translateY(-50%);left:-",h.W0[16],";"+(true?"":0),true?"":0),arrowRight:(0,n.iv)("border-left-color:",h.Jv.surface.tutor,";top:50%;transform:translateY(-50%);right:-",h.W0[16],";"+(true?"":0),true?"":0),arrowTop:(0,n.iv)("border-bottom-color:",h.Jv.surface.tutor,";left:50%;transform:translateX(-50%);top:-",h.W0[16],";"+(true?"":0),true?"":0),arrowBottom:(0,n.iv)("border-top-color:",h.Jv.surface.tutor,";left:50%;transform:translateX(-50%);bottom:-",h.W0[16],";"+(true?"":0),true?"":0),content:(0,n.iv)("background-color:",h.Jv.surface.tutor,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";::-webkit-scrollbar{background-color:",h.Jv.surface.tutor,";width:10px;}::-webkit-scrollbar-thumb{background-color:",h.Jv.action.secondary["default"],";border-radius:",h.E0[6],";}"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.small("medium"),";color:",h.Jv.text.primary,";"+(true?"":0),true?"":0),description:(0,n.iv)(y.c.small(),";color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),body:(0,n.iv)("padding:",h.W0[16]," ",h.W0[20]," ",h.W0[12],";",Pt.i.display.flex("column"),";gap:",h.W0[8],";"+(true?"":0),true?"":0),footer:function t(e){var r=e.isDelete,o=r===void 0?false:r;return(0,n.iv)(Pt.i.display.flex(),";padding:",h.W0[4]," ",h.W0[16]," ",h.W0[8],";justify-content:end;gap:",h.W0[10],";",o&&(0,n.iv)("button:last-of-type{color:",h.Jv.text.error,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var ql=["css"];function Hl(){Hl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Hl.apply(this,arguments)}function Vl(t,e){if(t==null)return{};var r=$l(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function $l(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}var Kl=function t(e){var r=e.field,o=e.fieldState,i=e.disabled,a=e.value,u=e.onChange,l=e.label,c=e.description,s=e.isHidden,d=e.labelCss;return(0,n.tZ)(Dt,{field:r,fieldState:o,isHidden:s},(function(t){var e=t.css,o=Vl(t,ql);return(0,n.tZ)("div",null,(0,n.tZ)(Z,Hl({},r,o,{inputCss:e,labelCss:d,value:a,disabled:i,checked:r.value,label:l,onChange:function t(){r.onChange(!r.value);if(u){u(!r.value)}}})),c&&(0,n.tZ)("p",{css:tc.description},c))}))};const Xl=Kl;var tc={description:(0,n.iv)(y.c.small()," color:",h.Jv.text.hints,";padding-left:30px;margin-top:",h.W0[6],";"+(true?"":0),true?"":0)};var ec=r(7363);function rc(t){"@babel/helpers - typeof";return rc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rc(t)}var nc=["css"];function oc(t,e,r){e=ic(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ic(t){var e=ac(t,"string");return rc(e)==="symbol"?e:String(e)}function ac(t,e){if(rc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(rc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function uc(){uc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return uc.apply(this,arguments)}function lc(t,e){if(t==null)return{};var r=cc(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function cc(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function sc(t,e){return hc(t)||vc(t,e)||fc(t,e)||dc()}function dc(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fc(t,e){if(!t)return;if(typeof t==="string")return pc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pc(t,e)}function pc(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function vc(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function hc(t){if(Array.isArray(t))return t}function yc(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var bc=function t(e){var r;var o=e.field,i=e.fieldState,a=e.content,l=e.contentPosition,c=l===void 0?"left":l,s=e.showVerticalBar,f=s===void 0?true:s,p=e.type,v=p===void 0?"text":p,h=e.size,y=h===void 0?"regular":h,b=e.label,g=e.placeholder,m=g===void 0?"":g,w=e.disabled,x=e.readOnly,_=e.loading,Z=e.helpText,O=e.removeOptionsMinWidth,j=O===void 0?true:O,E=e.onChange,C=e.presetOptions,A=C===void 0?[]:C,P=e.selectOnFocus,W=P===void 0?false:P,L=e.wrapperCss,I=e.contentCss,D=e.removeBorder,T=D===void 0?false:D;var M=(r=o.value)!==null&&r!==void 0?r:"";var J=(0,u.useRef)(null);var N=(0,u.useState)(false),F=sc(N,2),B=F[0],z=F[1];var R=(0,xt.l)({isOpen:B,isDropdown:true}),U=R.triggerRef,G=R.triggerWidth,Q=R.position,Y=R.popoverRef;return(0,n.tZ)(Dt,{fieldState:i,field:o,label:b,disabled:w,readOnly:x,loading:_,helpText:Z,removeBorder:T,placeholder:m},(function(t){var e;var r=t.css,u=lc(t,nc);return(0,n.tZ)(ec.Fragment,null,(0,n.tZ)("div",{css:[wc.inputWrapper(!!i.error,T),L,true?"":0,true?"":0],ref:U},a&&c==="left"&&(0,n.tZ)("div",{css:[wc.inputLeftContent(f,y),I,true?"":0,true?"":0]},a),(0,n.tZ)("input",uc({},u,{css:[r,wc.input(c,f,y),true?"":0,true?"":0],onClick:function t(){return z(true)},autoComplete:"off",readOnly:x,ref:function t(e){o.ref(e);J.current=e},onFocus:function t(){if(!W||!J.current){return}J.current.select()},value:M,onChange:function t(e){var r=v==="number"?e.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):e.target.value;o.onChange(r);if(E){E(r)}},"data-input":true})),a&&c==="right"&&(0,n.tZ)("div",{css:wc.inputRightContent(f,y)},a)),(0,n.tZ)(xt.h,{isOpen:B,onClickOutside:function t(){return z(false)},onEscape:function t(){return z(false)}},(0,n.tZ)("div",{css:[wc.optionsWrapper,(e={},oc(e,S.dZ?"right":"left",Q.left),oc(e,"top",Q.top),oc(e,"maxWidth",G),e),true?"":0,true?"":0],ref:Y},(0,n.tZ)("ul",{css:[wc.options(j),true?"":0,true?"":0]},A.map((function(t){return(0,n.tZ)("li",{key:String(t.value),css:wc.optionItem({isSelected:t.value===o.value})},(0,n.tZ)("button",{type:"button",css:wc.label,onClick:function e(){o.onChange(t.value);E===null||E===void 0?void 0:E(t.value);z(false)}},(0,n.tZ)(k.Z,{when:t.icon},(0,n.tZ)(d.Z,{name:t.icon,width:32,height:32})),(0,n.tZ)("span",null,t.label)))}))))))}))};const gc=bc;var mc=true?{name:"16gsvie",styles:"min-width:200px"}:0;var wc={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:function t(e,r){return(0,n.iv)("display:flex;align-items:center;",!r&&(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[6],";box-shadow:",h.AF.input,";background-color:",h.Jv.background.white,";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",Pt.i.inputFocus,";",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function t(e,r,o){return(0,n.iv)("&[data-input]{",y.c.body(),";border:none;box-shadow:none;background-color:transparent;padding-",e,":0;",r&&(0,n.iv)("padding-",e,":",h.W0[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.iv)("font-size:",h.JB[24],";font-weight:",h.Ue.medium,";height:34px;",r&&(0,n.iv)("padding-",e,":",h.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},label:(0,n.iv)(Pt.i.resetButton,";width:100%;height:100%;display:flex;align-items:center;gap:",h.W0[8],";margin:0 ",h.W0[12],";padding:",h.W0[6]," 0;text-align:left;line-height:",h.Nv[24],";word-break:break-all;cursor:pointer;span{flex-shrink:0;}"+(true?"":0),true?"":0),optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:function t(e){return(0,n.iv)("z-index:",h.W5.dropdown,";background-color:",h.Jv.background.white,";list-style-type:none;box-shadow:",h.AF.popover,";padding:",h.W0[4]," 0;margin:0;max-height:500px;border-radius:",h.E0[6],";",Pt.i.overflowYAuto,";",!e&&mc,";"+(true?"":0),true?"":0)},optionItem:function t(e){var r=e.isSelected,o=r===void 0?false:r;return(0,n.iv)(y.c.body(),";min-height:36px;height:100%;width:100%;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;cursor:pointer;&:hover{background-color:",h.Jv.background.hover,";}",o&&(0,n.iv)("background-color:",h.Jv.background.active,";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:3px;height:100%;background-color:",h.Jv.action.primary["default"],";border-radius:0 ",h.E0[6]," ",h.E0[6]," 0;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputLeftContent:function t(e,r){return(0,n.iv)(y.c.small()," ",Pt.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(y.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-right:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function t(e,r){return(0,n.iv)(y.c.small()," ",Pt.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(y.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-left:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var xc=r(546);var _c=r(5035);var Zc=r(6877);function Oc(t){"@babel/helpers - typeof";return Oc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Oc(t)}var Sc=["css"];function kc(t,e,r){e=jc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function jc(t){var e=Ec(t,"string");return Oc(e)==="symbol"?e:String(e)}function Ec(t,e){if(Oc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Oc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Cc(){Cc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Cc.apply(this,arguments)}function Ac(t,e){if(t==null)return{};var r=Pc(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function Pc(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function Wc(t,e){return Mc(t)||Tc(t,e)||Ic(t,e)||Lc()}function Lc(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ic(t,e){if(!t)return;if(typeof t==="string")return Dc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dc(t,e)}function Dc(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Tc(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Mc(t){if(Array.isArray(t))return t}function Jc(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Nc=function t(){if(!wp.date){return}var e=wp.date.format;return{formatMonthDropdown:function t(r){return e("F",r)},formatMonthCaption:function t(r){return e("F",r)},formatCaption:function t(r){return e("F",r)},formatWeekdayName:function t(r){return e("D",r)}}};var Fc=function t(e){if(!e)return undefined;return(0,An.Z)(new Date(e))?new Date(e.length===10?e+"T00:00:00":e):undefined};var Bc=function t(e){var r=e.label,o=e.field,i=e.fieldState,a=e.disabled,l=e.disabledBefore,c=e.disabledAfter,s=e.loading,f=e.placeholder,p=e.helpText,h=e.isClearable,y=h===void 0?true:h,b=e.onChange,g=e.dateFormat,m=g===void 0?S.E_.yearMonthDay:g;var w=(0,u.useRef)(null);var x=(0,u.useState)(false),_=Wc(x,2),Z=_[0],O=_[1];var k=Fc(o.value);var j=k?(0,xc.Z)(k,m):"";var E=(0,xt.l)({isOpen:Z,isDropdown:true}),C=E.triggerRef,A=E.position,P=E.popoverRef;var W=function t(){var e;O(false);(e=w.current)===null||e===void 0?void 0:e.focus()};var L=Fc(l);var I=Fc(c);return(0,n.tZ)(Dt,{label:r,field:o,fieldState:i,disabled:a,loading:s,placeholder:f,helpText:p},(function(t){var e,r;var i=t.css,a=Ac(t,Sc);return(0,n.tZ)("div",null,(0,n.tZ)("div",{css:Rc.wrapper,ref:C},(0,n.tZ)("input",Cc({},a,{css:[i,Rc.input,true?"":0,true?"":0],ref:function t(e){o.ref(e);w.current=e},type:"text",value:j,onClick:function t(e){e.stopPropagation();O((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();O((function(t){return!t}))}},autoComplete:"off","data-input":true})),(0,n.tZ)(d.Z,{name:"calendarLine",width:30,height:32,style:Rc.icon}),y&&o.value&&(0,n.tZ)(v.Z,{variant:"text",buttonCss:Rc.clearButton,onClick:function t(){o.onChange("")}},(0,n.tZ)(d.Z,{name:"times",width:12,height:12}))),(0,n.tZ)(xt.h,{isOpen:Z,onClickOutside:W,onEscape:W},(0,n.tZ)("div",{css:[Rc.pickerWrapper,(e={},kc(e,S.dZ?"right":"left",A.left),kc(e,"top",A.top),e),true?"":0,true?"":0],ref:P},(0,n.tZ)(_c._,{dir:S.dZ?"rtl":"ltr",animate:true,mode:"single",formatters:Nc(),disabled:[!!L&&{before:L},!!I&&{after:I}],selected:k,onSelect:function t(e){if(e){var r=(0,xc.Z)(e,S.E_.yearMonthDay);o.onChange(r);W();if(b){b(r)}}},showOutsideDays:true,captionLayout:"dropdown",autoFocus:true,defaultMonth:k||new Date,startMonth:L||new Date((new Date).getFullYear()-10,0),endMonth:I||new Date((new Date).getFullYear()+10,11),weekStartsOn:(r=wp.date)===null||r===void 0?void 0:r.getSettings().l10n.startOfWeek}))))}))};const zc=Bc;var Rc={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.iv)("&[data-input]{padding-left:",h.W0[40],";}"+(true?"":0),true?"":0),icon:(0,n.iv)("position:absolute;top:50%;left:",h.W0[8],";transform:translateY(-50%);color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),pickerWrapper:(0,n.iv)(y.c.body("regular"),";position:absolute;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";.rdp-root{--rdp-day-height:40px;--rdp-day-width:40px;--rdp-day_button-height:40px;--rdp-day_button-width:40px;--rdp-nav-height:40px;--rdp-today-color:",h.Jv.text.title,";--rdp-caption-font-size:",h.JB[18],";--rdp-accent-color:",h.Jv.action.primary["default"],";--rdp-background-color:",h.Jv.background.hover,";--rdp-accent-color-dark:",h.Jv.action.primary.active,";--rdp-background-color-dark:",h.Jv.action.primary.hover,";--rdp-selected-color:",h.Jv.text.white,";--rdp-day_button-border-radius:",h.E0.circle,";--rdp-outside-opacity:0.5;--rdp-disabled-opacity:0.25;}.rdp-months{margin:",h.W0[16],";}.rdp-month_grid{margin:0px;}.rdp-day{padding:0px;}.rdp-nav{--rdp-accent-color:",h.Jv.text.primary,";button{border-radius:",h.E0.circle,";&:hover,&:focus,&:active{background-color:",h.Jv.background.hover,";color:",h.Jv.text.primary,";}&:focus-visible:not(:disabled){--rdp-accent-color:",h.Jv.text.white,";background-color:",h.Jv.background.brand,";}}}.rdp-dropdown_root{.rdp-caption_label{padding:",h.W0[8],";}}.rdp-today{.rdp-day_button{font-weight:",h.Ue.bold,";}}.rdp-selected{color:var(--rdp-selected-color);background-color:var(--rdp-accent-color);border-radius:",h.E0.circle,";font-weight:",h.Ue.regular,";.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-accent-color);color:",h.Jv.text.primary,";}&:focus-visible{outline:2px solid var(--rdp-accent-color);outline-offset:2px;}&:not(.rdp-outside){color:var(--rdp-selected-color);}}}.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-background-color);color:",h.Jv.text.primary,";}&:focus-visible:not([disabled]){color:var(--rdp-selected-color);opacity:1;background-color:var(--rdp-accent-color);}}"+(true?"":0),true?"":0),clearButton:(0,n.iv)("position:absolute;top:50%;right:",h.W0[4],";transform:translateY(-50%);width:32px;height:32px;",Pt.i.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",h.E0[2],";:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0)};var Uc=r(91);var Gc=r(7662);var Qc=r(7573);function Yc(t){"@babel/helpers - typeof";return Yc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yc(t)}var qc=["css"];function Hc(t,e,r){e=Vc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Vc(t){var e=$c(t,"string");return Yc(e)==="symbol"?e:String(e)}function $c(t,e){if(Yc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Yc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Kc(){Kc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Kc.apply(this,arguments)}function Xc(t,e){if(t==null)return{};var r=ts(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function ts(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function es(t,e){return as(t)||is(t,e)||ns(t,e)||rs()}function rs(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ns(t,e){if(!t)return;if(typeof t==="string")return os(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return os(t,e)}function os(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function is(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function as(t){if(Array.isArray(t))return t}function us(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ls=function t(e){var r=e.label,o=e.field,i=e.fieldState,a=e.interval,l=a===void 0?30:a,c=e.disabled,s=e.loading,f=e.placeholder,p=e.helpText,h=e.isClearable,y=h===void 0?true:h;var b=(0,u.useState)(false),g=es(b,2),m=g[0],w=g[1];var x=(0,u.useRef)(null);var _=(0,u.useMemo)((function(){var t=(0,Uc.Z)((0,Gc.Z)(new Date,0),0);var e=(0,Uc.Z)((0,Gc.Z)(new Date,23),59);var r=(0,Qc.Z)({start:t,end:e},{step:l});return r.map((function(t){return(0,xc.Z)(t,S.E_.hoursMinutes)}))}),[l]);var Z=(0,xt.l)({isOpen:m,isDropdown:true}),O=Z.triggerRef,k=Z.triggerWidth,j=Z.position,E=Z.popoverRef;var C=be({options:_.map((function(t){return{label:t,value:t}})),isOpen:m,selectedValue:o.value,onSelect:function t(e){o.onChange(e.value);w(false)},onClose:function t(){return w(false)}}),A=C.activeIndex,P=C.setActiveIndex;(0,u.useEffect)((function(){if(m&&A>=0&&x.current){x.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[m,A]);return(0,n.tZ)(Dt,{label:r,field:o,fieldState:i,disabled:c,loading:s,placeholder:f,helpText:p},(function(t){var e,r;var i=t.css,a=Xc(t,qc);return(0,n.tZ)("div",null,(0,n.tZ)("div",{css:ss.wrapper,ref:O},(0,n.tZ)("input",Kc({},a,{ref:o.ref,css:[i,ss.input,true?"":0,true?"":0],type:"text",onClick:function t(e){e.stopPropagation();w((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();w((function(t){return!t}))}if(e.key==="Tab"){w(false)}},value:(e=o.value)!==null&&e!==void 0?e:"",onChange:function t(e){var r=e.target.value;o.onChange(r)},autoComplete:"off","data-input":true})),(0,n.tZ)(d.Z,{name:"clock",width:32,height:32,style:ss.icon}),y&&o.value&&(0,n.tZ)(v.Z,{variant:"text",buttonCss:ss.clearButton,onClick:function t(){return o.onChange("")}},(0,n.tZ)(d.Z,{name:"times",width:12,height:12}))),(0,n.tZ)(xt.h,{isOpen:m,onClickOutside:function t(){return w(false)},onEscape:function t(){return w(false)}},(0,n.tZ)("div",{css:[ss.popover,(r={},Hc(r,S.dZ?"right":"left",j.left),Hc(r,"top",j.top),Hc(r,"maxWidth",k),r),true?"":0,true?"":0],ref:E},(0,n.tZ)("ul",{css:ss.list},_.map((function(t,e){return(0,n.tZ)("li",{key:e,css:ss.listItem,ref:A===e?x:null,"data-active":A===e},(0,n.tZ)("button",{type:"button",css:ss.itemButton,onClick:function e(){o.onChange(t);w(false)},onMouseOver:function t(){return P(e)},onMouseLeave:function t(){return e!==A&&P(-1)},onFocus:function t(){return P(e)}},t))}))))))}))};const cs=ls;var ss={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.iv)("&[data-input]{padding-left:",h.W0[40],";}"+(true?"":0),true?"":0),icon:(0,n.iv)("position:absolute;top:50%;left:",h.W0[8],";transform:translateY(-50%);color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),popover:(0,n.iv)("position:absolute;width:100%;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";height:380px;overflow-y:auto;border-radius:",h.E0[6],";"+(true?"":0),true?"":0),list:true?{name:"v5al3",styles:"list-style:none;padding:0;margin:0"}:0,listItem:(0,n.iv)("width:100%;height:40px;cursor:pointer;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;&[data-active='true']{background-color:",h.Jv.background.hover,";}:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0),itemButton:(0,n.iv)(Pt.i.resetButton,";",y.c.body(),";margin:",h.W0[4]," ",h.W0[12],";width:100%;height:100%;&:focus,&:active,&:hover{background:none;color:",h.Jv.text.primary,";}"+(true?"":0),true?"":0),clearButton:(0,n.iv)("position:absolute;top:50%;right:",h.W0[4],";transform:translateY(-50%);width:32px;height:32px;",Pt.i.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",h.E0[2],";:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0)};function ds(t){"@babel/helpers - typeof";return ds="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ds(t)}function fs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ps(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fs(Object(r),!0).forEach((function(e){vs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function vs(t,e,r){e=hs(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function hs(t){var e=ys(t,"string");return ds(e)==="symbol"?e:String(e)}function ys(t,e){if(ds(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ds(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function bs(){bs=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return bs.apply(this,arguments)}var gs=j.y.tutor_currency;function ms(t){var e=t.index;var r=(0,p.Gc)();var o=r.watch("subscriptions.".concat(e,".offer_sale_price"));var i=r.watch("subscriptions.".concat(e,".regular_price"));var u=!!r.watch("subscriptions.".concat(e,".schedule_sale_price"));return(0,n.tZ)("div",{css:ws.wrapper},(0,n.tZ)("div",null,(0,n.tZ)(p.Qr,{control:r.control,name:"subscriptions.".concat(e,".offer_sale_price"),render:function t(e){return(0,n.tZ)(En,bs({},e,{label:(0,a.__)("Offer sale price","tutor")}))}})),(0,n.tZ)(k.Z,{when:o},(0,n.tZ)("div",{css:ws.inputWrapper},(0,n.tZ)(p.Qr,{control:r.control,name:"subscriptions.".concat(e,".sale_price"),rules:ps(ps({},Mn()),{},{validate:function t(e){if(e&&i&&Number(e)>=Number(i)){return(0,a.__)("Sale price should be less than regular price","tutor")}if(e&&i&&Number(e)<=0){return(0,a.__)("Sale price should be greater than 0","tutor")}return undefined}}),render:function t(e){return(0,n.tZ)(xl,bs({},e,{type:"number",label:(0,a.__)("Sale Price","tutor"),content:(gs===null||gs===void 0?void 0:gs.symbol)||"$",selectOnFocus:true,contentCss:Pt.i.inputCurrencyStyle}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"subscriptions.".concat(e,".schedule_sale_price"),render:function t(e){return(0,n.tZ)(Xl,bs({},e,{label:(0,a.__)("Schedule the sale price","tutor")}))}}),(0,n.tZ)(k.Z,{when:u},(0,n.tZ)("div",{css:ws.datetimeWrapper},(0,n.tZ)("label",null,(0,a.__)("Sale starts from","tutor")),(0,n.tZ)("div",{css:Pt.i.dateAndTimeWrapper},(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_from_date"),control:r.control,rules:{required:(0,a.__)("Schedule date is required","tutor")},render:function t(e){return(0,n.tZ)(zc,bs({},e,{isClearable:false,placeholder:"yyyy-mm-dd",disabledBefore:(new Date).toISOString()}))}}),(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_from_time"),control:r.control,rules:{required:(0,a.__)("Schedule time is required","tutor")},render:function t(e){return(0,n.tZ)(cs,bs({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}}))),(0,n.tZ)("div",{css:ws.datetimeWrapper},(0,n.tZ)("label",null,(0,a.__)("Sale ends to","tutor")),(0,n.tZ)("div",{css:Pt.i.dateAndTimeWrapper},(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_to_date"),control:r.control,rules:{required:(0,a.__)("Schedule date is required","tutor"),validate:{checkEndDate:function t(n){var o=r.watch("subscriptions.".concat(e,".sale_price_from_date"));var i=n;if(o&&i){return new Date(o)>new Date(i)?(0,a.__)("Sales End date should be greater than start date","tutor"):undefined}return undefined}},deps:["subscriptions.".concat(e,".sale_price_from_date")]},render:function t(o){return(0,n.tZ)(zc,bs({},o,{isClearable:false,placeholder:"yyyy-mm-dd",disabledBefore:r.watch("subscriptions.".concat(e,".sale_price_from_date"))||undefined}))}}),(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_to_time"),control:r.control,rules:{required:(0,a.__)("Schedule time is required","tutor"),validate:{checkEndTime:function t(n){var o=r.watch("subscriptions.".concat(e,".sale_price_from_date"));var i=r.watch("subscriptions.".concat(e,".sale_price_from_time"));var u=r.watch("subscriptions.".concat(e,".sale_price_to_date"));var l=n;if(o&&u&&i&&l){return new Date("".concat(o," ").concat(i))>new Date("".concat(u," ").concat(l))?(0,a.__)("Sales End time should be greater than start time","tutor"):undefined}return undefined}},deps:["subscriptions.".concat(e,".sale_price_from_date"),"subscriptions.".concat(e,".sale_price_from_time"),"subscriptions.".concat(e,".sale_price_to_date")]},render:function t(e){return(0,n.tZ)(cs,bs({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}})))))))}var ws={wrapper:(0,n.iv)("background-color:",h.Jv.background.white,";padding:",h.W0[12],";border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";display:flex;flex-direction:column;gap:",h.W0[20],";"+(true?"":0),true?"":0),inputWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[12],";padding:",h.W0[4],";margin:-",h.W0[4],";"+(true?"":0),true?"":0),datetimeWrapper:(0,n.iv)("label{",y.c.caption(),";color:",h.Jv.text.title,";}"+(true?"":0),true?"":0)};function xs(t){"@babel/helpers - typeof";return xs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xs(t)}function _s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Zs(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_s(Object(r),!0).forEach((function(e){Os(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Os(t,e,r){e=Ss(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ss(t){var e=ks(t,"string");return xs(e)==="symbol"?e:String(e)}function ks(t,e){if(xs(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(xs(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var js={id:"0",payment_type:"recurring",plan_type:"course",assign_id:"0",plan_name:"",recurring_value:"1",recurring_interval:"month",is_featured:false,regular_price:"0",sale_price:"0",sale_price_from_date:"",sale_price_from_time:"",sale_price_to_date:"",sale_price_to_time:"",recurring_limit:(0,a.__)("Until cancelled","tutor"),do_not_provide_certificate:false,enrollment_fee:"0",trial_value:"1",trial_interval:"day",charge_enrollment_fee:false,enable_free_trial:false,offer_sale_price:false,schedule_sale_price:false};var Es=function t(e){var r,n,o,i,u,l,c,s,d,f;return{id:e.id,payment_type:(r=e.payment_type)!==null&&r!==void 0?r:"recurring",plan_type:(n=e.plan_type)!==null&&n!==void 0?n:"course",assign_id:e.assign_id,plan_name:(o=e.plan_name)!==null&&o!==void 0?o:"",recurring_value:(i=e.recurring_value)!==null&&i!==void 0?i:"0",recurring_interval:(u=e.recurring_interval)!==null&&u!==void 0?u:"month",is_featured:!!Number(e.is_featured),regular_price:(l=e.regular_price)!==null&&l!==void 0?l:"0",recurring_limit:e.recurring_limit==="0"?(0,a.__)("Until cancelled","tutor"):e.recurring_limit||"",enrollment_fee:(c=e.enrollment_fee)!==null&&c!==void 0?c:"0",trial_value:(s=e.trial_value)!==null&&s!==void 0?s:"0",trial_interval:(d=e.trial_interval)!==null&&d!==void 0?d:"day",sale_price:(f=e.sale_price)!==null&&f!==void 0?f:"0",charge_enrollment_fee:!!Number(e.enrollment_fee),enable_free_trial:!!Number(e.trial_value),offer_sale_price:!!Number(e.sale_price),schedule_sale_price:!!e.sale_price_from,do_not_provide_certificate:!Number(e.provide_certificate),sale_price_from_date:e.sale_price_from?(0,xc.Z)((0,b.nP)(e.sale_price_from),S.E_.yearMonthDay):"",sale_price_from_time:e.sale_price_from?(0,xc.Z)((0,b.nP)(e.sale_price_from),S.E_.hoursMinutes):"",sale_price_to_date:e.sale_price_to?(0,xc.Z)((0,b.nP)(e.sale_price_to),S.E_.yearMonthDay):"",sale_price_to_time:e.sale_price_to?(0,xc.Z)((0,b.nP)(e.sale_price_to),S.E_.hoursMinutes):""}};var Cs=function t(e){return Zs(Zs(Zs(Zs(Zs(Zs({},e.id&&String(e.id)!=="0"&&{id:e.id}),{},{payment_type:e.payment_type,plan_type:e.plan_type,assign_id:e.assign_id,plan_name:e.plan_name},e.payment_type==="recurring"&&{recurring_value:e.recurring_value,recurring_interval:e.recurring_interval}),{},{regular_price:e.regular_price,recurring_limit:e.recurring_limit===(0,a.__)("Until cancelled","tutor")?"0":e.recurring_limit,is_featured:e.is_featured?"1":"0"},e.charge_enrollment_fee&&{enrollment_fee:e.enrollment_fee}),e.enable_free_trial&&{trial_value:e.trial_value,trial_interval:e.trial_interval}),{},{sale_price:e.offer_sale_price?e.sale_price:"0"},e.schedule_sale_price&&{sale_price_from:(0,b.WK)(new Date("".concat(e.sale_price_from_date," ").concat(e.sale_price_from_time))),sale_price_to:(0,b.WK)(new Date("".concat(e.sale_price_to_date," ").concat(e.sale_price_to_time)))}),{},{provide_certificate:e.do_not_provide_certificate?"0":"1"})};var As=function t(e){return St.R.post(kt.Z.GET_SUBSCRIPTIONS_LIST,{object_id:e})};var Ps=function t(e){return(0,_t.a)({queryKey:["SubscriptionsList",e],queryFn:function t(){return As(e).then((function(t){return t.data}))}})};var Ws=function t(e,r){return St.R.post(kt.Z.SAVE_SUBSCRIPTION,Zs(Zs({object_id:e},r.id&&{id:r.id}),r))};var Ls=function t(e){var r=(0,o.NL)();var n=(0,Ot.p)(),i=n.showToast;return(0,Zt.D)({mutationFn:function t(r){return Ws(e,r)},onSuccess:function t(n){if(n.status_code===200||n.status_code===201){i({message:n.message,type:"success"});r.invalidateQueries({queryKey:["SubscriptionsList",e]})}},onError:function t(e){i({type:"danger",message:(0,b.Mo)(e)})}})};var Is=function t(e,r){return St.R.post(kt.Z.DELETE_SUBSCRIPTION,{object_id:e,id:r})};var Ds=function t(e){var r=(0,o.NL)();var n=(0,Ot.p)(),i=n.showToast;return(0,Zt.D)({mutationFn:function t(r){return Is(e,r)},onSuccess:function t(n,o){if(n.status_code===200){i({message:n.message,type:"success"});r.setQueryData(["SubscriptionsList",e],(function(t){return t.filter((function(t){return t.id!==String(o)}))}))}},onError:function t(e){i({type:"danger",message:(0,b.Mo)(e)})}})};var Ts=function t(e,r){return St.R.post(kt.Z.DUPLICATE_SUBSCRIPTION,{object_id:e,id:r})};var Ms=function t(e){var r=(0,o.NL)();var n=(0,Ot.p)(),i=n.showToast;return(0,Zt.D)({mutationFn:function t(r){return Ts(e,r)},onSuccess:function t(n){if(n.data){i({message:n.message,type:"success"});r.invalidateQueries({queryKey:["SubscriptionsList",e]})}},onError:function t(e){i({type:"danger",message:(0,b.Mo)(e)})}})};var Js=function t(e,r){return St.R.post(kt.Z.SORT_SUBSCRIPTION,{object_id:e,plan_ids:r})};var Ns=function t(e){var r=(0,o.NL)();var n=(0,Ot.p)(),i=n.showToast;return(0,Zt.D)({mutationFn:function t(r){return Js(e,r)},onSuccess:function t(n,o){if(n.status_code===200){r.setQueryData(["SubscriptionsList",e],(function(t){var e=o.map((function(t){return String(t)}));return t.sort((function(t,r){return e.indexOf(t.id)-e.indexOf(r.id)}))}));r.invalidateQueries({queryKey:["SubscriptionsList",e]})}},onError:function t(n){i({type:"danger",message:(0,b.Mo)(n)});r.invalidateQueries({queryKey:["SubscriptionsList",e]})}})};var Fs=function t(){return wpAjaxInstance.get(endpoints.GET_MEMBERSHIP_PLANS).then((function(t){return t.data}))};var Bs=function t(){return useQuery({queryKey:["MembershipPlans"],queryFn:Fs})};function zs(t){"@babel/helpers - typeof";return zs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zs(t)}function Rs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Us(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Rs(Object(r),!0).forEach((function(e){Gs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Gs(t,e,r){e=Qs(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Qs(t){var e=Ys(t,"string");return zs(e)==="symbol"?e:String(e)}function Ys(t,e){if(zs(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(zs(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var qs=function t(e){return(0,Sl.cP)(Us(Us({},e),{},{wasDragging:true}))};var Hs={droppable:{strategy:Zl.uN.Always}};function Vs(t){"@babel/helpers - typeof";return Vs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vs(t)}function $s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ks(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$s(Object(r),!0).forEach((function(e){Xs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xs(t,e,r){e=td(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function td(t){var e=ed(t,"string");return Vs(e)==="symbol"?e:String(e)}function ed(t,e){if(Vs(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Vs(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function rd(){rd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return rd.apply(this,arguments)}function nd(t){return ad(t)||id(t)||fd(t)||od()}function od(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function id(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ad(t){if(Array.isArray(t))return pd(t)}function ud(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ud=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Vs(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function ld(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function cd(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ld(i,n,o,a,u,"next",t)}function u(t){ld(i,n,o,a,u,"throw",t)}a(undefined)}))}}function sd(t,e){return hd(t)||vd(t,e)||fd(t,e)||dd()}function dd(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fd(t,e){if(!t)return;if(typeof t==="string")return pd(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pd(t,e)}function pd(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function vd(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function hd(t){if(Array.isArray(t))return t}function yd(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var bd=100;var gd=j.y.tutor_currency;function md(t){var e;var r=t.courseId,o=t.id,i=t.toggleCollapse,l=t.bgLight,c=l===void 0?false:l,s=t.isExpanded,f=t.isOverlay,v=f===void 0?false:f;var y=(0,u.useRef)(null);var g=(0,u.useRef)(null);var m=(0,u.useRef)(null);var w=(0,p.Gc)();var x=w.watch("subscriptions");var _=x.findIndex((function(t){return t.id===o}));var Z=w.watch("subscriptions.".concat(_));var S=w.formState.isDirty;var j=w.formState.errors.subscriptions?Object.keys(w.formState.errors.subscriptions[_]||{}).length:0;var C=(0,u.useState)(false),A=sd(C,2),P=A[0],W=A[1];var L=(0,u.useState)(false),I=sd(L,2),D=I[0],T=I[1];(0,u.useEffect)((function(){if(s){var t=setTimeout((function(){w.setFocus("subscriptions.".concat(_,".plan_name"))}),bd);if(_>0){var e;(e=y.current)===null||e===void 0?void 0:e.scrollIntoView({behavior:"smooth",block:"start"})}return function(){clearTimeout(t)}}}),[s]);(0,u.useEffect)((function(){var t=function t(e){if((0,E.$K)(y.current)&&!y.current.contains(e.target)){T(false)}};document.addEventListener("click",t);return function(){return document.removeEventListener("click",t)}}),[D]);var M=Ds(r);var J=Ms(r);var N=function(){var t=cd(ud().mark((function t(){var e;return ud().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return M.mutateAsync(Number(Z.id));case 2:e=r.sent;if(e.data){W(false);if(s){i(Z.id)}}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var F=function(){var t=cd(ud().mark((function t(){var e;return ud().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return J.mutateAsync(Number(Z.id));case 2:e=r.sent;if(e.data){i(String(e.data))}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var B=(0,Sl.nB)({id:Z.id||"",animateLayoutChanges:qs}),z=B.attributes,R=B.listeners,U=B.setNodeRef,G=B.transform,Q=B.transition,Y=B.isDragging;var q=(0,u.useCallback)((function(t){if(t){U(t);y.current=t}}),[U]);var H=w.watch("subscriptions.".concat(_,".plan_name"));var V=w.watch("subscriptions.".concat(_,".charge_enrollment_fee"));var $=w.watch("subscriptions.".concat(_,".is_featured"));var K=w.watch("subscriptions.".concat(_,".offer_sale_price"));var X=!!w.watch("subscriptions.".concat(_,".schedule_sale_price"));var tt=(0,Fl.q_)({height:s?(e=g.current)===null||e===void 0?void 0:e.scrollHeight:0,opacity:s?1:0,overflow:"hidden",config:{duration:300,easing:function t(e){return e*(2-e)}}},[V,$,K,X,S,s,j]),et=sd(tt,2),rt=et[0],nt=et[1];(0,u.useEffect)((function(){if((0,E.$K)(g.current)){var t;nt.start({height:s?(t=g.current)===null||t===void 0?void 0:t.scrollHeight:0,opacity:s?1:0})}}),[V,$,K,X,S,s,j]);var ot=[3,6,9,12];var it=[].concat(nd(ot.map((function(t){return{label:(0,a.sprintf)((0,a.__)("%s times","tutor"),t.toString()),value:String(t)}}))),[{label:(0,a.__)("Until cancelled","tutor"),value:(0,a.__)("Until cancelled","tutor")}]);var at={transform:Nl.ux.Transform.toString(G),transition:Q,opacity:Y?.3:undefined,background:Y?h.Jv.stroke.hover:undefined};return(0,n.tZ)("form",rd({},z,{css:_d.subscription({bgLight:c,isActive:D,isDragging:v,isDeletePopoverOpen:P}),onClick:function t(){return T(true)},style:at,ref:q}),(0,n.tZ)("div",{css:_d.subscriptionHeader(s)},(0,n.tZ)("div",rd({css:_d.grabber({isFormDirty:S})},S?{}:R),(0,n.tZ)(d.Z,{"data-grabber":true,name:"threeDotsVerticalDouble",width:24,height:24}),(0,n.tZ)("button",{type:"button",css:_d.title,disabled:S,title:H,onClick:function t(){return!S&&i(Z.id)}},H,(0,n.tZ)(k.Z,{when:Z.is_featured},(0,n.tZ)(Wt.Z,{content:(0,a.__)("Featured","tutor"),delay:200},(0,n.tZ)(d.Z,{name:"star",width:24,height:24}))))),(0,n.tZ)("div",{css:_d.actions(s),"data-visually-hidden":true},(0,n.tZ)(k.Z,{when:!s},(0,n.tZ)(Wt.Z,{content:(0,a.__)("Edit","tutor"),delay:200},(0,n.tZ)("button",{"data-cy":"edit-subscription",type:"button",disabled:S,onClick:function t(){return!S&&i(Z.id)}},(0,n.tZ)(d.Z,{name:"edit",width:24,height:24})))),(0,n.tZ)(k.Z,{when:Z.isSaved},(0,n.tZ)(Wt.Z,{content:(0,a.__)("Duplicate","tutor"),delay:200},(0,n.tZ)("button",{"data-cy":"duplicate-subscription",type:"button",disabled:S,onClick:F},(0,n.tZ)(k.Z,{when:!J.isPending,fallback:(0,n.tZ)(O.ZP,{size:24})},(0,n.tZ)(d.Z,{name:"copyPaste",width:24,height:24})))),(0,n.tZ)(Wt.Z,{content:(0,a.__)("Delete","tutor"),delay:200},(0,n.tZ)("button",{"data-cy":"delete-subscription",ref:m,type:"button",disabled:S,onClick:function t(){return W(true)}},(0,n.tZ)(d.Z,{name:"delete",width:24,height:24}))),(0,n.tZ)("button",{type:"button",disabled:S,onClick:function t(){return!S&&i(Z.id)},"data-collapse-button":true,title:(0,a.__)("Collapse/expand plan","tutor")},(0,n.tZ)(d.Z,{name:"chevronDown",width:24,height:24}))))),(0,n.tZ)(Fl.q.div,{style:Ks({},rt),css:_d.itemWrapper(s)},(0,n.tZ)("div",{ref:g,css:Pt.i.display.flex("column")},(0,n.tZ)("div",{css:_d.subscriptionContent},(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".plan_name"),rules:Mn(),render:function t(e){return(0,n.tZ)(Zo,rd({},e,{placeholder:(0,a.__)("Enter plan name","tutor"),label:(0,a.__)("Plan Name","tutor")}))}}),(0,n.tZ)("div",{css:_d.inputGroup},(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".regular_price"),rules:Ks(Ks({},Mn()),{},{validate:function t(e){if(Number(e)<=0){return(0,a.__)("Price must be greater than 0","tutor")}}}),render:function t(e){return(0,n.tZ)(xl,rd({},e,{label:(0,a.__)("Price","tutor"),content:(gd===null||gd===void 0?void 0:gd.symbol)||"$",placeholder:(0,a.__)("Plan price","tutor"),selectOnFocus:true,contentCss:Pt.i.inputCurrencyStyle,type:"number"}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".recurring_value"),rules:Ks(Ks({},Mn()),{},{validate:function t(e){if(Number(e)<1){return(0,a.__)("This value must be equal to or greater than 1","tutor")}}}),render:function t(e){return(0,n.tZ)(Zo,rd({},e,{label:(0,a.__)("Billing Interval","tutor"),placeholder:(0,a.__)("12","tutor"),selectOnFocus:true,type:"number"}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".recurring_interval"),render:function t(e){return(0,n.tZ)(Me,rd({},e,{label:(0,n.tZ)("div",null," "),options:[{label:(0,a.__)("Day(s)","tutor"),value:"day"},{label:(0,a.__)("Week(s)","tutor"),value:"week"},{label:(0,a.__)("Month(s)","tutor"),value:"month"},{label:(0,a.__)("Year(s)","tutor"),value:"year"}],removeOptionsMinWidth:true}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".recurring_limit"),rules:Ks(Ks({},Mn()),{},{validate:function t(e){if(e===(0,a.__)("Until cancelled","tutor")){return true}if(Number(e)<=0){return(0,a.__)("Renew plan must be greater than 0","tutor")}return true}}),render:function t(e){return(0,n.tZ)(gc,rd({},e,{label:(0,a.__)("Billing Cycles","tutor"),placeholder:(0,a.__)("Select or type times to renewing the plan","tutor"),content:e.field.value!==(0,a.__)("Until cancelled","tutor")&&(0,a.__)("Times","tutor"),contentPosition:"right",type:"number",presetOptions:it,selectOnFocus:true}))}})),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".charge_enrollment_fee"),render:function t(e){return(0,n.tZ)(Xl,rd({},e,{label:(0,a.__)("Charge enrollment fee","tutor")}))}}),(0,n.tZ)(k.Z,{when:V},(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".enrollment_fee"),rules:Ks(Ks({},Mn()),{},{validate:function t(e){if(Number(e)<=0){return(0,a.__)("Enrollment fee must be greater than 0","tutor")}return true}}),render:function t(e){return(0,n.tZ)(xl,rd({},e,{label:(0,a.__)("Enrollment fee","tutor"),content:(gd===null||gd===void 0?void 0:gd.symbol)||"$",placeholder:(0,a.__)("Enter enrollment fee","tutor"),selectOnFocus:true,contentCss:Pt.i.inputCurrencyStyle,type:"number"}))}})),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".do_not_provide_certificate"),render:function t(e){return(0,n.tZ)(Xl,rd({},e,{label:(0,a.__)("Do not provide certificate","tutor")}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".is_featured"),render:function t(e){return(0,n.tZ)(Xl,rd({},e,{label:(0,a.__)("Mark as featured","tutor")}))}}),(0,n.tZ)(ms,{index:_})))),(0,n.tZ)(Ql,{isOpen:P,triggerRef:m,closePopover:b.ZT,maxWidth:"258px",title:(0,a.sprintf)((0,a.__)('Delete "%s"',"tutor"),Z.plan_name),message:(0,a.__)("Are you sure you want to delete this plan? This cannot be undone.","tutor"),animationType:ir.ru.slideUp,arrow:"auto",hideArrow:true,isLoading:M.isPending,confirmButton:{text:(0,a.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,a.__)("Cancel","tutor"),variant:"text"},onConfirmation:N,onCancel:function t(){return W(false)}}))}var wd=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var xd=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var _d={grabber:function t(e){var r=e.isFormDirty;return(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";",y.c.body(),";color:",h.Jv.text.hints,";width:100%;min-height:40px;[data-grabber]{color:",h.Jv.icon["default"],";cursor:",r?"not-allowed":"grab",";flex-shrink:0;}span{max-width:496px;width:100%;",Pt.i.textEllipsis,";}"+(true?"":0),true?"":0)},trialWrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 1fr;align-items:start;gap:",h.W0[8],";"+(true?"":0),true?"":0),title:(0,n.iv)(Pt.i.resetButton,";display:flex;align-items:center;color:",h.Jv.text.hints,";flex-grow:1;gap:",h.W0[8],";:disabled{cursor:default;}svg{color:",h.Jv.icon.brand,";}"+(true?"":0),true?"":0),titleField:(0,n.iv)("width:100%;position:relative;input{padding-right:",h.W0[128],"!important;}"+(true?"":0),true?"":0),titleActions:(0,n.iv)("position:absolute;right:",h.W0[4],";top:50%;transform:translateY(-50%);display:flex;align-items:center;gap:",h.W0[8],";"+(true?"":0),true?"":0),subscription:function t(e){var r=e.bgLight,o=e.isActive,i=e.isDragging,a=e.isDeletePopoverOpen;return(0,n.iv)("width:100%;border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0.card,";overflow:hidden;transition:border-color 0.3s ease;[data-visually-hidden]{opacity:",a?1:0,";transition:opacity 0.3s ease;}",r&&(0,n.iv)("background-color:",h.Jv.background.white,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("border-color:",h.Jv.stroke.brand,";"+(true?"":0),true?"":0)," ",i&&(0,n.iv)("box-shadow:",h.AF.drag,";[data-grabber]{cursor:grabbing;}"+(true?"":0),true?"":0)," &:hover:not(:disabled){[data-visually-hidden]{opacity:1;}}",h.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},itemWrapper:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)(e&&(0,n.iv)("background-color:",h.Jv.background.hover,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},subscriptionHeader:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)("padding:",h.W0[12]," ",h.W0[16],";display:flex;align-items:center;justify-content:space-between;",e&&(0,n.iv)("background-color:",h.Jv.background.hover,";border-bottom:1px solid ",h.Jv.stroke.border,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},subscriptionContent:(0,n.iv)("padding:",h.W0[16],";display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),actions:function t(e){return(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";button{width:24px;height:24px;",Pt.i.resetButton,";color:",h.Jv.icon["default"],";display:flex;align-items:center;justify-content:center;transition:color 0.3s ease;:disabled{cursor:not-allowed;color:",h.Jv.icon.disable.background,";}&[data-collapse-button]{transition:transform 0.3s ease;",e&&xd,";svg{width:20px;height:20px;}&:hover:not(:disabled){color:",h.Jv.icon.hover,";}}}"+(true?"":0),true?"":0)},collapse:function t(e){return(0,n.iv)("transition:transform 0.3s ease;svg{width:16px;height:16px;}",e&&wd,";"+(true?"":0),true?"":0)},inputGroup:(0,n.iv)("display:grid;grid-template-columns:1fr 0.7fr 1fr 1fr;align-items:start;gap:",h.W0[8],";",h.Uo.smallMobile,"{grid-template-columns:1fr;}"+(true?"":0),true?"":0)};var Zd=r(7363);function Od(t){"@babel/helpers - typeof";return Od="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Od(t)}function Sd(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Sd=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Od(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function kd(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function jd(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){kd(i,n,o,a,u,"next",t)}function u(t){kd(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Ed(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Cd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ed(Object(r),!0).forEach((function(e){Ad(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ed(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ad(t,e,r){e=Pd(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Pd(t){var e=Wd(t,"string");return Od(e)==="symbol"?e:String(e)}function Wd(t,e){if(Od(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Od(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ld(t,e){return Jd(t)||Md(t,e)||Dd(t,e)||Id()}function Id(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Dd(t,e){if(!t)return;if(typeof t==="string")return Td(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Td(t,e)}function Td(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Md(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Jd(t){if(Array.isArray(t))return t}function Nd(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}function Fd(t){var e;var r=t.courseId,l=t.isBundle,c=l===void 0?false:l,s=t.title,f=t.subtitle,h=t.icon,y=t.closeModal,g=t.expandedSubscriptionId,m=t.createEmptySubscriptionOnMount;var w=(0,o.NL)();var x=at({defaultValues:{subscriptions:[]},mode:"onChange"});var _=(0,p.Dq)({control:x.control,name:"subscriptions",keyName:"_id"}),Z=_.append,O=_.remove,j=_.move,C=_.fields;var A=(0,u.useState)(g||""),P=Ld(A,2),W=P[0],L=P[1];var I=(0,u.useState)(null),D=Ld(I,2),T=D[0],M=D[1];var J=!!(0,i.y)({queryKey:["SubscriptionsList",r]});var N=w.getQueryData(["SubscriptionsList",r]);var F=Ns(r);var B=Ls(r);var z=x.formState.isDirty;var R=x.getValues().subscriptions.find((function(t){return t.id===W}));var U=C.findIndex((function(t){return!t.isSaved}))!==-1?C.findIndex((function(t){return!t.isSaved})):(e=x.formState.dirtyFields.subscriptions)===null||e===void 0?void 0:e.findIndex((function(t){return(0,E.$K)(t)}));(0,u.useEffect)((function(){if(!N){return}if(C.length===0){return x.reset({subscriptions:N.map((function(t){return Cd(Cd({},Es(t)),{},{isSaved:true})}))})}var t=N.map((function(t){var e=C.find((function(e){return e.id===t.id}));if(e){return Cd(Cd({},e),Cd(Cd({},Es(t)),{},{isSaved:true}))}return Cd(Cd({},Es(t)),{},{isSaved:true})}));x.reset({subscriptions:t})}),[N,J]);var G=function(){var t=jd(Sd().mark((function t(e){var n;return Sd().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:o.prev=0;x.trigger();n=setTimeout(jd(Sd().mark((function t(){var n,o,i;return Sd().wrap((function t(a){while(1)switch(a.prev=a.next){case 0:n=x.formState.errors.subscriptions||[];if(!n.length){a.next=3;break}return a.abrupt("return");case 3:o=Cs(Cd(Cd({},e),{},{id:e.isSaved?e.id:"0",assign_id:String(r),plan_type:c?"bundle":"course"}));a.next=6;return B.mutateAsync(o);case 6:i=a.sent;if(i.status_code===200||i.status_code===201){L((function(t){return t===o.id?"":o.id||""}))}case 8:case"end":return a.stop()}}),t)}))),0);return o.abrupt("return",(function(){clearTimeout(n)}));case 6:o.prev=6;o.t0=o["catch"](0);x.reset();case 9:case"end":return o.stop()}}),t,null,[[0,6]])})));return function e(r){return t.apply(this,arguments)}}();var Q=(0,Zl.Dy)((0,Zl.VT)(Zl.we,{activationConstraint:{distance:10}}),(0,Zl.VT)(Zl.Lg,{coordinateGetter:Sl.is}));(0,u.useEffect)((function(){if(m){var t=(0,b.x0)();Z(Cd(Cd({},js),{},{id:t,isSaved:false}));L(t)}}),[]);return(0,n.tZ)(p.RV,x,(0,n.tZ)(Wl,{onClose:function t(){return y({action:"CLOSE"})},icon:z?(0,n.tZ)(d.Z,{name:"warning",width:24,height:24}):h,title:z?S.iM.isAboveMobile?(0,a.__)("Unsaved Changes","tutor"):"":s,subtitle:z?s===null||s===void 0?void 0:s.toString():f,maxWidth:1218,actions:z&&(0,n.tZ)(Zd.Fragment,null,(0,n.tZ)(v.Z,{variant:"text",size:"small",onClick:function t(){return R?x.reset():y({action:"CLOSE"})}},R!==null&&R!==void 0&&R.isSaved?(0,a.__)("Discard Changes","tutor"):(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{"data-cy":"save-subscription",loading:B.isPending,variant:"primary",size:"small",onClick:function t(){if(U!==-1&&R){G(R)}}},R!==null&&R!==void 0&&R.isSaved?(0,a.__)("Update","tutor"):(0,a.__)("Save","tutor")))},(0,n.tZ)("div",{css:Bd.wrapper},(0,n.tZ)(k.Z,{when:C.length,fallback:(0,n.tZ)(Ml,{onCreateSubscription:function t(){var e=(0,b.x0)();Z(Cd(Cd({},js),{},{id:e,isSaved:false}));L(e)}})},(0,n.tZ)("div",{css:Bd.container},(0,n.tZ)("div",{css:Bd.header},(0,n.tZ)("h6",null,(0,a.__)("Subscription Plans","tutor"))),(0,n.tZ)("div",{css:Bd.content},(0,n.tZ)(Zl.LB,{sensors:Q,collisionDetection:Zl.pE,measuring:Hs,modifiers:[Ol.hg],onDragStart:function t(e){M(e.active.id)},onDragEnd:function(){var t=jd(Sd().mark((function t(e){var r,n,o,i,a;return Sd().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:r=e.active,n=e.over;if(n){u.next=4;break}M(null);return u.abrupt("return");case 4:if(r.id!==n.id){o=C.findIndex((function(t){return t.id===r.id}));i=C.findIndex((function(t){return t.id===n.id}));a=(0,b.Ao)(C,o,i);j(o,i);F.mutateAsync(a.map((function(t){return Number(t.id)})))}M(null);case 6:case"end":return u.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()},(0,n.tZ)(Sl.Fo,{items:C,strategy:Sl.qw},(0,n.tZ)(le,{each:C},(function(t,e){return(0,n.tZ)(md,{key:t.id,id:t.id,courseId:r,toggleCollapse:function t(e){L((function(t){return t===e?"":e}))},onDiscard:!t.id?function(){O(e)}:b.ZT,isExpanded:T?false:W===t.id})}))),(0,kl.createPortal)((0,n.tZ)(Zl.y9,null,(0,n.tZ)(k.Z,{when:T},(function(t){return(0,n.tZ)(md,{id:t,courseId:r,toggleCollapse:b.ZT,bgLight:true,onDiscard:b.ZT,isExpanded:false,isOverlay:true})}))),document.body)),(0,n.tZ)("div",null,(0,n.tZ)(v.Z,{variant:"secondary",icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),disabled:z,onClick:function t(){var e=(0,b.x0)();Z(Cd(Cd({},js),{},{id:e,isSaved:false}));L(e)}},(0,a.__)("Add New Plan","tutor")))))))))}var Bd={wrapper:true?{name:"w1atjl",styles:"width:100%;height:100%"}:0,container:(0,n.iv)("max-width:640px;width:100%;padding-block:",h.W0[40],";margin-inline:auto;display:flex;flex-direction:column;gap:",h.W0[32],";",h.Uo.smallMobile,"{padding-block:",h.W0[24],";padding-inline:",h.W0[8],";}"+(true?"":0),true?"":0),header:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;h6{",y.c.heading6("medium"),";color:",h.Jv.text.primary,";text-transform:none;letter-spacing:normal;}"+(true?"":0),true?"":0),content:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[16],";"+(true?"":0),true?"":0)};var zd=r(7363);function Rd(t,e){switch(t){case"hour":return e>1?(0,a.__)("Hours","tutor"):(0,a.__)("Hour","tutor");case"day":return e>1?(0,a.__)("Days","tutor"):(0,a.__)("Day","tutor");case"week":return e>1?(0,a.__)("Weeks","tutor"):(0,a.__)("Week","tutor");case"month":return e>1?(0,a.__)("Months","tutor"):(0,a.__)("Month","tutor");case"year":return e>1?(0,a.__)("Years","tutor"):(0,a.__)("Year","tutor");case"until_cancellation":return(0,a.__)("Until Cancellation","tutor")}}function Ud(t){var e=t.subscription,r=t.courseId,o=t.isBundle;var i=(0,un.d)(),u=i.showModal;return(0,n.tZ)("div",{"data-cy":"subscription-preview-item",css:Gd.wrapper},(0,n.tZ)("div",{css:Gd.item},(0,n.tZ)("p",{css:Gd.title},e.plan_name,(0,n.tZ)(k.Z,{when:e.is_featured},(0,n.tZ)(d.Z,{style:Gd.featuredIcon,name:"star",height:20,width:20}))),(0,n.tZ)("div",{css:Gd.information},(0,n.tZ)(k.Z,{when:e.payment_type==="recurring",fallback:(0,n.tZ)("span",null,(0,a.__)("Lifetime","tutor"))},(0,n.tZ)("span",null,(0,a.sprintf)((0,a.__)("Renew every %1$s %2$s","tutor"),e.recurring_value.toString().padStart(2,"0"),Rd(e.recurring_interval,Number(e.recurring_value))))),(0,n.tZ)(k.Z,{when:e.payment_type!=="onetime"},(0,n.tZ)(k.Z,{when:e.recurring_limit===(0,a.__)("Until cancelled","tutor"),fallback:(0,n.tZ)(zd.Fragment,null,(0,n.tZ)("span",null,"•"),(0,n.tZ)("span",null,e.recurring_limit.toString().padStart(2,"0")," ",(0,a.__)("Billing Cycles","tutor")))},(0,n.tZ)("span",null,"•"),(0,n.tZ)("span",null,(0,a.__)("Until Cancellation","tutor")))))),(0,n.tZ)("button",{type:"button",css:Gd.editButton,onClick:function t(){u({component:Fd,props:{title:(0,a.__)("Manage Subscription Plans","tutor"),icon:(0,n.tZ)(d.Z,{name:"dollar-recurring",width:24,height:24}),expandedSubscriptionId:e.id,courseId:r,isBundle:o}})},"data-edit-button":true,"data-cy":"edit-subscription"},(0,n.tZ)(d.Z,{name:"pen",width:19,height:19})))}var Gd={wrapper:(0,n.iv)("display:flex;justify-content:space-between;align-items:center;background-color:",h.Jv.background.white,";padding:",h.W0[8]," ",h.W0[12],";[data-edit-button]{opacity:0;transition:opacity 0.3s ease;}&:hover{background-color:",h.Jv.background.hover,";[data-edit-button]{opacity:1;}}&:not(:last-of-type){border-bottom:1px solid ",h.Jv.stroke["default"],";}"+(true?"":0),true?"":0),item:(0,n.iv)("min-height:48px;display:flex;flex-direction:column;justify-content:center;gap:",h.W0[4],";"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.caption("medium"),";color:",h.Jv.text.primary,";display:flex;align-items:center;"+(true?"":0),true?"":0),information:(0,n.iv)(y.c.small(),";color:",h.Jv.text.hints,";display:flex;align-items:center;flex-wrap:wrap;gap:",h.W0[4],";"+(true?"":0),true?"":0),featuredIcon:(0,n.iv)("color:",h.Jv.icon.brand,";"+(true?"":0),true?"":0),editButton:(0,n.iv)(Pt.i.resetButton,";",Pt.i.flexCenter(),";width:24px;height:24px;border-radius:",h.E0[4],";color:",h.Jv.icon["default"],";transition:color 0.3s ease,background 0.3s ease;&:hover{background:",h.Jv.action.secondary["default"],";color:",h.Jv.icon.brand,";}"+(true?"":0),true?"":0)};function Qd(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}function Yd(t){var e=t.courseId,r=t.isBundle,o=r===void 0?false:r;var i=Ps(e);var u=(0,un.d)(),l=u.showModal;if(i.isLoading){return(0,n.tZ)(O.g4,null)}if(!i.data){return null}var c=i.data;return(0,n.tZ)("div",{css:Vd.outer},(0,n.tZ)(k.Z,{when:c.length>0},(0,n.tZ)("div",{css:Vd.header},(0,a.__)("Subscriptions","tutor"))),(0,n.tZ)("div",{css:Vd.inner({hasSubscriptions:c.length>0})},(0,n.tZ)(le,{each:c},(function(t,r){return(0,n.tZ)(Ud,{key:r,subscription:Es(t),courseId:e,isBundle:o})})),(0,n.tZ)("div",{css:Vd.emptyState({hasSubscriptions:c.length>0})},(0,n.tZ)(v.Z,{"data-cy":"add-subscription",variant:"secondary",icon:(0,n.tZ)(d.Z,{name:"dollar-recurring",width:24,height:24}),onClick:function t(){l({component:Fd,props:{title:(0,a.__)("Manage Subscription Plans","tutor"),icon:(0,n.tZ)(d.Z,{name:"dollar-recurring",width:24,height:24}),createEmptySubscriptionOnMount:true,courseId:e,isBundle:o}})}},(0,a.__)("Add Subscription","tutor")))))}const qd=Yd;var Hd=true?{name:"1e1ncky",styles:"border:none"}:0;var Vd={outer:(0,n.iv)("width:100%;display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),inner:function t(e){var r=e.hasSubscriptions;return(0,n.iv)("background:",h.Jv.background.white,";border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0.card,";width:100%;overflow:hidden;",!r&&Hd,";"+(true?"":0),true?"":0)},header:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;",y.c.body(),";color:",h.Jv.text.title,";"+(true?"":0),true?"":0),emptyState:function t(e){var r=e.hasSubscriptions;return(0,n.iv)("padding:",r?"".concat(h.W0[8]," ").concat(h.W0[12]):0,";width:100%;&>button{width:100%;}"+(true?"":0),true?"":0)}};var $d=r(6860);var Kd=r(7363);function Xd(){Xd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Xd.apply(this,arguments)}var tf=(0,$d.X)();var ef=j.y.tutor_currency;var rf=function t(){var e,r,o;var u=(0,c.Gc)();var l=(0,i.y)({queryKey:["CourseBundle",tf]});var s=Number(u.getValues("details.subtotal_raw_price")).toFixed(2)||0;var d=(0,c.qo)({control:u.control,name:"course_selling_option"});var f=[{label:(0,a.__)("One-time purchase only","tutor-pro"),value:"one_time"},{label:(0,a.__)("Subscription only","tutor-pro"),value:"subscription"},{label:(0,a.__)("Subscription & one-time purchase","tutor-pro"),value:"both"}];return(0,n.tZ)(Kd.Fragment,null,(0,n.tZ)(k.Z,{when:(0,b.ro)(S.AO.SUBSCRIPTION)&&((e=j.y.settings)===null||e===void 0?void 0:e.monetize_by)==="tutor"},(0,n.tZ)(c.Qr,{name:"course_selling_option",control:u.control,render:function t(e){return(0,n.tZ)(Me,Xd({},e,{label:(0,a.__)("Purchase Options","tutor-pro"),options:f}))}})),(0,n.tZ)(k.Z,{when:!["subscription"].includes(d)||((r=j.y.settings)===null||r===void 0?void 0:r.monetize_by)==="wc"},(0,n.tZ)("div",{css:of.coursePriceWrapper},(0,n.tZ)("div",{css:of.regularPrice},(0,n.tZ)("label",null,(0,a.__)("Regular Price","tutor-pro")),(0,n.tZ)("div",null,(ef===null||ef===void 0?void 0:ef.symbol)||"$"," ",s)),(0,n.tZ)(c.Qr,{name:"details.subtotal_raw_sale_price",control:u.control,rules:{validate:function t(e){if(!e){return true}if(Number(e)>=Number(s)){return(0,a.__)("Sale price must be less than regular price","tutor-pro")}return true}},render:function t(e){return(0,n.tZ)(xl,Xd({},e,{label:(0,a.__)("Sale Price","tutor-pro"),content:(ef===null||ef===void 0?void 0:ef.symbol)||"$",placeholder:(0,a.__)("0","tutor-pro"),type:"number",loading:!!l&&!e.field.value,selectOnFocus:true,contentCss:Pt.i.inputCurrencyStyle}))}}))),(0,n.tZ)(k.Z,{when:(0,b.ro)(S.AO.SUBSCRIPTION)&&((o=j.y.settings)===null||o===void 0?void 0:o.monetize_by)==="tutor"},(0,n.tZ)(qd,{courseId:tf,isBundle:true})))};const nf=rf;var of={priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 1fr;place-items:start;gap:",h.W0[16],";"+(true?"":0),true?"":0),regularPrice:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[4],";label{",y.c.caption(),";color:",h.Jv.text.title,";}div{",y.c.body(),";",Pt.i.display.flex(),";align-items:center;color:",h.Jv.text.title,";height:40px;}"+(true?"":0),true?"":0)};var af=r(460);var uf=r(2391);var lf=r(2592);var cf=r(462);var sf=r(2582);var df=r(8901);function ff(){ff=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ff.apply(this,arguments)}function pf(t,e){return gf(t)||bf(t,e)||hf(t,e)||vf()}function vf(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function hf(t,e){if(!t)return;if(typeof t==="string")return yf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yf(t,e)}function yf(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bf(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function gf(t){if(Array.isArray(t))return t}function mf(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var wf=function t(){var e,r,o,i;var l=(0,c.Gc)();var f=(0,c.qo)({name:"post_date"});var p=(e=(0,c.qo)({name:"schedule_date"}))!==null&&e!==void 0?e:"";var h=(r=(0,c.qo)({name:"schedule_time"}))!==null&&r!==void 0?r:(0,s.WU)((0,af.T)(new Date,1),S.E_.hoursMinutes);var y=(o=(0,c.qo)({name:"isScheduleEnabled"}))!==null&&o!==void 0?o:false;var b=(i=(0,c.qo)({name:"showScheduleForm"}))!==null&&i!==void 0?i:false;var g=(0,u.useState)(p&&h&&(0,uf.J)(new Date("".concat(p," ").concat(h)))?(0,s.WU)(new Date("".concat(p," ").concat(h)),S.E_.yearMonthDayHourMinuteSecond24H):""),m=pf(g,2),w=m[0],x=m[1];var _=function t(){l.setValue("schedule_date","",{shouldDirty:true});l.setValue("schedule_time","",{shouldDirty:true});l.setValue("showScheduleForm",true,{shouldDirty:true})};var Z=function t(){var e=(0,lf.R)(new Date(f),new Date);l.setValue("schedule_date",e&&w?(0,s.WU)((0,cf.D)(w),S.E_.yearMonthDay):"",{shouldDirty:true});l.setValue("schedule_time",e&&w?(0,s.WU)((0,cf.D)(w),S.E_.hoursMinutes):"",{shouldDirty:true})};var O=function t(){if(!p||!h){return}l.setValue("showScheduleForm",false,{shouldDirty:true});x((0,s.WU)(new Date("".concat(p," ").concat(h)),S.E_.yearMonthDayHourMinuteSecond24H))};(0,u.useEffect)((function(){if(y&&b){l.setFocus("schedule_date")}}),[b,y]);return(0,n.tZ)("div",{css:_f.scheduleOptions},(0,n.tZ)(c.Qr,{name:"isScheduleEnabled",control:l.control,render:function t(e){return(0,n.tZ)(En,ff({},e,{label:(0,a.__)("Schedule","tutor-pro"),onChange:function t(e){if(!e&&p&&h){l.setValue("showScheduleForm",false,{shouldDirty:true})}}}))}}),y&&b&&(0,n.tZ)("div",{css:_f.formWrapper},(0,n.tZ)("div",{css:Pt.i.dateAndTimeWrapper},(0,n.tZ)(c.Qr,{name:"schedule_date",control:l.control,rules:{required:(0,a.__)("Schedule date is required.","tutor-pro"),validate:{invalidDateRule:Fn,futureDate:function t(e){if((0,lf.R)(new Date("".concat(e)),(0,sf.b)(new Date))){return(0,a.__)("Schedule date should be in the future.","tutor-pro")}return true}}},render:function t(e){return(0,n.tZ)(zc,ff({},e,{isClearable:false,placeholder:(0,a.__)("Select date","tutor-pro"),disabledBefore:(0,s.WU)(new Date,S.E_.yearMonthDay),onChange:function t(){l.setFocus("schedule_time")},dateFormat:S.E_.monthDayYear}))}}),(0,n.tZ)(c.Qr,{name:"schedule_time",control:l.control,rules:{required:(0,a.__)("Schedule time is required.","tutor-pro"),validate:{invalidTimeRule:zn,futureDate:function t(e){if((0,lf.R)(new Date("".concat(l.watch("schedule_date")," ").concat(e)),new Date)){return(0,a.__)("Schedule time should be in the future.","tutor-pro")}return true}}},render:function t(e){return(0,n.tZ)(cs,ff({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}})),(0,n.tZ)("div",{css:_f.scheduleButtonsWrapper},(0,n.tZ)(v.Z,{variant:"tertiary",size:"small",onClick:Z,disabled:!p&&!h||(0,uf.J)(new Date("".concat(p," ").concat(h)))&&(0,df.x)(new Date("".concat(p," ").concat(h)),new Date(w))},(0,a.__)("Cancel","tutor-pro")),(0,n.tZ)(v.Z,{variant:"secondary",size:"small",onClick:l.handleSubmit(O),disabled:!p||!h},(0,a.__)("Ok","tutor-pro")))),y&&!b&&(0,n.tZ)("div",{css:_f.scheduleInfoWrapper},(0,n.tZ)("div",{css:_f.scheduledFor},(0,n.tZ)("div",{css:_f.scheduleLabel},(0,a.__)("Scheduled for","tutor-pro")),(0,n.tZ)("div",{css:_f.scheduleInfoButtons},(0,n.tZ)("button",{type:"button",css:Pt.i.actionButton,onClick:_},(0,n.tZ)(d.Z,{name:"delete",width:24,height:24})),(0,n.tZ)("button",{type:"button",css:Pt.i.actionButton,onClick:function t(){l.setValue("showScheduleForm",true,{shouldDirty:true})}},(0,n.tZ)(d.Z,{name:"edit",width:24,height:24})))),(0,n.tZ)(k.Z,{when:p&&h&&(0,uf.J)(new Date("".concat(p," ").concat(h)))},(0,n.tZ)("div",{css:_f.scheduleInfo},(0,a.sprintf)((0,a.__)("%s at %s","tutor-pro"),(0,s.WU)((0,cf.D)(p),S.E_.monthDayYear),h)))))};const xf=wf;var _f={scheduleOptions:(0,n.iv)("padding:",h.W0[12],";border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";gap:",h.W0[8],";background-color:",h.Jv.bg.white,";"+(true?"":0),true?"":0),formWrapper:(0,n.iv)("margin-top:",h.W0[16],";"+(true?"":0),true?"":0),scheduleButtonsWrapper:(0,n.iv)("display:flex;gap:",h.W0[12],";margin-top:",h.W0[8],";button{width:100%;span{justify-content:center;}}"+(true?"":0),true?"":0),scheduleInfoWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";margin-top:",h.W0[12],";"+(true?"":0),true?"":0),scheduledFor:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,scheduleLabel:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),scheduleInfoButtons:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[8],";"+(true?"":0),true?"":0),scheduleInfo:(0,n.iv)(y.c.caption(),";background-color:",h.Jv.background.status.processing,";padding:",h.W0[8],";border-radius:",h.E0[4],";text-align:center;"+(true?"":0),true?"":0)};function Zf(t){"@babel/helpers - typeof";return Zf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zf(t)}var Of,Sf;function kf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?kf(Object(r),!0).forEach((function(e){Ef(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ef(t,e,r){e=Cf(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Cf(t){var e=Af(t,"string");return Zf(e)==="symbol"?e:String(e)}function Af(t,e){if(Zf(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Zf(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Pf(){Pf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Pf.apply(this,arguments)}var Wf=(0,$d.X)();var Lf=!!j.y.tutor_pro_url;var If=((Of=j.y.settings)===null||Of===void 0?void 0:Of.chatgpt_enable)==="on";var Df=(0,b.ro)(S.AO.SUBSCRIPTION)&&((Sf=j.y.settings)===null||Sf===void 0?void 0:Sf.membership_only_mode);var Tf=[{label:(0,a.__)("Show Discount % off","tutor-pro"),value:"in_percentage"},{label:(0,a.sprintf)((0,a.__)("Show discount amount (%s)","tutor-pro"),j.y.tutor_currency.symbol),value:"in_amount"},{label:(0,a.__)("Show None","tutor-pro"),value:"none"}];var Mf=function t(){var e=(0,c.Gc)();var r=(0,i.y)({queryKey:["CourseBundle",Wf]});var o=e.watch("details.authors");var u=e.watch("post_modified");var l=e.watch("visibility");return(0,n.tZ)("div",{css:Nf.sidebar},(0,n.tZ)("div",{css:Nf.statusAndDate},(0,n.tZ)(c.Qr,{name:"visibility",control:e.control,render:function t(o){return(0,n.tZ)(Me,Pf({},o,{label:(0,a.__)("Visibility","tutor-pro"),placeholder:(0,a.__)("Select visibility status","tutor-pro"),options:S.R_,leftIcon:(0,n.tZ)(d.Z,{name:"eye",width:32,height:32}),loading:!!r&&!o.field.value,onChange:function t(){e.setValue("post_password","")}}))}}),(0,n.tZ)(k.Z,{when:u},(function(t){return(0,n.tZ)("div",{css:Nf.updatedOn},(0,a.sprintf)((0,a.__)("Last updated on %s","tutor-pro"),(0,s.WU)(new Date(t),S.E_.dayMonthYear)||""))}))),(0,n.tZ)(k.Z,{when:l==="password_protected"},(0,n.tZ)(c.Qr,{name:"post_password",control:e.control,rules:{required:(0,a.__)("Password is required","tutor-pro")},render:function t(e){return(0,n.tZ)(Zo,Pf({},e,{label:(0,a.__)("Password","tutor-pro"),placeholder:(0,a.__)("Enter password","tutor-pro"),type:"password",isPassword:true,loading:!!r&&!e.field.value}))}})),(0,n.tZ)(xf,null),(0,n.tZ)(c.Qr,{name:"thumbnail",control:e.control,render:function t(e){return(0,n.tZ)(hl,Pf({},e,{label:(0,a.__)("Featured Image","tutor-pro"),buttonText:(0,a.__)("Upload Thumbnail","tutor-pro"),infoText:(0,a.sprintf)((0,a.__)("JPEG, PNG, GIF, and WebP formats, up to %s","tutor-pro"),j.y.max_upload_size),generateWithAi:!Lf||If,loading:!!r&&!e.field.value}))}}),(0,n.tZ)(k.Z,{when:!Df},(0,n.tZ)(nf,null)),(0,n.tZ)(c.Qr,{name:"ribbon_type",control:e.control,render:function t(e){return(0,n.tZ)(Me,Pf({},e,{label:(0,a.__)("Select ribbon to display","tutor-pro"),placeholder:(0,a.__)("Select ribbon","tutor-pro"),options:Tf,loading:!!r&&!e.field.value}))}}),(0,n.tZ)(c.Qr,{name:"details.categories",control:e.control,render:function t(e){var o;return(0,n.tZ)(li,Pf({},e,{field:jf(jf({},e.field),{},{value:(o=e.field.value)===null||o===void 0?void 0:o.map((function(t){return t.term_id}))}),label:(0,a.__)("Categories","tutor-pro"),disabled:true,loading:!!r&&!e.field.value}))}}),(0,n.tZ)(k.Z,{when:o.length>0},(0,n.tZ)("div",{css:Nf.labelWithContent},(0,n.tZ)("label",null,(0,a.__)("Instructors")),(0,n.tZ)("div",{css:Nf.instructorsWrapper},(0,n.tZ)(le,{each:o},(function(t){return(0,n.tZ)("div",{key:t.user_id,css:Nf.instructor},(0,n.tZ)(k.Z,{when:t.avatar_url,fallback:(0,n.tZ)("div",{"data-avatar":true},t.display_name.charAt(0).toUpperCase())},(0,n.tZ)("img",{src:t.avatar_url,alt:t.display_name,"data-avatar":true})),(0,n.tZ)("div",null,(0,n.tZ)("div",{"data-name":"instructor-name"},t.display_name),(0,n.tZ)("div",{"data-name":"instructor-email"},t.user_email)))}))))))};const Jf=Mf;var Nf={sidebar:(0,n.iv)("border-left:1px solid ",h.Jv.stroke.divider,";min-height:calc(100vh - ",h.J9,"px);padding-left:",h.W0[32],";padding-block:",h.W0[24],";display:flex;flex-direction:column;gap:",h.W0[16],";",h.Uo.smallTablet,"{border-left:none;border-top:1px solid ",h.Jv.stroke.divider,";padding-block:",h.W0[16],";padding-left:0;}"+(true?"":0),true?"":0),statusAndDate:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[4],";"+(true?"":0),true?"":0),updatedOn:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),priceRadioGroup:(0,n.iv)(Pt.i.display.flex(),";align-items:center;gap:",h.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)(Pt.i.display.flex(),";align-items:flex-start;gap:",h.W0[16],";"+(true?"":0),true?"":0),labelWithContent:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[4],";label{",y.c.caption(),";color:",h.Jv.text.title,";}"+(true?"":0),true?"":0),categoriesWrapper:(0,n.iv)(Pt.i.display.flex(),";gap:",h.W0[8],";"+(true?"":0),true?"":0),category:(0,n.iv)("padding:",h.W0[4]," ",h.W0[8],";border-radius:",h.E0[24],";background-color:",h.Jv.surface.wordpress,";",y.c.small(),";color:",h.Jv.text.title,";"+(true?"":0),true?"":0),instructorsWrapper:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[8],";"+(true?"":0),true?"":0),instructor:(0,n.iv)(Pt.i.display.flex(),";align-items:center;gap:",h.W0[10],";padding:",h.W0[8]," ",h.W0[12],";border-radius:",h.E0[4],";background-color:",h.Jv.background.white,";[data-avatar]{width:40px;height:40px;",Pt.i.flexCenter(),";border-radius:",h.E0.circle,";border:1px solid ",h.Jv.stroke["default"],";background-color:",h.Jv.background["default"],";}[data-name='instructor-name']{",y.c.caption("medium"),";}[data-name='instructor-email']{",y.c.small(),";color:",h.Jv.text.subdued,";}"+(true?"":0),true?"":0)};var Ff=l().forwardRef((function(t,e){var r=t.children,o=t.className,i=t.bordered,a=i===void 0?false:i,u=t.wrapperCss;return(0,n.tZ)("div",{ref:e,className:o,css:[Rf.wrapper(a),u,true?"":0,true?"":0]},r)}));Ff.displayName="Box";var Bf=l().forwardRef((function(t,e){var r=t.children,o=t.className,i=t.separator,a=i===void 0?false:i,u=t.tooltip;return(0,n.tZ)("div",{ref:e,className:o,css:Rf.title(a)},(0,n.tZ)("span",null,r),(0,n.tZ)(k.Z,{when:u},(0,n.tZ)(Wt.Z,{content:u},(0,n.tZ)(d.Z,{name:"info",width:20,height:20}))))}));Bf.displayName="BoxTitle";var zf=l().forwardRef((function(t,e){var r=t.children,o=t.className;return(0,n.tZ)("div",{ref:e,className:o,css:Rf.subtitle},(0,n.tZ)("span",null,r))}));zf.displayName="BoxSubtitle";var Rf={wrapper:function t(e){return(0,n.iv)("background-color:",h.Jv.background.white,";border-radius:",h.E0[8],";padding:",h.W0[12]," ",h.W0[20]," ",h.W0[20],";",e&&(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},title:function t(e){return(0,n.iv)(y.c.body("medium"),";color:",h.Jv.text.title,";display:flex;gap:",h.W0[4],";align-items:center;",e&&(0,n.iv)("border-bottom:1px solid ",h.Jv.stroke.divider,";padding:",h.W0[12]," ",h.W0[20],";"+(true?"":0),true?"":0)," &>div{height:20px;svg{color:",h.Jv.icon.hints,";}}&>span{display:inline-block;}"+(true?"":0),true?"":0)},subtitle:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0)};var Uf=function t(e){var r=e.onAddCourse,o=e.selectedCourses,i=e.totalEnrolled,u=i===void 0?0:i;return(0,n.tZ)("div",{css:Qf.wrapper},(0,n.tZ)("div",{css:Qf.left},(0,n.tZ)("div",{css:y.c.body("medium")},o.length>1?(0,a.sprintf)((0,a.__)("%d Courses selected","tutor-pro"),o.length):(0,a.sprintf)((0,a.__)("%d Course selected","tutor-pro"),o.length))),(0,n.tZ)(Wt.Z,{content:(0,a.__)("You cannot add/remove course(s) from a course bundle with enrolled students as it may disrupt the learning experience.","tutor-pro"),delay:200,disabled:u<=0},(0,n.tZ)(v.Z,{"data-cy":"add-course",variant:"secondary",isOutlined:true,icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),buttonCss:Qf.addCourseButton,disabled:u>0,onClick:r},(0,a.__)("Add Courses","tutor-pro"))))};const Gf=Uf;var Qf={wrapper:(0,n.iv)("display:flex;justify-content:space-between;align-items:center;padding:0 ",h.W0[20]," ",h.W0[12]," ",h.W0[20],";border-bottom:1px solid ",h.Jv.stroke.divider,";"+(true?"":0),true?"":0),left:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),addCourseButton:(0,n.iv)("outline:1px solid ",h.Jv.stroke.border,";&:hover{outline:1px solid ",h.Jv.stroke.border,";}"+(true?"":0),true?"":0)};var Yf=r(9752);var qf=r(2339);var Hf=r(5587);var Vf=r(4285);var $f=r(9776);function Kf(t){"@babel/helpers - typeof";return Kf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kf(t)}function Xf(){Xf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Xf.apply(this,arguments)}function tp(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */tp=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Kf(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function ep(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function rp(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ep(i,n,o,a,u,"next",t)}function u(t){ep(i,n,o,a,u,"throw",t)}a(undefined)}))}}function np(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var op=(0,$d.X)();var ip=function t(e){var r=e.course,o=e.index,i=e.isOverlay;var u=(0,Hf.nB)({id:r.id}),l=u.attributes,s=u.listeners,f=u.setNodeRef,p=u.transform,y=u.transition,b=u.isDragging;var g=(0,c.Gc)();var m=(0,$f.V5)();var w=g.watch("total_enrolled");var x={transform:Vf.ux.Transform.toString(p),transition:y,opacity:b?.3:1,background:b?h.Jv.stroke.hover:undefined};var _=function(){var t=rp(tp().mark((function t(e){var r;return tp().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:n.next=2;return m.mutateAsync({ID:op,course_ids:[e],user_action:"remove_course"});case 2:r=n.sent;if(r.data){g.setValue("details",r.data)}case 4:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();return(0,n.tZ)("div",Xf({},l,{ref:f,style:x,css:lp.wrapper({isOverlay:i,isRemoveBtnDisabled:Number(w)>0})}),(0,n.tZ)("div",{css:lp.left},(0,n.tZ)("button",Xf({},s,{"data-drag-button":true,css:Pt.i.resetButton}),(0,n.tZ)(d.Z,{name:"dragVertical",width:24,height:24})),(0,n.tZ)("span",{"data-index":true},o),(0,n.tZ)("img",{src:r.image,alt:r.title}),(0,n.tZ)("p",null,r.title)),(0,n.tZ)("div",{css:lp.right},(0,n.tZ)(Wt.Z,{content:(0,a.__)("You cannot remove course(s) from a course bundle with enrolled students as it may disrupt the learning experience.","tutor-pro"),delay:200,disabled:w<=0},(0,n.tZ)(v.Z,{variant:"text","data-cy":"remove-course","data-cross-button":true,disabled:w>0,onClick:function t(){return _(r.id)},loading:m.isPending},(0,n.tZ)(d.Z,{name:"cross",width:24,height:24}))),(0,n.tZ)(k.Z,{when:r.is_purchasable,fallback:(0,n.tZ)("span",{"data-price":true,css:lp.price({hasSalePrice:false})},(0,a.__)("Free","tutor-pro"))},(0,n.tZ)(k.Z,{when:r.sale_price,fallback:(0,n.tZ)("span",{"data-price":true,css:lp.price({hasSalePrice:false})},r.regular_price)},(0,n.tZ)("span",{"data-price":true,css:lp.price({hasSalePrice:true})},r.regular_price),(0,n.tZ)("span",{"data-price":true,css:lp.price({hasSalePrice:false})},r.sale_price)))))};const ap=ip;var up=true?{name:"1sfig4b",styles:"cursor:not-allowed"}:0;var lp={wrapper:function t(e){var r=e.isOverlay,o=r===void 0?false:r,i=e.isRemoveBtnDisabled,a=i===void 0?false:i;return(0,n.iv)(Pt.i.display.flex(),";justify-content:space-between;align-items:center;padding:",h.W0[16]," ",h.W0[20],";border-bottom:1px solid ",h.Jv.stroke.divider,";gap:",h.W0[28],";background-color:",h.Jv.background.white,";[data-drag-button]{cursor:grab;display:none;color:",h.Jv.icon.hints,";}[data-cross-button]{display:none;color:",h.Jv.color.black[50],";padding:",h.W0[4],";box-shadow:none;transition:color 0.3s ease-in-out;",a&&up," &:hover{color:",!a&&h.Jv.color.black[100],";}}",o&&(0,n.iv)("box-shadow:",h.AF.drag,";border-bottom:none;border-radius:",h.E0.card,";background-color:",h.Jv.background.hover,";cursor:grabbing;"+(true?"":0),true?"":0)," &:hover{background-color:",h.Jv.background.hover,";[data-index],[data-price]{display:none;}[data-drag-button],[data-cross-button]{display:block;}}"+(true?"":0),true?"":0)},left:(0,n.iv)(Pt.i.display.flex(),";align-items:center;gap:",h.W0[16],";img{width:76px;height:",h.W0[48],";object-fit:cover;object-position:center;border-radius:",h.E0[2],";flex-shrink:0;}p,span{",y.c.caption(),";",Pt.i.text.ellipsis(2),";}span{flex-shrink:0;width:",h.W0[24],";",Pt.i.flexCenter(),";}"+(true?"":0),true?"":0),right:(0,n.iv)(Pt.i.display.flex(),";align-items:center;justify-content:flex-end;flex-shrink:0;max-width:120px;width:100%;gap:",h.W0[8],";position:relative;"+(true?"":0),true?"":0),price:function t(e){var r=e.hasSalePrice,o=r===void 0?false:r;return(0,n.iv)(y.c.caption(),";color:",o?h.Jv.text.subdued:h.Jv.text.primary,";text-decoration:",o?"line-through":"none",";"+(true?"":0),true?"":0)}};function cp(t,e){return vp(t)||pp(t,e)||dp(t,e)||sp()}function sp(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function dp(t,e){if(!t)return;if(typeof t==="string")return fp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fp(t,e)}function fp(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pp(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function vp(t){if(Array.isArray(t))return t}var hp=function t(e){var r=e.courses,o=e.onSort;var i=(0,u.useState)(null),a=cp(i,2),l=a[0],c=a[1];var s=(0,u.useMemo)((function(){return r.find((function(t){return t.id===l}))}),[l,r]);var d=(0,Yf.Dy)((0,Yf.VT)(Yf.we,{activationConstraint:{distance:10}}),(0,Yf.VT)(Yf.Lg,{coordinateGetter:Hf.is}));return(0,n.tZ)(Yf.LB,{sensors:d,collisionDetection:Yf.pE,measuring:Hs,modifiers:[qf.hg],onDragStart:function t(e){c(e.active.id)},onDragEnd:function t(e){var n=e.active,i=e.over;if(!i||n.id===i.id){c(null);return}var a=r.findIndex((function(t){return t.id===n.id}));var u=r.findIndex((function(t){return t.id===i.id}));o(a,u)}},(0,n.tZ)(Hf.Fo,{items:r,strategy:Hf.qw},(0,n.tZ)(le,{each:r},(function(t,e){return(0,n.tZ)(ap,{key:t.id,course:t,index:e+1})}))),(0,kl.createPortal)((0,n.tZ)(Yf.y9,null,(0,n.tZ)(k.Z,{when:s},(function(t){return(0,n.tZ)(ap,{course:t,index:0,isOverlay:true})}))),document.body))};const yp=hp;var bp=function t(){var e=(0,c.Gc)();var r=e.watch("details.overview");var o={total_duration:"clock",total_quizzes:"questionCircle",total_video_contents:"videoCamera",total_resources:"download"};var i={total_duration:(0,a.__)("Total Duration","tutor-pro"),total_quizzes:(0,a.__)("Quiz Papers","tutor-pro"),total_video_contents:(0,a.__)("Lesson Content","tutor-pro"),total_resources:(0,a.__)("Downloadable Resources","tutor-pro")};return(0,n.tZ)("div",{css:mp.wrapper},(0,n.tZ)("div",{css:mp.title},(0,a.__)("Selection Overview","tutor-pro")),(0,n.tZ)("div",{css:mp.overview},Object.keys(o).map((function(t){var e=r[t];return(0,n.tZ)("div",{css:mp.overviewItem,key:t},(0,n.tZ)(d.Z,{name:o[t],width:32,height:32}),(0,n.tZ)("span",null,t==="total_duration"?String(e).replace(/:\d{2}$/,""):e),(0,n.tZ)("span",null,i[t]))}))))};const gp=bp;var mp={wrapper:(0,n.iv)("padding:",h.W0[12]," ",h.W0[20]," 0 ",h.W0[20],";"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.body("medium"),";padding-bottom:",h.W0[12],";"+(true?"":0),true?"":0),overview:(0,n.iv)("display:grid;grid-template-columns:1fr 1fr;gap:",h.W0[4],";"+(true?"":0),true?"":0),overviewItem:(0,n.iv)(Pt.i.display.flex(),";gap:",h.W0[8],";align-items:center;",y.c.caption(),";svg{color:",h.Jv.icon["default"],";flex-shrink:0;}span:first-of-type:not(:only-of-type){font-weight:",h.Ue.semiBold,";flex-shrink:0;}"+(true?"":0),true?"":0)};function xp(t){"@babel/helpers - typeof";return xp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xp(t)}function _p(){_p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return _p.apply(this,arguments)}function Zp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Op(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Zp(Object(r),!0).forEach((function(e){Sp(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zp(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Sp(t,e,r){e=kp(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function kp(t){var e=jp(t,"string");return xp(e)==="symbol"?e:String(e)}function jp(t,e){if(xp(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(xp(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ep=function t(e){var r=e.onFilterItems;var o=at({defaultValues:{search:""}});var i=Y(o.watch("search"));(0,u.useEffect)((function(){r(Op({},i.length>0&&{search:i}))}),[r,i]);return(0,n.tZ)(c.Qr,{control:o.control,name:"search",render:function t(e){return(0,n.tZ)(xl,_p({},e,{content:(0,n.tZ)(d.Z,{name:"search",width:24,height:24}),placeholder:(0,a.__)("Search...","tutor-pro"),showVerticalBar:false}))}})};const Cp=Ep;function Ap(t){"@babel/helpers - typeof";return Ap="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ap(t)}function Pp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Wp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pp(Object(r),!0).forEach((function(e){Lp(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pp(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Lp(t,e,r){e=Ip(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ip(t){var e=Dp(t,"string");return Ap(e)==="symbol"?e:String(e)}function Dp(t,e){if(Ap(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ap(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Tp(t,e){return Bp(t)||Fp(t,e)||Jp(t,e)||Mp()}function Mp(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Jp(t,e){if(!t)return;if(typeof t==="string")return Np(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Np(t,e)}function Np(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Fp(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Bp(t){if(Array.isArray(t))return t}var zp=function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},r=e.limit,n=r===void 0?S.gK:r;var o=(0,u.useState)({page:1,sortProperty:"",sortDirection:undefined,filter:{}}),i=Tp(o,2),a=i[0],l=i[1];var c=a;var s=n*Math.max(0,c.page-1);var d=(0,u.useCallback)((function(t){l((function(e){return Wp(Wp({},e),t)}))}),[l]);var f=function t(e){return d({page:e})};var p=(0,u.useCallback)((function(t){return d({page:1,filter:t})}),[d]);var v=function t(e){var r={};if(e!==c.sortProperty){r={sortDirection:"asc",sortProperty:e}}else{r={sortDirection:c.sortDirection==="asc"?"desc":"asc",sortProperty:e}}d(r)};return{pageInfo:c,onPageChange:f,onColumnSort:v,offset:s,itemsPerPage:n,onFilterItems:p}};function Rp(t,e){return qp(t)||Yp(t,e)||Gp(t,e)||Up()}function Up(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Gp(t,e){if(!t)return;if(typeof t==="string")return Qp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qp(t,e)}function Qp(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Yp(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function qp(t){if(Array.isArray(t))return t}var Hp=function t(e){var r=e.currentPage,o=e.onPageChange,i=e.totalItems,l=e.itemsPerPage;var c=Math.max(Math.ceil(i/l),1);var s=(0,u.useState)(""),f=Rp(s,2),p=f[0],v=f[1];(0,u.useEffect)((function(){v(r.toString())}),[r]);var h=function t(e){if(e<1||e>c){return}o(e)};return(0,n.tZ)("div",{css:$p.wrapper},(0,n.tZ)("div",{css:$p.pageStatus},(0,a.__)("Page","tutor"),(0,n.tZ)("span",null,(0,n.tZ)("input",{type:"text",css:$p.paginationInput,value:p,onChange:function t(e){var r=e.currentTarget.value;var n=r.replace(/[^0-9]/g,"");var i=Number(n);if(i>0&&i<=c){v(n);o(i)}else if(!n){v(n)}},autoComplete:"off"})),(0,a.__)("of","tutor")," ",(0,n.tZ)("span",null,c)),(0,n.tZ)("div",{css:$p.pageController},(0,n.tZ)("button",{type:"button",css:$p.paginationButton,onClick:function t(){return h(r-1)},disabled:r===1},(0,n.tZ)(d.Z,{name:!S.dZ?"chevronLeft":"chevronRight",width:32,height:32})),(0,n.tZ)("button",{type:"button",css:$p.paginationButton,onClick:function t(){return h(r+1)},disabled:r===c},(0,n.tZ)(d.Z,{name:!S.dZ?"chevronRight":"chevronLeft",width:32,height:32}))))};const Vp=Hp;var $p={wrapper:(0,n.iv)("display:flex;justify-content:end;align-items:center;flex-wrap:wrap;gap:",h.W0[8],";height:36px;"+(true?"":0),true?"":0),pageStatus:(0,n.iv)(y.c.body()," color:",h.Jv.text.title,";min-width:100px;"+(true?"":0),true?"":0),paginationInput:(0,n.iv)("outline:0;border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[6],";margin:0 ",h.W0[8],";color:",h.Jv.text.subdued,";padding:8px 12px;width:72px;&::-webkit-outer-spin-button,&::-webkit-inner-spin-button{-webkit-appearance:none;margin:",h.W0[0],";}&[type='number']{-moz-appearance:textfield;}"+(true?"":0),true?"":0),pageController:(0,n.iv)("gap:",h.W0[8],";display:flex;justify-content:center;align-items:center;height:100%;"+(true?"":0),true?"":0),paginationButton:(0,n.iv)(Pt.i.resetButton,";background:",h.Jv.background.white,";color:",h.Jv.icon["default"],";border-radius:",h.E0[6],";height:32px;width:32px;display:grid;place-items:center;transition:background-color 0.2s ease-in-out,color 0.3s ease-in-out;svg{color:",h.Jv.icon["default"],";}&:hover{background:",h.Jv.background["default"],";&>svg{color:",h.Jv.icon.brand,";}}&:disabled{background:",h.Jv.background.white,";&>svg{color:",h.Jv.icon.disable["default"],";}}"+(true?"":0),true?"":0)};function Kp(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Xp={bodyRowSelected:h.Jv.background.active,bodyRowHover:h.Jv.background.hover};var tv=true?{name:"1azakc",styles:"text-align:center"}:0;var ev=function t(e){var r=e.columns,o=e.data,i=e.entireHeader,a=i===void 0?null:i,u=e.headerHeight,l=u===void 0?60:u,c=e.noHeader,s=c===void 0?false:c,f=e.isStriped,p=f===void 0?false:f,v=e.isRounded,h=v===void 0?false:v,y=e.stripedBySelectedIndex,g=y===void 0?[]:y,m=e.colors,w=m===void 0?{}:m,x=e.isBordered,_=x===void 0?true:x,Z=e.loading,O=Z===void 0?false:Z,S=e.itemsPerPage,k=S===void 0?1:S,j=e.querySortProperty,E=e.querySortDirection,C=E===void 0?"asc":E,A=e.onSortClick,P=e.renderInLastRow,W=e.rowStyle;var L=function t(e,o){return(0,n.tZ)("tr",{key:e,css:[ov.tableRow({isBordered:_,isStriped:p}),ov.bodyTr({colors:w,isSelected:g.includes(e),isRounded:h}),W,true?"":0,true?"":0]},r.map((function(t,e){return(0,n.tZ)("td",{key:e,css:[ov.td,{width:t.width},true?"":0,true?"":0]},o(t))})))};var I=function t(e){var r=null;var o=e.sortProperty;if(!o){return e.Header}if(e.sortProperty===j){if(C==="asc"){r=(0,n.tZ)(d.Z,{name:"chevronDown"})}else{r=(0,n.tZ)(d.Z,{name:"chevronUp"})}}return(0,n.tZ)("button",{type:"button",css:ov.headerWithIcon,onClick:function t(){return A===null||A===void 0?void 0:A(o)}},e.Header,r&&r)};var D=function t(){if(a){return(0,n.tZ)("th",{css:ov.th,colSpan:r.length},a)}return r.map((function(t,e){if(t.Header!==null){return(0,n.tZ)("th",{key:e,css:[ov.th,{width:t.width},true?"":0,true?"":0],colSpan:t.headerColSpan},I(t))}}))};var T=function t(){if(O){return(0,b.w6)(k).map((function(t){return L(t,(function(){return(0,n.tZ)(Ke,{animation:true,height:20,width:"".concat((0,b.sZ)(40,80),"%")})}))}))}if(!o.length){return(0,n.tZ)("tr",{css:ov.tableRow({isBordered:false,isStriped:false})},(0,n.tZ)("td",{colSpan:r.length,css:[ov.td,tv,true?"":0,true?"":0]},"No Data!"))}var e=o.map((function(t,e){return L(e,(function(r){return"Cell"in r?r.Cell(t,e):r.accessor(t,e)}))}));if(P){P=(0,n.tZ)("tr",{key:e.length},(0,n.tZ)("td",{css:ov.td},P));e.push(P)}return e};return(0,n.tZ)("div",{css:ov.tableContainer({isRounded:h})},(0,n.tZ)("table",{css:ov.table},!s&&(0,n.tZ)("thead",null,(0,n.tZ)("tr",{css:[ov.tableRow({isBordered:_,isStriped:p}),{height:l},true?"":0,true?"":0]},D())),(0,n.tZ)("tbody",null,T())))};const rv=ev;var nv=true?{name:"1hr9znz",styles:":last-of-type{border-bottom:none;}"}:0;var ov={tableContainer:function t(e){var r=e.isRounded;return(0,n.iv)("display:block;width:100%;overflow-x:auto;",r&&(0,n.iv)("border:1px solid ",h.Jv.stroke.divider,";border-radius:",h.E0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},headerWithIcon:(0,n.iv)(Pt.i.resetButton,";",y.c.body(),";color:",h.Jv.text.subdued,";display:flex;gap:",h.W0[4],";align-items:center;"+(true?"":0),true?"":0),table:true?{name:"1k58b2x",styles:"width:100%;border-collapse:collapse;border:none"}:0,tableRow:function t(e){var r=e.isBordered,o=e.isStriped;return(0,n.iv)(r&&(0,n.iv)("border-bottom:1px solid ",h.Jv.stroke.divider,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("&:nth-of-type(even){background-color:",h.Jv.background.active,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},th:(0,n.iv)(y.c.body(),";background-color:",h.Jv.background.white,";color:",h.Jv.text.primary,";padding:0 ",h.W0[16],";border:none;"+(true?"":0),true?"":0),bodyTr:function t(e){var r=e.colors,o=e.isSelected,i=e.isRounded;var a=r.bodyRowDefault,u=r.bodyRowSelectedHover,l=r.bodyRowHover,c=l===void 0?Xp.bodyRowHover:l,s=r.bodyRowSelected,d=s===void 0?Xp.bodyRowSelected:s;return(0,n.iv)(a&&(0,n.iv)("background-color:",a,";"+(true?"":0),true?"":0)," &:hover{background-color:",o&&u?u:c,";}",o&&(0,n.iv)("background-color:",d,";"+(true?"":0),true?"":0)," ",i&&nv,";"+(true?"":0),true?"":0)},td:(0,n.iv)(y.c.body(),";padding:",h.W0[16],";border:none;"+(true?"":0),true?"":0)};var iv=r(4139);function av(t){"@babel/helpers - typeof";return av="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},av(t)}function uv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function lv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uv(Object(r),!0).forEach((function(e){cv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function cv(t,e,r){e=sv(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function sv(t){var e=dv(t,"string");return av(e)==="symbol"?e:String(e)}function dv(t,e){if(av(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(av(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var fv=function t(e){return St.R.get(kt.Z.GET_COURSE_LIST,{params:e})};var pv=function t(e){var r=e.params,n=e.isEnabled;return(0,_t.a)({queryKey:["PrerequisiteCourses",r],queryFn:function t(){return fv(lv({exclude:r.exclude,limit:r.limit,offset:r.offset,filter:r.filter},r.post_status&&{post_status:r.post_status})).then((function(t){return t.data}))},placeholderData:iv.Wk,enabled:n})};var vv=function t(e){var r=e.courseId,n=e.builder;return St.R.post(kt.Z.TUTOR_UNLINK_PAGE_BUILDER,{course_id:r,builder:n})};var hv=function t(){return(0,Zt.D)({mutationFn:vv})};var yv=function t(e){return wpAjaxInstance.get(endpoints.BUNDLE_LIST,{params:e})};var bv=function t(e){var r=e.params,n=e.isEnabled;return useQuery({queryKey:["PrerequisiteCourses",r],queryFn:function t(){return yv(lv({exclude:r.exclude,limit:r.limit,offset:r.offset,filter:r.filter},r.post_status&&{post_status:r.post_status})).then((function(t){return t.data}))},placeholderData:keepPreviousData,enabled:n})};const gv=r.p+"images/4d4615923a6630682b98f437e34c40a0-course-placeholder.png";var mv=r(7363);function wv(t){return Ov(t)||Zv(t)||_v(t)||xv()}function xv(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _v(t,e){if(!t)return;if(typeof t==="string")return Sv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sv(t,e)}function Zv(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Ov(t){if(Array.isArray(t))return Sv(t)}function Sv(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function kv(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var jv=function t(e){var r,o,i,u;var l=e.form,c=e.addedCourseIds;var s=l.watch("courses")||[];var d=zp(),f=d.pageInfo,p=d.onPageChange,v=d.itemsPerPage,h=d.offset,y=d.onFilterItems;var b=pv({params:{offset:h,limit:v,filter:f.filter,exclude:c},isEnabled:true});var g=(r=(o=b.data)===null||o===void 0?void 0:o.results)!==null&&r!==void 0?r:[];function m(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var e=s.map((function(t){return t.id}));var r=g.map((function(t){return t.id}));if(t){var n=g.filter((function(t){return!e.includes(t.id)}));l.setValue("courses",[].concat(wv(s),wv(n)));return}var o=s.filter((function(t){return!r.includes(t.id)}));l.setValue("courses",o)}function w(){return g.every((function(t){return s.map((function(t){return t.id})).includes(t.id)}))}var x=[{Header:(i=b.data)!==null&&i!==void 0&&i.results.length?(0,n.tZ)(Z,{onChange:m,checked:b.isLoading||b.isRefetching?false:w(),label:(0,a.__)("Name","tutor-pro"),labelCss:Cv.checkboxLabel,"data-cy":"select-all-courses"}):"#",Cell:function t(e){return(0,n.tZ)("div",{css:Cv.checkboxWrapper},(0,n.tZ)(Z,{onChange:function t(){var r=s.filter((function(t){return t.id!==e.id}));var n=(r===null||r===void 0?void 0:r.length)===s.length;if(n){l.setValue("courses",[].concat(wv(r),[e]))}else{l.setValue("courses",r)}},checked:s.map((function(t){return t.id})).includes(e.id),"data-cy":"select-course"}),(0,n.tZ)("div",{css:Cv.courseItemWrapper},(0,n.tZ)("img",{src:e.image||gv,css:Cv.thumbnail,alt:(0,a.__)("Course item","tutor-pro")}),(0,n.tZ)("div",{css:Cv.title},e.title)))}},{Header:(0,n.tZ)("div",{css:Cv.tablePriceLabel},(0,a.__)("Price","tutor-pro")),Cell:function t(e){return(0,n.tZ)("div",{css:Cv.priceWrapper},(0,n.tZ)("div",{css:Cv.price},(0,n.tZ)(k.Z,{when:e.is_purchasable,fallback:(0,a.__)("Free","tutor-pro")},(0,n.tZ)("span",null,e.sale_price?e.sale_price:e.regular_price),e.sale_price&&(0,n.tZ)("span",{css:Cv.discountPrice},e.regular_price))))}}];if(b.isLoading){return(0,n.tZ)(O.g4,null)}if(!b.data){return(0,n.tZ)("div",{css:Cv.errorMessage},(0,a.__)("Something went wrong","tutor-pro"))}return(0,n.tZ)(mv.Fragment,null,(0,n.tZ)("div",{css:Cv.tableActions},(0,n.tZ)(Cp,{onFilterItems:y})),(0,n.tZ)("div",{css:Cv.tableWrapper},(0,n.tZ)(rv,{columns:x,data:(u=b.data.results)!==null&&u!==void 0?u:[],itemsPerPage:v,loading:b.isFetching||b.isRefetching})),(0,n.tZ)("div",{css:Cv.paginatorWrapper},(0,n.tZ)(Vp,{currentPage:f.page,onPageChange:p,totalItems:b.data.total_items,itemsPerPage:v})))};const Ev=jv;var Cv={tableLabel:true?{name:"1flj9lk",styles:"text-align:left"}:0,tablePriceLabel:true?{name:"2qga7i",styles:"text-align:right"}:0,tableActions:(0,n.iv)("padding:",h.W0[20],";"+(true?"":0),true?"":0),tableWrapper:true?{name:"1uijx3y",styles:"max-height:calc(100vh - 350px);overflow:auto"}:0,checkboxWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[12],";"+(true?"":0),true?"":0),checkboxLabel:(0,n.iv)(y.c.body(),";color:",h.Jv.text.primary,";"+(true?"":0),true?"":0),paginatorWrapper:(0,n.iv)("margin:",h.W0[20]," ",h.W0[16],";"+(true?"":0),true?"":0),courseItemWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[16],";"+(true?"":0),true?"":0),bundleBadge:(0,n.iv)(y.c.tiny(),";display:inline-block;padding:0px ",h.W0[8],";background-color:#9342e7;color:",h.Jv.text.white,";border-radius:",h.E0[40],";"+(true?"":0),true?"":0),subscriptionBadge:(0,n.iv)(y.c.tiny(),";display:flex;align-items:center;width:fit-content;padding:0px ",h.W0[6]," 0px ",h.W0[4],";background-color:",h.Jv.color.warning[90],";color:",h.Jv.text.white,";border-radius:",h.E0[40],";"+(true?"":0),true?"":0),selectedBadge:(0,n.iv)("margin-left:",h.W0[4],";",y.c.tiny(),";padding:",h.W0[4]," ",h.W0[8],";background-color:",h.Jv.background.disable,";color:",h.Jv.text.title,";border-radius:",h.E0[2],";white-space:nowrap;"+(true?"":0),true?"":0),title:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.primary,";",Pt.i.text.ellipsis(2),";text-wrap:pretty;"+(true?"":0),true?"":0),thumbnail:(0,n.iv)("width:76px;height:48px;border-radius:",h.E0[4],";object-fit:cover;object-position:center;"+(true?"":0),true?"":0),priceWrapper:true?{name:"1al764r",styles:"min-width:200px;text-align:right;[data-button]{display:none;}"}:0,price:(0,n.iv)(y.c.caption(),";display:flex;gap:",h.W0[4],";justify-content:end;"+(true?"":0),true?"":0),startingFrom:(0,n.iv)("color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),discountPrice:(0,n.iv)("text-decoration:line-through;color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),errorMessage:true?{name:"1tw8cl2",styles:"height:100px;display:flex;align-items:center;justify-content:center"}:0};function Av(t){"@babel/helpers - typeof";return Av="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Av(t)}function Pv(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Pv=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Av(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Wv(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Lv(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Wv(i,n,o,a,u,"next",t)}function u(t){Wv(i,n,o,a,u,"throw",t)}a(undefined)}))}}var Iv=(0,$d.X)();function Dv(t){var e=t.title,r=t.closeModal,o=t.actions,i=t.form,u=t.addedCourseIds;var l=at({defaultValues:{courses:[]}});var c=l.watch("courses");var s=(0,$f.V5)();function d(){return f.apply(this,arguments)}function f(){f=Lv(Pv().mark((function t(){var e;return Pv().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:n.next=2;return s.mutateAsync({ID:Iv,course_ids:c.map((function(t){return t.id})),user_action:"add_course"});case 2:e=n.sent;if(e.data){i.setValue("details",e.data);r({action:"CONFIRM"})}case 4:case"end":return n.stop()}}),t)})));return f.apply(this,arguments)}return(0,n.tZ)(Ir.Z,{onClose:function t(){return r({action:"CLOSE"})},title:e,actions:o,maxWidth:720},(0,n.tZ)(Ev,{form:l,addedCourseIds:u}),(0,n.tZ)("div",{css:Mv.footer},(0,n.tZ)(v.Z,{size:"small",variant:"text",onClick:function t(){return r({action:"CLOSE"})}},(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{size:"small",variant:"primary",onClick:d,loading:s.isPending,disabled:c.length===0,"data-cy":"add-selected-courses"},(0,a.__)("Add","tutor"))))}const Tv=Dv;var Mv={footer:(0,n.iv)("box-shadow:0px 1px 0px 0px #e4e5e7 inset;height:56px;display:flex;align-items:center;justify-content:end;gap:",h.W0[16],";padding-inline:",h.W0[16],";"+(true?"":0),true?"":0)};const Jv=r.p+"images/a0821d0a40df52034db17af368a7a7bc-bundle-empty-state.webp";var Nv=function t(e){var r=e.loading;var o=(0,c.Gc)();var i=(0,un.d)(),u=i.showModal;var l=(0,c.Dq)({control:o.control,name:"details.courses",keyName:"_id"}),s=l.fields,f=l.move;var p=o.watch("total_enrolled");return(0,n.tZ)("div",{css:Bv.wrapper,"data-cy":"course-selection"},(0,n.tZ)("label",{css:y.c.caption()},(0,a.__)("Courses","tutor-pro")),(0,n.tZ)(Ff,{wrapperCss:Bv.boxWrapper},(0,n.tZ)(k.Z,{when:!r,fallback:(0,n.tZ)(O.g4,null)},(0,n.tZ)(k.Z,{when:s.length>0,fallback:(0,n.tZ)("div",{css:Bv.emptyState},(0,n.tZ)("img",{src:Jv,alt:(0,a.__)("Empty State","tutor-pro")}),(0,n.tZ)("p",null,(0,a.__)("No Courses Added Yet","tutor-pro")),(0,n.tZ)(Wt.Z,{content:(0,a.__)("You cannot add/remove course(s) from a course bundle with enrolled students as it may disrupt the learning experience.","tutor-pro"),delay:200,disabled:Number(p)<=0},(0,n.tZ)(v.Z,{"data-cy":"add-course",variant:"secondary",isOutlined:true,icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),css:Bv.addCourseButton,disabled:Number(p)>0,onClick:function t(){u({component:Tv,props:{title:(0,a.__)("Select Courses","tutor-pro"),form:o,addedCourseIds:s.map((function(t){return t.id}))}})}},(0,a.__)("Add Courses","tutor-pro"))))},(0,n.tZ)(Gf,{onAddCourse:function t(){u({component:Tv,props:{title:(0,a.__)("Select Courses","tutor-pro"),form:o,addedCourseIds:s.map((function(t){return t.id}))}})},selectedCourses:s,totalEnrolled:p}),(0,n.tZ)(yp,{courses:s,onSort:f}),(0,n.tZ)(gp,null)))))};const Fv=Nv;var Bv={wrapper:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[6],";"+(true?"":0),true?"":0),boxWrapper:(0,n.iv)("padding-inline:0;border:1px solid ",h.Jv.stroke.divider,";"+(true?"":0),true?"":0),emptyState:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[12],";align-items:center;padding-block:",h.W0[32],";img{max-width:60px;width:100%;object-fit:contain;object-position:center;}p{",y.c.body("medium"),";}"+(true?"":0),true?"":0),addCourseButton:(0,n.iv)("outline:1px solid ",h.Jv.stroke.border,";&:hover{outline:1px solid ",h.Jv.stroke.border,";}"+(true?"":0),true?"":0)};var zv=r(7363);function Rv(){Rv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Rv.apply(this,arguments)}function Uv(t,e){return Hv(t)||qv(t,e)||Qv(t,e)||Gv()}function Gv(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Qv(t,e){if(!t)return;if(typeof t==="string")return Yv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yv(t,e)}function Yv(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function qv(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Hv(t){if(Array.isArray(t))return t}var Vv=function t(e){var r=e.field,o=e.fieldState,i=e.label,l=i===void 0?"":i,c=e.baseURL,s=e.onChange;var f=r.value,p=f===void 0?"":f;var h="".concat(c,"/").concat(p);var y=(0,u.useState)(false),g=Uv(y,2),m=g[0],w=g[1];var x=(0,u.useState)(h),_=Uv(x,2),Z=_[0],O=_[1];var S="".concat(c,"/");var k=(0,u.useState)(p),j=Uv(k,2),E=j[0],C=j[1];(0,u.useEffect)((function(){if(c){O("".concat(c,"/").concat(p))}if(p){C(p)}}),[c,p]);return(0,n.tZ)(Dt,{field:r,fieldState:o},(function(t){return(0,n.tZ)("div",{css:$v.aliasWrapper},l&&(0,n.tZ)("label",{css:$v.label},l,": "),(0,n.tZ)("div",{css:$v.linkWrapper},!m?(0,n.tZ)(zv.Fragment,null,(0,n.tZ)("a",{"data-cy":"course-slug",href:Z,target:"_blank",css:$v.link,title:Z,rel:"noreferrer"},Z),(0,n.tZ)("button",{css:$v.iconWrapper,type:"button",onClick:function t(){return w((function(t){return!t}))}},(0,n.tZ)(d.Z,{name:"edit",width:24,height:24,style:$v.editIcon}))):(0,n.tZ)(zv.Fragment,null,(0,n.tZ)("span",{css:$v.prefix,title:S},S),(0,n.tZ)("div",{css:$v.editWrapper},(0,n.tZ)("input",Rv({},t,{className:"tutor-input-field",css:$v.editable,type:"text",value:E,onChange:function t(e){return C(e.target.value)},autoComplete:"off"})),(0,n.tZ)(v.Z,{variant:"secondary",isOutlined:true,size:"small",buttonCss:$v.saveBtn,onClick:function t(){w(false);r.onChange((0,b.k6)(E.replace(c,"")));s===null||s===void 0?void 0:s((0,b.k6)(E.replace(c,"")))}},(0,a.__)("Save","tutor")),(0,n.tZ)(v.Z,{buttonContentCss:$v.cancelButton,variant:"text",size:"small",onClick:function t(){w(false);C(p)}},(0,a.__)("Cancel","tutor"))))))}))};var $v={aliasWrapper:(0,n.iv)("display:flex;min-height:32px;align-items:center;gap:",h.W0[4],";",h.Uo.smallMobile,"{flex-direction:column;gap:",h.W0[4],";align-items:flex-start;}"+(true?"":0),true?"":0),label:(0,n.iv)("flex-shrink:0;",y.c.caption(),";color:",h.Jv.text.subdued,";margin:0px;"+(true?"":0),true?"":0),linkWrapper:(0,n.iv)("display:flex;align-items:center;width:fit-content;font-size:",h.JB[14],";",h.Uo.smallMobile,"{gap:",h.W0[4],";flex-wrap:wrap;}"+(true?"":0),true?"":0),link:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.subdued,";text-decoration:none;",Pt.i.text.ellipsis(1)," max-width:fit-content;word-break:break-all;"+(true?"":0),true?"":0),iconWrapper:(0,n.iv)(Pt.i.resetButton," margin-left:",h.W0[8],";height:24px;width:24px;background-color:",h.Jv.background.white,";border-radius:",h.E0[4],";:focus{",Pt.i.inputFocus,";}"+(true?"":0),true?"":0),editIcon:(0,n.iv)("color:",h.Jv.icon["default"],";:hover{color:",h.Jv.icon.brand,";}"+(true?"":0),true?"":0),prefix:(0,n.iv)(y.c.caption()," color:",h.Jv.text.subdued,";",Pt.i.text.ellipsis(1)," word-break:break-all;max-width:fit-content;"+(true?"":0),true?"":0),editWrapper:(0,n.iv)("margin-left:",h.W0[2],";display:flex;align-items:center;width:fit-content;"+(true?"":0),true?"":0),editable:(0,n.iv)("&.tutor-input-field{",y.c.caption()," background:",h.Jv.background.white,";width:208px;height:32px;border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[8]," ",h.W0[12],";border-radius:",h.E0.input,";margin-right:",h.W0[8],";outline:none;&:focus{border-color:",h.Jv.stroke["default"],";box-shadow:none;outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0),saveBtn:(0,n.iv)("flex-shrink:0;margin-right:",h.W0[8],";"+(true?"":0),true?"":0),cancelButton:(0,n.iv)("color:",h.Jv.text.brand,";"+(true?"":0),true?"":0)};const Kv=Vv;function Xv(t,e){return oh(t)||nh(t,e)||eh(t,e)||th()}function th(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function eh(t,e){if(!t)return;if(typeof t==="string")return rh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rh(t,e)}function rh(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function nh(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function oh(t){if(Array.isArray(t))return t}var ih=!!j.y.tutor_pro_url;if(!window.wp.editor.getDefaultSettings){window.wp.editor.getDefaultSettings=function(){return{}}}function ah(t,e,r,n,o,i,u,l,c,s,d,f,p){var v=f||(n?"bold italic underline | image | ".concat(ih?"codesample":""):"formatselect bold italic underline | bullist numlist | blockquote | alignleft aligncenter alignright | link unlink | wp_more ".concat(ih?" codesample":""," | wp_adv"));var h=p||"strikethrough hr | forecolor pastetext removeformat | charmap | outdent indent | undo redo | wp_help | fullscreen | tutor_button | undoRedoDropdown";v=d?v:v.replaceAll(" | "," ");return{tinymce:{wpautop:true,menubar:false,autoresize_min_height:c||200,autoresize_max_height:s||500,wp_autoresize_on:true,browser_spellcheck:!l,convert_urls:false,end_container_on_empty_block:true,entities:"38,amp,60,lt,62,gt",entity_encoding:"raw",fix_list_elements:true,indent:false,relative_urls:0,remove_script_host:0,plugins:"charmap,colorpicker,hr,lists,image,media,paste,tabfocus,textcolor,fullscreen,wordpress,wpautoresize,wpeditimage,wpemoji,wpgallery,wplink,wpdialogs,wptextpattern,wpview".concat(ih?",codesample":""),skin:"light",skin_url:"".concat(j.y.site_url,"/wp-content/plugins/tutor/assets/lib/tinymce/light"),submit_patch:true,link_context_toolbar:false,theme:"modern",toolbar:!l,toolbar1:v,toolbar2:n?false:h,content_css:"".concat(j.y.site_url,"/wp-includes/css/dashicons.min.css,").concat(j.y.site_url,"/wp-includes/js/tinymce/skins/wordpress/wp-content.css,").concat(j.y.site_url,"/wp-content/plugins/tutor/assets/lib/tinymce/light/content.min.css"),statusbar:!l,branding:false,setup:function o(i){i.on("init",(function(){if(t&&!l){i.getBody().focus()}if(l){i.setMode("readonly");var e=i.contentDocument.querySelector(".mce-content-body");e.style.backgroundColor="transparent";setTimeout((function(){var t=e.scrollHeight;if(t){i.iframeElement.style.height="".concat(t,"px")}}),500)}}));if(!n){i.addButton("tutor_button",{text:(0,a.__)("Tutor ShortCode","tutor"),icon:false,type:"menubutton",menu:[{text:(0,a.__)("Student Registration Form","tutor"),onclick:function t(){i.insertContent("[tutor_student_registration_form]")}},{text:(0,a.__)("Instructor Registration Form","tutor"),onclick:function t(){i.insertContent("[tutor_instructor_registration_form]")}},{text:(0,a.__)("Courses","tutor"),onclick:function t(){i.windowManager.open({title:(0,a.__)("Courses Shortcode","tutor"),body:[{type:"textbox",name:"id",label:(0,a.__)("Course id, separate by (,) comma","tutor"),value:""},{type:"textbox",name:"exclude_ids",label:(0,a.__)("Exclude Course IDS","tutor"),value:""},{type:"textbox",name:"category",label:(0,a.__)("Category IDS","tutor"),value:""},{type:"listbox",name:"orderby",label:(0,a.__)("Order By","tutor"),onselect:function t(){},values:[{text:"ID",value:"ID"},{text:"title",value:"title"},{text:"rand",value:"rand"},{text:"date",value:"date"},{text:"menu_order",value:"menu_order"},{text:"post__in",value:"post__in"}]},{type:"listbox",name:"order",label:(0,a.__)("Order","tutor"),onselect:function t(){},values:[{text:"DESC",value:"DESC"},{text:"ASC",value:"ASC"}]},{type:"textbox",name:"count",label:(0,a.__)("Count","tutor"),value:"6"}],onsubmit:function t(e){i.insertContent('[tutor_course id="'.concat(e.data.id,'" exclude_ids="').concat(e.data.exclude_ids,'" category="').concat(e.data.category,'" orderby="').concat(e.data.orderby,'" order="').concat(e.data.order,'" count="').concat(e.data.count,'"]'))}})}}]})}i.on("change keyup paste",(function(){e(i.getContent())}));i.on("focus",(function(){r(true)}));i.on("blur",(function(){return r(false)}));i.on("FullscreenStateChanged",(function(t){var e=document.getElementById("tutor-course-builder");var r=document.getElementById("tutor-course-bundle-builder-root");var n=e||r;if(n){if(t.state){n.style.position="relative";n.style.zIndex="100000"}else{n.removeAttribute("style")}}u===null||u===void 0?void 0:u(t.state)}))},wp_keep_scroll_position:false,wpeditimage_html5_captions:true},mediaButtons:!o&&!n&&!l,drag_drop_upload:true,quicktags:i||n||l?false:{buttons:["strong","em","block","del","ins","img","ul","ol","li","code","more","close"]}}}var uh=function t(e){var r=e.value,o=r===void 0?"":r,i=e.onChange,a=e.isMinimal,l=e.hideMediaButtons,c=e.hideQuickTags,s=e.autoFocus,d=s===void 0?false:s,f=e.onFullScreenChange,p=e.readonly,v=p===void 0?false:p,h=e.min_height,y=e.max_height,g=e.toolbar1,m=e.toolbar2;var w=(0,u.useRef)(null);var x=(0,u.useRef)((0,b.x0)()),_=x.current;var Z=(0,u.useState)(d),O=Xv(Z,2),k=O[0],j=O[1];var E=function t(e){var r=e.target;i(r.value)};var C=(0,u.useCallback)((function(t){var e=window,r=e.tinymce;if(!r||k){return}var n=window.tinymce.get(_);if(n){if(t!==n.getContent()){n.setContent(t)}}}),[_,k]);(0,u.useEffect)((function(){C(o)}),[o]);(0,u.useEffect)((function(){if(typeof window.wp!=="undefined"&&window.wp.editor){window.wp.editor.remove(_);window.wp.editor.initialize(_,ah(k,i,j,a,l,c,f,v,h,y,S.iM.isAboveMobile,g,m));var t=w.current;t===null||t===void 0?void 0:t.addEventListener("change",E);t===null||t===void 0?void 0:t.addEventListener("input",E);return function(){window.wp.editor.remove(_);t===null||t===void 0?void 0:t.removeEventListener("change",E);t===null||t===void 0?void 0:t.removeEventListener("input",E)}}}),[v]);return(0,n.tZ)("div",{css:ch.wrapper({hideQuickTags:c,isMinimal:a,isFocused:k,isReadOnly:v})},(0,n.tZ)("textarea",{"data-cy":"tutor-tinymce",ref:w,id:_,defaultValue:o}))};const lh=uh;var ch={wrapper:function t(e){var r=e.hideQuickTags,o=e.isMinimal,i=e.isFocused,a=e.isReadOnly;return(0,n.iv)("flex:1;.wp-editor-tools{z-index:auto;}.wp-editor-container{border-top-left-radius:",h.E0[6],";border-bottom-left-radius:",h.E0[6],";border-bottom-right-radius:",h.E0[6],";",i&&!a&&(0,n.iv)(Pt.i.inputFocus,";"+(true?"":0),true?"":0)," :focus-within{",!a&&Pt.i.inputFocus,";}}.wp-switch-editor{height:auto;border:1px solid #dcdcde;border-radius:0px;border-top-left-radius:",h.E0[4],";border-top-right-radius:",h.E0[4],";top:2px;padding:3px 8px 4px;font-size:13px;color:#646970;&:focus,&:active,&:hover{background:#f0f0f1;color:#646970;}}.mce-btn button{&:focus,&:active,&:hover{background:none;color:#50575e;}}.mce-toolbar-grp,.quicktags-toolbar{border-top-left-radius:",h.E0[6],";",(r||o)&&(0,n.iv)("border-top-right-radius:",h.E0[6],";"+(true?"":0),true?"":0),";}.mce-top-part::before{display:none;}.mce-statusbar{border-bottom-left-radius:",h.E0[6],";border-bottom-right-radius:",h.E0[6],";}.mce-tinymce{box-shadow:none;background-color:transparent;}.mce-edit-area{background-color:unset;}",(r||o)&&(0,n.iv)(".mce-tinymce.mce-container{border:",!a?"1px solid ".concat(h.Jv.stroke["default"]):"none",";border-radius:",h.E0[6],";",i&&!a&&(0,n.iv)(Pt.i.inputFocus,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)," textarea{visibility:visible!important;width:100%;resize:none;border:none;outline:none;padding:",h.W0[10],";}"+(true?"":0),true?"":0)}};var sh=r(8789);function dh(t){"@babel/helpers - typeof";return dh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dh(t)}var fh;function ph(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ph=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==dh(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function vh(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function hh(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){vh(i,n,o,a,u,"next",t)}function u(t){vh(i,n,o,a,u,"throw",t)}a(undefined)}))}}function yh(t,e){return xh(t)||wh(t,e)||gh(t,e)||bh()}function bh(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function gh(t,e){if(!t)return;if(typeof t==="string")return mh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mh(t,e)}function mh(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function wh(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function xh(t){if(Array.isArray(t))return t}function _h(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Zh={droip:"droipColorized",elementor:"elementorColorized",gutenberg:"gutenbergColorized",divi:"diviColorized"};var Oh=!!j.y.tutor_pro_url;var Sh=(fh=j.y.settings)===null||fh===void 0?void 0:fh.chatgpt_key_exist;var kh=function t(e){var r=e.editorUsed,o=e.onBackToWPEditorClick,i=e.onCustomEditorButtonClick;var l=(0,un.d)(),c=l.showModal;var s=(0,u.useState)(""),f=yh(s,2),p=f[0],y=f[1];return(0,n.tZ)("div",{css:Ch.editorOverlay},(0,n.tZ)(k.Z,{when:r.name!=="gutenberg"},(0,n.tZ)(v.Z,{variant:"tertiary",size:"small",buttonCss:Ch.editWithButton,icon:(0,n.tZ)(d.Z,{name:"arrowLeft",height:24,width:24}),loading:p==="back_to",onClick:hh(ph().mark((function t(){var e,i;return ph().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:u.next=2;return c({component:sh.Z,props:{title:(0,a.__)("Back to WordPress Editor","tutor"),description:(0,n.tZ)(hn,{type:"warning",icon:"warning"},(0,a.__)("Warning: Switching to the WordPress default editor may cause issues with your current layout, design, and content.","tutor")),confirmButtonText:(0,a.__)("Confirm","tutor"),confirmButtonVariant:"primary"},depthIndex:h.W5.highest});case 2:e=u.sent;i=e.action;if(!(i==="CONFIRM")){u.next=12;break}u.prev=5;y("back_to");u.next=9;return o===null||o===void 0?void 0:o(r.name);case 9:u.prev=9;y("");return u.finish(9);case 12:case"end":return u.stop()}}),t,null,[[5,,9,12]])})))},(0,a.__)("Back to WordPress Editor","tutor"))),(0,n.tZ)(v.Z,{variant:"tertiary",size:"small",buttonCss:Ch.editWithButton,loading:p==="edit_with",icon:Zh[r.name]&&(0,n.tZ)(d.Z,{name:Zh[r.name],height:24,width:24}),onClick:hh(ph().mark((function t(){return ph().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.prev=0;y("edit_with");e.next=4;return i===null||i===void 0?void 0:i(r);case 4:window.location.href=r.link;case 5:e.prev=5;y("");return e.finish(5);case 8:case"end":return e.stop()}}),t,null,[[0,,5,8]])})))},(0,a.sprintf)((0,a.__)("Edit with %s","tutor"),r===null||r===void 0?void 0:r.label)))};var jh=function t(e){var r,o,i,l,c;var s=e.label,f=e.field,p=e.fieldState,v=e.disabled,y=e.readOnly,b=e.loading,g=e.placeholder,m=e.helpText,w=e.onChange,x=e.generateWithAi,_=x===void 0?false:x,Z=e.onClickAiButton,E=e.hasCustomEditorSupport,C=E===void 0?false:E,A=e.isMinimal,P=A===void 0?false:A,W=e.hideMediaButtons,L=W===void 0?false:W,I=e.hideQuickTags,D=I===void 0?false:I,T=e.editors,M=T===void 0?[]:T,J=e.editorUsed,N=J===void 0?{name:"classic",label:"Classic Editor",link:""}:J,F=e.isMagicAi,B=F===void 0?false:F,z=e.autoFocus,R=z===void 0?false:z,U=e.onCustomEditorButtonClick,G=e.onBackToWPEditorClick,Q=e.onFullScreenChange,Y=e.min_height,q=e.max_height,H=e.toolbar1,V=e.toolbar2;var $=(0,un.d)(),K=$.showModal;var X=((r=j.y.settings)===null||r===void 0?void 0:r.hide_admin_bar_for_users)==="off";var tt=(o=j.y.current_user)===null||o===void 0?void 0:(i=o.roles)===null||i===void 0?void 0:i.includes(S.er.ADMINISTRATOR);var et=(l=j.y.current_user)===null||l===void 0?void 0:(c=l.roles)===null||c===void 0?void 0:c.includes(S.er.TUTOR_INSTRUCTOR);var rt=(0,u.useState)(null),nt=yh(rt,2),ot=nt[0],it=nt[1];var at=M.filter((function(t){return tt||et&&X||t.name==="droip"}));var ut=C&&at.length>0;var lt=ut&&N.name!=="classic";var ct=function t(){if(!Oh){K({component:dn,props:{image:no,image2x:ro}})}else if(!Sh){K({component:to,props:{image:no,image2x:ro}})}else{K({component:on,isMagicAi:true,props:{title:(0,a.__)("AI Studio","tutor"),icon:(0,n.tZ)(d.Z,{name:"magicAiColorize",width:24,height:24}),characters:1e3,field:f,fieldState:p,is_html:true}});Z===null||Z===void 0?void 0:Z()}};var st=(0,n.tZ)("div",{css:Ch.editorLabel},(0,n.tZ)("span",{css:Ch.labelWithAi},s,(0,n.tZ)(k.Z,{when:_},(0,n.tZ)("button",{type:"button",css:Ch.aiButton,onClick:ct},(0,n.tZ)(d.Z,{name:"magicAiColorize",width:32,height:32})))),(0,n.tZ)("div",{css:Ch.editorsButtonWrapper},(0,n.tZ)("span",null,(0,a.__)("Edit with","tutor")),(0,n.tZ)("div",{css:Ch.customEditorButtons},(0,n.tZ)(le,{each:at},(function(t){return(0,n.tZ)(Wt.Z,{key:t.name,content:t.label,delay:200},(0,n.tZ)("button",{type:"button",css:Ch.customEditorButton,disabled:ot===t.name,onClick:hh(ph().mark((function e(){return ph().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;it(t.name);r.next=4;return U===null||U===void 0?void 0:U(t);case 4:window.location.href=t.link;case 5:r.prev=5;it(null);return r.finish(5);case 8:case"end":return r.stop()}}),e,null,[[0,,5,8]])})))},(0,n.tZ)(k.Z,{when:ot===t.name},(0,n.tZ)(O.fz,null)),(0,n.tZ)(d.Z,{name:Zh[t.name],height:24,width:24})))})))));return(0,n.tZ)(Dt,{label:ut?st:s,field:f,fieldState:p,disabled:v,readOnly:y,placeholder:g,helpText:m,isMagicAi:B,generateWithAi:!ut&&_,onClickAiButton:ct,replaceEntireLabel:ut},(function(){var t;if(b){return(0,n.tZ)("div",{css:Pt.i.flexCenter()},(0,n.tZ)(O.ZP,{size:20,color:h.Jv.icon["default"]}))}return(0,n.tZ)("div",{css:Ch.wrapper({isOverlayVisible:lt})},(0,n.tZ)(k.Z,{when:lt},(0,n.tZ)(kh,{editorUsed:N,onBackToWPEditorClick:G,onCustomEditorButtonClick:U})),(0,n.tZ)(lh,{value:(t=f.value)!==null&&t!==void 0?t:"",onChange:function t(e){f.onChange(e);if(w){w(e)}},isMinimal:P,hideMediaButtons:L,hideQuickTags:D,autoFocus:R,onFullScreenChange:Q,readonly:y,min_height:Y,max_height:q,toolbar1:H,toolbar2:V}))}))};const Eh=jh;var Ch={wrapper:function t(e){var r=e.isOverlayVisible,o=r===void 0?false:r;return(0,n.iv)("position:relative;",o&&(0,n.iv)("overflow:hidden;border-radius:",h.E0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},editorLabel:true?{name:"1u6jws0",styles:"display:flex;width:100%;align-items:center;justify-content:space-between"}:0,aiButton:(0,n.iv)(Pt.i.resetButton,";",Pt.i.flexCenter(),";width:32px;height:32px;border-radius:",h.E0[4],";:disabled{cursor:not-allowed;}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";}"+(true?"":0),true?"":0),labelWithAi:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";"+(true?"":0),true?"":0),editorsButtonWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[8],";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),customEditorButtons:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";"+(true?"":0),true?"":0),customEditorButton:(0,n.iv)(Pt.i.resetButton," display:flex;align-items:center;justify-content:center;position:relative;border-radius:",h.E0.circle,";&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}"+(true?"":0),true?"":0),editorOverlay:(0,n.iv)("position:absolute;height:100%;width:100%;",Pt.i.flexCenter(),";gap:",h.W0[8],";background-color:",pi()(h.Jv.background.modal,.6),";border-radius:",h.E0[6],";z-index:",h.W5.positive,";backdrop-filter:blur(8px);"+(true?"":0),true?"":0),editWithButton:(0,n.iv)("background:",h.Jv.action.secondary["default"],";color:",h.Jv.text.primary,";box-shadow:inset 0 -1px 0 0 ",pi()("#1112133D",.24),",0 1px 0 0 ",pi()("#1112133D",.8),";"+(true?"":0),true?"":0)};function Ah(t){"@babel/helpers - typeof";return Ah="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ah(t)}function Ph(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ph=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&r.call(b,i)&&(h=b);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Ah(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Wh(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Lh(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Wh(i,n,o,a,u,"next",t)}function u(t){Wh(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Ih(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Dh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ih(Object(r),!0).forEach((function(e){Th(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ih(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Th(t,e,r){e=Mh(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Mh(t){var e=Jh(t,"string");return Ah(e)==="symbol"?e:String(e)}function Jh(t,e){if(Ah(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ah(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Nh(){Nh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Nh.apply(this,arguments)}function Fh(t,e){return Gh(t)||Uh(t,e)||zh(t,e)||Bh()}function Bh(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function zh(t,e){if(!t)return;if(typeof t==="string")return Rh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rh(t,e)}function Rh(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Uh(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Gh(t){if(Array.isArray(t))return t}var Qh=(0,$d.X)();var Yh=false;var qh=function t(){var e;var r=(0,c.Gc)();var l=(0,o.NL)();var s=(0,$f.f)();var d=l.getQueryData(["CourseBundle",Qh]);var f=hv();var p=(0,u.useState)(false),v=Fh(p,2),h=v[0],y=v[1];var g=(0,i.y)({queryKey:["CourseBundle",Qh]});var m=!!j.y.tutor_pro_url;var w=((e=j.y.settings)===null||e===void 0?void 0:e.chatgpt_enable)==="on";var x=r.watch("post_status");var _=r.watch("editor_used");return(0,n.tZ)("div",{css:Vh.wrapper},(0,n.tZ)("div",{css:Vh.mainForm({isWpEditorFullScreen:h})},(0,n.tZ)("div",{css:Vh.fieldsWrapper},(0,n.tZ)("div",{css:Vh.titleAndSlug},(0,n.tZ)(c.Qr,{name:"post_title",control:r.control,render:function t(e){return(0,n.tZ)(Zo,Nh({},e,{label:(0,a.__)("Title","tutor-pro"),placeholder:(0,a.__)("ex. Learn Photoshop CS6 from scratch","tutor-pro"),isClearable:true,generateWithAi:!m||w,loading:!!g&&!e.field.value,onChange:function t(e){if(x==="draft"&&!Yh){r.setValue("post_name",(0,b.k6)(String(e)),{shouldValidate:true,shouldDirty:true})}}}))}}),(0,n.tZ)(c.Qr,{name:"post_name",control:r.control,render:function t(e){return(0,n.tZ)(Kv,Nh({},e,{label:(0,a.__)("Bundle URL","tutor-pro"),baseURL:"".concat(j.y.home_url,"/course-bundle"),onChange:function t(){return Yh=true}}))}})),(0,n.tZ)(c.Qr,{name:"post_content",control:r.control,render:function t(e){return(0,n.tZ)(Eh,Nh({},e,{label:(0,a.__)("Description","tutor-pro"),loading:!!g&&!e.field.value,max_height:280,hasCustomEditorSupport:true,editorUsed:_,editors:d===null||d===void 0?void 0:d.editors,generateWithAi:!m||w,onFullScreenChange:function t(e){y(e)},onCustomEditorButtonClick:function t(){return r.handleSubmit((function(t){var e=(0,$f.ZO)(t);return s.mutateAsync(Dh(Dh({ID:Qh},e),{},{post_status:(0,b.Xl)(r.getValues("post_status"),r.getValues("visibility"))}))}))()},onBackToWPEditorClick:function(){var t=Lh(Ph().mark((function t(e){return Ph().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:return n.abrupt("return",f.mutateAsync({courseId:Qh,builder:e}).then((function(t){r.setValue("editor_used",{name:"classic",label:(0,a.__)("Classic Editor","tutor-pro"),link:""});return t})));case 1:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))}}),(0,n.tZ)(Fv,{loading:!!g&&!r.getValues("details.courses").length}),(0,n.tZ)("div",{css:Vh.additionalFields},(0,n.tZ)(c.Qr,{name:"course_benefits",control:r.control,render:function t(e){return(0,n.tZ)(oe,Nh({},e,{label:(0,a.__)("What Will I Learn?","tutor-pro"),placeholder:(0,a.__)("Define the key takeaways from this course (list one benefit per line)","tutor-pro"),rows:3,enableResize:true}))}})))),(0,n.tZ)(Jf,null))};const Hh=qh;var Vh={wrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 338px;gap:",h.W0[32],";width:100%;",h.Uo.smallTablet,"{grid-template-columns:1fr;gap:0;}"+(true?"":0),true?"":0),mainForm:function t(e){var r=e.isWpEditorFullScreen;return(0,n.iv)("padding-block:",h.W0[32]," ",h.W0[24],";align-self:start;top:",h.J9,"px;position:sticky;",r&&(0,n.iv)("z-index:",h.W5.header+1,";"+(true?"":0),true?"":0)," ",h.Uo.smallTablet,"{padding-top:",h.W0[16],";position:unset;}"+(true?"":0),true?"":0)},fieldsWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[24],";"+(true?"":0),true?"":0),titleAndSlug:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),sidebar:(0,n.iv)("border-left:1px solid ",h.Jv.stroke.divider,";min-height:calc(100vh - ",h.J9,"px);padding-left:",h.W0[32],";padding-block:",h.W0[24],";display:flex;flex-direction:column;gap:",h.W0[16],";"+(true?"":0),true?"":0),priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:flex;align-items:flex-start;gap:",h.W0[16],";"+(true?"":0),true?"":0),statusAndDate:(0,n.iv)(Pt.i.display.flex("column"),";gap:",h.W0[4],";"+(true?"":0),true?"":0),updatedOn:(0,n.iv)(y.c.caption(),";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),additionalFields:(0,n.iv)("padding:",h.W0[12]," ",h.W0[20],";background-color:",h.Jv.background.white,";border:1px solid ",h.Jv.stroke.divider,";border-radius:",h.E0.card,";"+(true?"":0),true?"":0)}}}]);