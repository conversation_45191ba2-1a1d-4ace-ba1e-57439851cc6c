(()=>{var t={};function o(t){"@babel/helpers - typeof";return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}window.jQuery(document).ready((function(t){var e=function t(o,e){o.prop("disabled",!e);!e?o.addClass("is-loading"):o.removeClass("is-loading")};t("#tutor-gc-student-password-set button").click((function(){var o=t(this);var a=o.parent().parent();var r=a.find('[name="password-1"]').val();var n=a.find('[name="password-2"]').val();var s=a.find('[name="token"]').val();if(!r||!n||r!==n){alert("Invalid Password");return}e(o,false);t.ajax({url:window._tutorobject.ajaxurl,data:{action:"tutor_gc_student_set_password",token:s,password:r},type:"POST",success:function t(o){window.location.replace(window._tutorobject.tutor_frontend_dashboard_url)},error:function t(){e(o,true);alert("Request Failed.")}})}));t("[tutor-gc-stream-loader]").click((function(e){e.preventDefault();var a=t(this);var r=a.data("next_token");var n=a.data("course_id");t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:{next_token:r,action:"tutor_gc_load_more_stream",course_id:n},beforeSend:function t(){a.addClass("is-loading")},success:function e(r){try{r=JSON.parse(r)}catch(t){}a.removeClass("is-loading");if(o(r)=="object"){if(!r.html||/\S+/.test(r.html)==false){a.remove();return}a.data("next_token",r.next_token);t("[tutor-gc-streams]").append(r.html);!r.next_token?a.remove():0}},error:function t(){a.removeClass("is-loading")}})}))}))})();