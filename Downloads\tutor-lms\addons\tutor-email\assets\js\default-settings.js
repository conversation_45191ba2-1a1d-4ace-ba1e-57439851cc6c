(()=>{"use strict";var e={449:(e,t,r)=>{r.d(t,{Kd:()=>f});var o=wp.i18n,a=o.__,n=o._x,l=o._n,i=o._nx;function c(e){var t=e.split(",");var r=/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;for(var o=0;o<t.length;o++){var a=t[o].trim();if(!r.test(a)){return false}}return true}function u(e,t){return new Promise((function(r,o){var n=new FormData(e);n.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);var l=n.get("action");if(l==="save_email_template"){n.append(t.name,t.value)}if(l==="save_email_settings"){var i=n.get("email_from_address");var u=n.get("email_from_name");if(!i||!u){e.reportValidity();return o(a("Sender Email Address and Name are required!","tutor-pro"))}if(!c(i)){e.reportValidity();return o(a("Please provide a valid Email Address","tutor-pro"))}}else{var s=n.get("email-subject")||n.get("email_subject");var d=n.get("email-heading")||n.get("email_heading");if(!s||!d){e.reportValidity();return o(a("Email subject and heading are required!","tutor-pro"))}}var m=new XMLHttpRequest;m.open("POST",_tutorobject.ajaxurl,true);m.send(n);m.onreadystatechange=function(e){if(m.readyState===4){var t=JSON.parse(m.response);if(t.success){r(t.message)}else{o(t.message)}}}}))}function s(e,t){if(e){if(e.src){e.src=t}else{e.innerHTML=t}}}function d(e){var t=document.createElement("iframe");t.width="100%";t.height="800px";t.src="".concat(_tutorobject.home_url,"?page=tutor-email-preview&template=").concat(e);return t}function m(e){var t=e.querySelector(".tutor-email-body");var r=e.querySelector(".tutor-email-footer-text");var o=e.querySelector(".tutor-email-footer-content");var a=e.querySelectorAll("a");t.style.paddingTop="0px";t.style.paddingLeft="0px";t.style.paddingRight="0px";t.style.backgroundColor="transparent";o.style.backgroundColor="transparent";a.forEach((function(e){e.classList.add("preview-only");e.addEventListener("click",(function(e){return e.preventDefault()}))}));if(r){var n=new URLSearchParams(window.location.href);var l=n.get("edit");if(l==="settings"){r.innerText="Example of a no-reply or instructional footnote"}}var i=t.querySelectorAll(".user-avatar");i.forEach((function(e){e.src="http://2.gravatar.com/avatar/bc78e9eeca4abe2043bb671018d504ef?s=96&d=mm&r=g"}))}function p(e){if(!e||e.length===0){return""}return'\t\n        <ul class="tutor-dropdown" style="width: 100%; margin: 0; max-height: 200px; overflow: scroll;">\n            '.concat(e.map((function(e){return'\n                    <li>\n                        <a class="tutor-dropdown-item tutor-d-flex tutor-justify-between" href="#" data-state-text="'.concat(e.placeholder,'">\n                            <span class="tutor-color-muted">').concat(e.placeholder,"</span>\n                            <span>").concat(e.label,"</span>\n                        </a>\n                    </li>\n                ")})).join(""),"\n        </ul>\n    ")}function _(e,t){var r=new FormData;r.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);r.append("action","tutor_manual_email_receiver_count_help_text");r.append("receiver_type",e);if(t){t.forEach((function(e){r.append("course_ids[]",e)}))}return jQuery.ajax({url:_tutorobject.ajaxurl,method:"POST",data:r,processData:false,contentType:false})}function f(e,t,r,o){if(e){e.style.setProperty(t,r,o)}}function v(e){var t=e.selection.getRng();var r=t.getClientRects()[0];if(r){var o={offsetTop:r.top+124,offsetLeft:r.left};return o}return{}}function b(e){var t={subject:"email-subject",heading:"email-heading",message:"email-additional-message",before_button:"email-before-button",footer_text:"email-footer-text"};Object.keys(e).map((function(r){if(r==="message"){var o=tinymce.get(t[r]);if(o){o.setContent(JSON.parse(e[r]));tinymce.triggerSave()}}else{var a=document.querySelector("[name=".concat(t[r],"]"));if(a){a.value=e[r];a.dispatchEvent(new Event("input",{bubbles:true}))}}}))}}};var t={};function r(o){var a=t[o];if(a!==undefined){return a.exports}var n=t[o]={exports:{}};e[o](n,n.exports,r);return n.exports}(()=>{r.d=(e,t)=>{for(var o in t){if(r.o(t,o)&&!r.o(e,o)){Object.defineProperty(e,o,{enumerable:true,get:t[o]})}}}})();(()=>{r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})();var o={};(()=>{var e=r(449);document.addEventListener("DOMContentLoaded",(function(){var t=document.querySelector("#tutor-email-settings-form")||document.querySelector("#tutor-mailer-form");if(t){var r;var o=t.querySelectorAll("input");o.forEach((function(t){t.addEventListener("input",(function(t){var r=t.target,o=r.name,a=r.value;var n=document.querySelector(".template-preview iframe");if(n){var l=n.contentWindow.document;var i=l.querySelector(".tutor-email-logo img");var c=l.querySelector(".tutor-email-header");var u=l.querySelector(".tutor-email-content");var s=l.querySelector(".tutor-email-footer");var d=l.querySelector(".tutor-email-footer-text");var m=l.querySelector(".tutor-email-heading");var p=l.querySelector(".tutor-email-buttons");var _=l.querySelector(".tutor-email-button");var f=l.querySelector(".tutor-email-button-bordered");switch(o){case"email_logo_position":(0,e.Kd)(i.parentElement,"text-align",a);break;case"email_logo_height":(0,e.Kd)(i,"height",a+"px");break;case"email_logo_alt_text":i.setAttribute("alt",a);break;case"email_template_button_position":(0,e.Kd)(p,"text-align",a);break;case"email_template_colors[header_background_color]":(0,e.Kd)(c,"background-color",a);break;case"email_template_colors[header_divider_color]":(0,e.Kd)(c,"border-color",a);break;case"email_template_colors[body_background_color]":(0,e.Kd)(u,"background-color",a);(0,e.Kd)(s,"background-color",a);break;case"email_template_colors[email_title_color]":(0,e.Kd)(m,"color",a);break;case"email_template_colors[email_text_color]":(0,e.Kd)(u,"color",a);break;case"email_template_colors[email_short_code_color]":l.querySelectorAll("strong").forEach((function(t){(0,e.Kd)(t,"color",a)}));break;case"email_template_colors[footnote_color]":(0,e.Kd)(d,"color",a);break;case"email_template_colors[primary_button_color]":(0,e.Kd)(_,"--primary-button-color",a);break;case"email_template_colors[primary_button_hover_color]":(0,e.Kd)(_,"--primary-button-hover-color",a);break;case"email_template_colors[primary_button_text_color]":(0,e.Kd)(_,"color",a,"important");break;case"email_template_colors[secondary_button_color]":(0,e.Kd)(f,"--secondary-button-color",a);break;case"email_template_colors[secondary_button_hover_color]":(0,e.Kd)(f,"--secondary-button-hover-color",a);break;case"email_template_colors[secondary_button_text_color]":(0,e.Kd)(f,"color",a,"important");break;default:break}}}))}));(r=document.querySelector(".tutor-thumbnail-uploader"))===null||r===void 0?void 0:r.addEventListener("tutor_settings_media_selected",(function(e){var t=document.querySelector(".template-preview iframe");if(t){var r=t.contentWindow.document;var o=r.querySelector(".tutor-email-logo img");o.setAttribute("src",e.detail.attachment.url)}}));var a=document.querySelector(".tutor-email-colors");var n=document.querySelector(".tutor-color-restore-button");n.addEventListener("click",(function(e){var t=JSON.parse(e.target.dataset.defaults);var r=a.querySelectorAll("input[type=color]");r.forEach((function(e){e.value=t[e.dataset.picker];e.dispatchEvent(new Event("input"))}))}));jQuery(document).on("input","input.email-logo-url",(function(){var e=jQuery(this),t=e.val(),r=e.parent().parent().find(".thumbnail-preview img").eq(0);if(r){r.attr("src",t)}}))}jQuery(".tutor-card-collapse-button").click((function(e){if(jQuery(this).find("i").hasClass("tutor-icon-angle-up")){jQuery(this).find("i").removeClass("tutor-icon-angle-up").addClass("tutor-icon-angle-down")}else{jQuery(this).find("i").removeClass("tutor-icon-angle-down").addClass("tutor-icon-angle-up")}jQuery(e.target.closest(".tutor-card")).find(".tutor-card-body").slideToggle()}))}))})()})();