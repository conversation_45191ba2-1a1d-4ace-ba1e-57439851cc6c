(()=>{"use strict";var e={};(()=>{e.d=(t,r)=>{for(var a in r){if(e.o(r,a)&&!e.o(t,a)){Object.defineProperty(t,a,{enumerable:true,get:r[a]})}}}})();(()=>{e.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})();var t={};var r=wp.i18n,a=r.__,n=r._x,o=r._n,i=r._nx;function c(e){var t=e.split(",");var r=/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;for(var a=0;a<t.length;a++){var n=t[a].trim();if(!r.test(n)){return false}}return true}function l(e,t){return new Promise((function(r,n){var o=new FormData(e);o.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);var i=o.get("action");if(i==="save_email_template"){o.append(t.name,t.value)}if(i==="save_email_settings"){var l=o.get("email_from_address");var u=o.get("email_from_name");if(!l||!u){e.reportValidity();return n(a("Sender Email Address and Name are required!","tutor-pro"))}if(!c(l)){e.reportValidity();return n(a("Please provide a valid Email Address","tutor-pro"))}}else{var s=o.get("email-subject")||o.get("email_subject");var f=o.get("email-heading")||o.get("email_heading");if(!s||!f){e.reportValidity();return n(a("Email subject and heading are required!","tutor-pro"))}}var p=new XMLHttpRequest;p.open("POST",_tutorobject.ajaxurl,true);p.send(o);p.onreadystatechange=function(e){if(p.readyState===4){var t=JSON.parse(p.response);if(t.success){r(t.message)}else{n(t.message)}}}}))}function u(e,t){if(e){if(e.src){e.src=t}else{e.innerHTML=t}}}function s(e){var t=document.createElement("iframe");t.width="100%";t.height="800px";t.src="".concat(_tutorobject.home_url,"?page=tutor-email-preview&template=").concat(e);return t}function f(e){var t=e.querySelector(".tutor-email-body");var r=e.querySelector(".tutor-email-footer-text");var a=e.querySelector(".tutor-email-footer-content");var n=e.querySelectorAll("a");t.style.paddingTop="0px";t.style.paddingLeft="0px";t.style.paddingRight="0px";t.style.backgroundColor="transparent";a.style.backgroundColor="transparent";n.forEach((function(e){e.classList.add("preview-only");e.addEventListener("click",(function(e){return e.preventDefault()}))}));if(r){var o=new URLSearchParams(window.location.href);var i=o.get("edit");if(i==="settings"){r.innerText="Example of a no-reply or instructional footnote"}}var c=t.querySelectorAll(".user-avatar");c.forEach((function(e){e.src="http://2.gravatar.com/avatar/bc78e9eeca4abe2043bb671018d504ef?s=96&d=mm&r=g"}))}function p(e){if(!e||e.length===0){return""}return'\t\n        <ul class="tutor-dropdown" style="width: 100%; margin: 0; max-height: 200px; overflow: scroll;">\n            '.concat(e.map((function(e){return'\n                    <li>\n                        <a class="tutor-dropdown-item tutor-d-flex tutor-justify-between" href="#" data-state-text="'.concat(e.placeholder,'">\n                            <span class="tutor-color-muted">').concat(e.placeholder,"</span>\n                            <span>").concat(e.label,"</span>\n                        </a>\n                    </li>\n                ")})).join(""),"\n        </ul>\n    ")}function d(e,t){var r=new FormData;r.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);r.append("action","tutor_manual_email_receiver_count_help_text");r.append("receiver_type",e);if(t){t.forEach((function(e){r.append("course_ids[]",e)}))}return jQuery.ajax({url:_tutorobject.ajaxurl,method:"POST",data:r,processData:false,contentType:false})}function m(e,t,r,a){if(e){e.style.setProperty(t,r,a)}}function v(e){var t=e.selection.getRng();var r=t.getClientRects()[0];if(r){var a={offsetTop:r.top+124,offsetLeft:r.left};return a}return{}}function g(e){var t={subject:"email-subject",heading:"email-heading",message:"email-additional-message",before_button:"email-before-button",footer_text:"email-footer-text"};Object.keys(e).map((function(r){if(r==="message"){var a=tinymce.get(t[r]);if(a){a.setContent(JSON.parse(e[r]));tinymce.triggerSave()}}else{var n=document.querySelector("[name=".concat(t[r],"]"));if(n){n.value=e[r];n.dispatchEvent(new Event("input",{bubbles:true}))}}}))}})();