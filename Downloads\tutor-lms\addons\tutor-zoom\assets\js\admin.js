(()=>{"use strict";var o={};var t=function o(t,e){var n=wp.i18n.__;var r=t||{},a=r.data,i=a===void 0?{}:a;var s=i.message,c=s===void 0?e||n("Something Went Wrong!","tutor-pro"):s;return c};window.jQuery(document).ready((function(o){var e=wp.i18n.__;o(".tutor-zoom-accordion-panel").on("click",(function(t){t.preventDefault();o(this).find("i").toggleClass("tutor-icon-angle-down tutor-icon-angle-up");o(this).parent().find(".tutor-zoom-accordion-body").slideToggle()}));o("#tutor-zoom-settings").on("change",'input[type="checkbox"], input[type="radio"]',(function(t){o(this).closest("form").submit()}));o("#tutor-zoom-settings").submit((function(n){n.preventDefault();var r=o(this);var a=r.serializeObject();o.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:a,beforeSend:function o(){r.find("#save-changes").attr("disabled","disabled").addClass("is-loading")},success:function o(n){var r;var a=n.success?e("Success","tutor-pro"):e("Error","tutor-pro");tutor_toast(a,t(n),n.success?"success":"error");if(n.success&&(r=n.data)!==null&&r!==void 0&&r.reload_page){window.location.reload()}},complete:function o(){r.find("#save-changes").removeAttr("disabled").removeClass("is-loading")}})}));function n(o,t){var e=new URL(window.location.href);var n=e.searchParams;n.set(o,t);e.search=n.toString();n.set("paged",1);e.search=n.toString();return e.toString()}o("#tutor-zoom-search-filter-form").on("keypress",'[name="search"]',(function(t){console.log(t,"on submit");if((t.keyCode||t.which)=="13"){var e=o(this).val();window.location=n("search",e)}})).find(".tutor-zoom-course, .tutor-zoom-date").on("change",(function(t){window.location=n(o(this).attr("name"),o(this).val())}))}))})();