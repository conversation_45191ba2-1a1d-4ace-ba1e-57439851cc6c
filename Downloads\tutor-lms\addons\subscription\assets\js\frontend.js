(()=>{var t={};function e(t){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function e(){return t};var t={},o=Object.prototype,n=o.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},u="function"==typeof Symbol?Symbol:{},a=u.iterator||"@@iterator",c=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,o){return e[r]=o}}function d(t,e,r,o){var n=e&&e.prototype instanceof v?e:v,u=Object.create(n.prototype),a=new j(o||[]);return i(u,"_invoke",{value:S(t,r,a)}),u}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=d;var f={};function v(){}function h(){}function m(){}var y={};l(y,a,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(k([])));w&&w!==o&&n.call(w,a)&&(y=w);var g=m.prototype=v.prototype=Object.create(y);function L(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,r){function o(i,u,a,c){var s=p(t[i],t,u);if("throw"!==s.type){var l=s.arg,d=l.value;return d&&"object"==e(d)&&n.call(d,"__await")?r.resolve(d.__await).then((function(t){o("next",t,a,c)}),(function(t){o("throw",t,a,c)})):r.resolve(d).then((function(t){l.value=t,a(l)}),(function(t){return o("throw",t,a,c)}))}c(s.arg)}var u;i(this,"_invoke",{value:function t(e,n){function i(){return new r((function(t,r){o(e,n,t,r)}))}return u=u?u.then(i,i):i()}})}function S(t,e,r){var o="suspendedStart";return function(n,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===n)throw i;return O()}for(r.method=n,r.arg=i;;){var u=r.delegate;if(u){var a=x(u,r);if(a){if(a===f)continue;return a}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===o)throw o="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o="executing";var c=p(t,e,r);if("normal"===c.type){if(o=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o="completed",r.method="throw",r.arg=c.arg)}}}function x(t,e){var r=e.method,o=t.iterator[r];if(undefined===o)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,x(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var n=p(o,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,f):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function k(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:O}}function O(){return{value:undefined,done:!0}}return h.prototype=m,i(g,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:h,configurable:!0}),h.displayName=l(m,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,s,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},L(_.prototype),l(_.prototype,c,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,o,n,i){void 0===i&&(i=Promise);var u=new _(d(e,r,o,n),i);return t.isGeneratorFunction(r)?u:u.next().then((function(t){return t.done?t.value:u.next()}))},L(g),l(g,s,"Generator"),l(g,a,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=k,j.prototype={constructor:j,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var r=this;function o(t,o){return a.type="throw",a.arg=e,r.next=t,o&&(r.method="next",r.arg=undefined),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],a=u.completion;if("root"===u.tryLoc)return o("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),s=n.call(u,"finallyLoc");if(c&&s){if(this.prev<u.catchLoc)return o(u.catchLoc,!0);if(this.prev<u.finallyLoc)return o(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return o(u.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return o(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var u=i;break}}u&&("break"===e||"continue"===e)&&u.tryLoc<=r&&r<=u.finallyLoc&&(u=null);var a=u?u.completion:{};return a.type=e,a.arg=r,u?(this.method="next",this.next=u.finallyLoc,f):this.complete(a)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),f},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),E(o),f}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc===e){var n=o.completion;if("throw"===n.type){var i=n.arg;E(o)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,o){return this.delegate={iterator:k(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=undefined),f}},t}function o(t,e,r,o,n,i,u){try{var a=t[i](u);var c=a.value}catch(t){r(t);return}if(a.done){e(c)}else{Promise.resolve(c).then(o,n)}}function n(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var u=t.apply(e,r);function a(t){o(u,n,i,a,c,"next",t)}function c(t){o(u,n,i,a,c,"throw",t)}a(undefined)}))}}var i=wp.i18n.__;var u=document.querySelector("#tutor-subscription-cancel-plan-button");var a=document.querySelector("#tutor-subscription-cancel-plan-modal");var c=document.querySelector("#tutor-subscription-cancel-plan-submit");if(u&&a){u.addEventListener("click",(function(t){a.classList.add("tutor-is-active")}));c.addEventListener("click",function(){var t=n(r().mark((function t(e){var o,n,u;return r().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:c.classList.add("is-loading");o=new FormData;o.append("_tutor_nonce",_tutorobject._tutor_nonce);o.append("action","tutor_subscription_status_update");o.append("subscription_id",e.target.dataset.subscriptionId);o.append("status","cancelled");r.prev=6;r.next=9;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:o});case 9:n=r.sent;r.next=12;return n.json();case 12:u=r.sent;if(u.status_code===200){window.location.reload();tutor_toast(i("Success","tutor-pro"),i("Subscription cancelled successfully!","tutor-pro"),"success")}else{tutor_toast(i("Failed","tutor-pro"),u.message,"error")}r.next=19;break;case 16:r.prev=16;r.t0=r["catch"](6);tutor_toast(i("Failed","tutor-pro"),i("Something went wrong!","tutor-pro"),"error");case 19:r.prev=19;c.classList.remove("is-loading");return r.finish(19);case 22:case"end":return r.stop()}}),t,null,[[6,16,19,22]])})));return function(e){return t.apply(this,arguments)}}())}var s=document.querySelector("#auto_renew");if(s){s.addEventListener("change",function(){var t=n(r().mark((function t(e){var o,n,u,a,c,s;return r().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:o=window._tutorobject.ajaxurl;n=e.target.checked?1:0;u=e.target.dataset.subscriptionId;a=new FormData;a.append("_tutor_nonce",_tutorobject._tutor_nonce);a.append("action","tutor_subscription_auto_renew");a.append("subscription_id",u);a.append("auto_renew",n);r.prev=8;r.next=11;return fetch(o,{method:"POST",body:a});case 11:c=r.sent;r.next=14;return c.json();case 14:s=r.sent;if(s.status_code===200){tutor_toast(i("Success","tutor-pro"),s.message,"success")}else{tutor_toast(i("Failed","tutor-pro"),s.message,"error")}r.next=21;break;case 18:r.prev=18;r.t0=r["catch"](8);tutor_toast(i("Failed","tutor-pro"),i("Something went wrong!","tutor-pro"),"error");case 21:case"end":return r.stop()}}),t,null,[[8,18]])})));return function(e){return t.apply(this,arguments)}}())}var l=document.querySelector("#tutor-subscription-early-renewal-submit");if(l){l.addEventListener("click",function(){var t=n(r().mark((function t(e){var o,n,u;return r().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:l.classList.add("is-loading");l.disabled=true;o=new FormData;o.append("_tutor_nonce",_tutorobject._tutor_nonce);o.append("action","tutor_subscription_early_renew");o.append("subscription_id",e.target.dataset.subscriptionId);r.prev=6;r.next=9;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:o});case 9:n=r.sent;r.next=12;return n.json();case 12:u=r.sent;if(u.status_code===200){window.location.reload();tutor_toast(i("Success","tutor-pro"),u.message,"success")}else{tutor_toast(i("Failed","tutor-pro"),u.message,"error")}r.next=19;break;case 16:r.prev=16;r.t0=r["catch"](6);tutor_toast(i("Failed","tutor-pro"),i("Something went wrong!","tutor-pro"),"error");case 19:r.prev=19;l.classList.remove("is-loading");l.disabled=false;return r.finish(19);case 23:case"end":return r.stop()}}),t,null,[[6,16,19,23]])})));return function(e){return t.apply(this,arguments)}}())}var d=document.querySelectorAll(".tutor-course-subscription-options input[name=selling_option]");d.forEach((function(t){t.addEventListener("change",(function(t){var e=t.target.value;if(e==="subscription"){var r,o,n,i,u,a,c,s;(r=document.querySelector(".tutor-subscription-plan-wrapper"))===null||r===void 0?void 0:r.classList.remove("tutor-d-none");(o=document.querySelector(".tutor-subscription-buy-now"))===null||o===void 0?void 0:o.classList.remove("tutor-d-none","tutor-native-add-to-cart");(n=document.querySelector(".tutor-subscription-add-to-cart-wrap"))===null||n===void 0?void 0:n.classList.add("tutor-d-none");(i=document.querySelector("#tutor-membership-view-pricing"))===null||i===void 0?void 0:i.classList.add("tutor-d-none");(u=document.querySelector(".tutor-plan-feature-list"))===null||u===void 0?void 0:u.classList.remove("tutor-d-none");(a=document.querySelector("#tutor-subscription-start-from"))===null||a===void 0?void 0:a.classList.add("tutor-d-none");(c=document.querySelector(".tutor-course-certificate-meta"))===null||c===void 0?void 0:c.classList.add("tutor-d-none");(s=document.querySelector(".tutor-course-enrollment-meta"))===null||s===void 0?void 0:s.classList.add("tutor-d-none")}else if(e==="membership"){var l,d,p,f,v,h,m,y;(l=document.querySelector(".tutor-subscription-plan-wrapper"))===null||l===void 0?void 0:l.classList.add("tutor-d-none");(d=document.querySelector(".tutor-subscription-buy-now"))===null||d===void 0?void 0:d.classList.add("tutor-d-none");(p=document.querySelector("#tutor-subscription-start-from"))===null||p===void 0?void 0:p.classList.remove("tutor-d-none");(f=document.querySelector(".tutor-subscription-add-to-cart-wrap"))===null||f===void 0?void 0:f.classList.add("tutor-d-none");(v=document.querySelector("#tutor-membership-view-pricing"))===null||v===void 0?void 0:v.classList.remove("tutor-d-none");(h=document.querySelector(".tutor-plan-feature-list"))===null||h===void 0?void 0:h.classList.add("tutor-d-none");(m=document.querySelector(".tutor-course-certificate-meta"))===null||m===void 0?void 0:m.classList.add("tutor-d-none");(y=document.querySelector(".tutor-course-enrollment-meta"))===null||y===void 0?void 0:y.classList.add("tutor-d-none")}else{var b,w,g,L,_,S,x,q;(b=document.querySelector(".tutor-subscription-plan-wrapper"))===null||b===void 0?void 0:b.classList.add("tutor-d-none");(w=document.querySelector(".tutor-subscription-buy-now"))===null||w===void 0?void 0:w.classList.add("tutor-d-none");(g=document.querySelector("#tutor-subscription-start-from"))===null||g===void 0?void 0:g.classList.remove("tutor-d-none");(L=document.querySelector(".tutor-subscription-add-to-cart-wrap"))===null||L===void 0?void 0:L.classList.remove("tutor-d-none");(_=document.querySelector("#tutor-membership-view-pricing"))===null||_===void 0?void 0:_.classList.add("tutor-d-none");(S=document.querySelector(".tutor-plan-feature-list"))===null||S===void 0?void 0:S.classList.add("tutor-d-none");(x=document.querySelector(".tutor-course-certificate-meta"))===null||x===void 0?void 0:x.classList.remove("tutor-d-none");(q=document.querySelector(".tutor-course-enrollment-meta"))===null||q===void 0?void 0:q.classList.remove("tutor-d-none")}}))}));var p=document.querySelectorAll(".tutor-course-subscription-plan input[name=plan_id]");p.forEach((function(t){t.addEventListener("change",(function(e){var r;var o=t.closest(".tutor-course-subscription-plan");var n=o.dataset.checkoutLink;(r=document.querySelector(".tutor-subscription-buy-now"))===null||r===void 0?void 0:r.setAttribute("href",n);var i=o.dataset.features;if(i){var u=JSON.parse(i);var a=u.map((function(t){return'\n                <div class="tutor-plan-feature-item">\n                    <i class="tutor-icon-mark-light"></i>\n                    '.concat(t,"\n                </div>\n            ")})).join("");document.querySelector(".tutor-plan-feature-list").innerHTML=a}}))}));if(p.length>0){p[0].checked=true;p[0].dispatchEvent(new Event("change"))}if(document.querySelector(".tutor-subscription-plans.subscriptions-only, .tutor-subscription-plans.membership-only")){var f,v;(f=document.querySelector(".tutor-course-enrollment-meta"))===null||f===void 0?void 0:f.classList.add("tutor-d-none");(v=document.querySelector(".tutor-course-certificate-meta"))===null||v===void 0?void 0:v.classList.add("tutor-d-none")}})();