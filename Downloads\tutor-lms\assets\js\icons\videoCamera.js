"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[4043],{71398:(l,e,a)=>{a.r(e);a.d(e,{default:()=>r});const r={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M39.776 16.79 31.5 20.927v6.146l8.276 4.137a.502.502 0 0 0 .724-.447V17.237a.5.5 0 0 0-.724-.447Zm-1.34-2.684a3.5 3.5 0 0 1 5.064 3.13v13.528a3.503 3.503 0 0 1-3.343 3.495 3.501 3.501 0 0 1-1.721-.365l-9.107-4.552A1.5 1.5 0 0 1 28.5 28v-8a1.5 1.5 0 0 1 .83-1.342l9.105-4.552Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M10 13.5A2.5 2.5 0 0 0 7.5 16v16a2.5 2.5 0 0 0 2.5 2.5h16a2.5 2.5 0 0 0 2.5-2.5V16a2.5 2.5 0 0 0-2.5-2.5H10ZM4.5 16a5.5 5.5 0 0 1 5.5-5.5h16a5.5 5.5 0 0 1 5.5 5.5v16a5.5 5.5 0 0 1-5.5 5.5H10A5.5 5.5 0 0 1 4.5 32V16Z" fill="currentColor"/>',viewBox:"0 0 48 48"}}}]);