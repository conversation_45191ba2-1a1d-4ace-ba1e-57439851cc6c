(()=>{var t={};function e(t){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}window.jQuery(document).ready((function(t){var a=function t(e,a){e.prop("disabled",!a);!a?e.addClass("is-loading"):e.removeClass("is-loading")};var o=function e(){this.upload_area=t("#tutor_gc_dashboard .tutor-upload-area");this.input_field=this.upload_area.find('[type="file"]');this.load_button=t("#tutor_gc_credential_upload>button");this.uploaded_file=null;this.is_file_valid=null;this.init=function(){this.on_click();this.on_change();this.on_drop();this.on_save()};this.load_file=function(t){if(t&&t[0]){var e=t[0];this.is_file_valid=this.is_valid_file(e);this.load_button.prop("disabled",!this.is_file_valid);if(this.is_file_valid){this.uploaded_file=e;this.upload_area.find("span.file_name").remove();this.upload_area.append('<span class="file_name">'+this.uploaded_file.name+"</span>")}else{alert("Invalid File.")}}};this.is_valid_file=function(t){var e=t.type||"";e=e.toLowerCase();var a=e=="application/json";return a};this.on_click=function(){var t=this;this.upload_area.find("button").click((function(e){e.preventDefault();t.input_field.trigger("click")}))};this.on_change=function(){var e=this;this.input_field.change((function(a){e.load_file(a.currentTarget.files);t(this).val("")}))};this.on_drop=function(){var e=this;this.upload_area.on("drag dragstart dragend dragover dragenter dragleave drop",(function(t){t.preventDefault();t.stopPropagation()})).on("dragover dragenter",(function(){t(this).addClass("dragover")})).on("dragleave dragend drop",(function(){t(this).removeClass("dragover")})).on("drop",(function(t){e.load_file(t.originalEvent.dataTransfer.files)}))};this.on_save=function(){var e=this;this.load_button.click((function(){if(!e.is_file_valid){return}var o=t(this);var r=new FormData;r.append("credential",e.uploaded_file,e.uploaded_file.name);r.append("action","tutor_gc_credential_save");var i=tutor_get_nonce_data(true);r.append(i.key,i.value);a(o,false);t.ajax({url:window._tutorobject.ajaxurl,type:"POST",processData:false,contentType:false,data:r,success:function t(){window.location.reload()},error:function t(){a(o,true);alert("Request Failed.")}})}))}};(new o).init();var r=t("#tutor_gc_dashboard .google-classroom-class-list");var i="no";var n=[];var s=function e(a){var o=t("#tutor_gc_bulk_action_button").data("process_running")==true;!a?n=[]:0;t("#tutor_gc_bulk_action_button").text(a?"Abort":"Apply").data("process_running",a);return o};var l=function e(a){var o=window.wp.i18n.__;var r;var n={title:o("Do you want to import students from this Classroom?","tutor-pro"),description:o("This is not recommended for paid courses as importing will skip the payment procedure.","tutor-pro"),buttons:{cancel:{title:o("Cancel","tutor-pro"),id:"cancel",class:"tutor-btn tutor-btn-outline-primary",callback:function e(){r.remove();t("body").removeClass("tutor-modal-open")}},reset:{title:o("Yes, Import Student","tutor-pro"),id:"reset",class:"tutor-btn tutor-btn-primary tutor-ml-20",callback:function e(){r.remove();t("body").removeClass("tutor-modal-open");i="import";a()}}}};r=new window.tutor_popup(t,"import-icon").popup(n)};var c=function e(a){var o=window.wp.i18n.__;var r;var i={title:o("Do you want to remove this course from the system?","tutor-pro"),description:o("This will not delete it from Google Classroom, it will only remove the connection.","tutor-pro"),buttons:{cancel:{title:o("Cancel","tutor-pro"),id:"cancel",class:"tutor-btn tutor-btn-outline-primary",callback:function e(){r.remove();t("body").removeClass("tutor-modal-open")}},reset:{title:o("Yes, Delete Course","tutor-pro"),id:"reset",class:"tutor-btn tutor-btn-primary tutor-ml-20",callback:function e(){r.remove();t("body").removeClass("tutor-modal-open");a()}}}};r=new window.tutor_popup(t,"icon-trash").popup(i)};r.find("[data-action]").not("a").click((function(o){var r=t(this);var u="primary";var d=r.data("classroom_id");var p=r.data("class_post_id");var f=r.data("action");var _=function t(e){alert("Something Went Wrong!");a(r,true)};function v(){a(r,false);t.ajax({url:window._tutorobject.ajaxurl,data:{class_id:d,action:"tutor_gc_class_action",action_name:f,post_id:p,enroll_student:i},type:"POST",success:function t(o){try{o=JSON.parse(o)}catch(t){}if(e(o)!=="object"){_();return}switch(o.class_status){case"publish":u="success";break;case"draft":u="warning";break;case"trash":u="danger";break;default:u="primary";break}var i=r.parent().attr("class","class-status-"+o.class_status);o.edit_link?i.find(".class-edit-link").attr("href",o.edit_link):0;o.preview_link?i.find(".class-preview-link").attr("href",o.preview_link):0;o.post_id?i.find("[data-action]").attr("data-class_post_id",o.post_id):0;i.parent().find("[data-gc-status]").attr("class","tutor-badge-label label-"+u).text(o.status_text);a(r,true);if(n.length>0){var l=n.shift();l.trigger("click")}else{s(false)}},error:_})}if(f=="delete"&&o.originalEvent){c(v);return}else if(f!=="import"||!o.originalEvent){v()}else{l(v)}}));t("#tutor_gc_classroom_code_privilege").change((function(){var e=t(this).prop("checked")?"yes":"no";t.ajax({url:window._tutorobject.ajaxurl,data:{action:"tutor_gc_classroom_code_privilege",enabled:e},type:"POST",error:function t(){alert("Action Failed.")}})}));t("#tutor_gc_bulk_action_button").click((function(){if(s(false)==true){return}var e=t(this).prev().val();var a=[];t(".google-classroom-class-list .tutor-bulk-checkbox:checked").each((function(){var o=t(this).closest("tr").find('[data-action="'+e+'"]:visible');o.length>0?a.push(o.eq(0)):0}));if(!a.length){alert("Please select the correct option to take an action.");return}function o(){n=a.slice(1);s(true);a[0].trigger("click")}if(e=="import"){l(o)}else if(e=="delete"){c(o)}else{o()}}));t("#tutor_gc_dashboard").on("input","#tutor-gc-search-class",(function(){var e=t(".google-classroom-class-list>tbody");var a=t(this).val()||"";a=a.trim();if(a==""){e.children().show();return}e.children().each((function(){var e=t(this).children();var o=e.filter(".tutor-gc-title").text().toLowerCase().trim();var r=e.filter(".tutor-gc-code").text().toLowerCase().trim();var i=o.indexOf(a.toLowerCase())>-1||r==a;t(this)[i?"show":"hide"]()}))}));var u=function t(){window.location.reload()};t("#tutor_gc_credential_upgrade").click((function(e){e.preventDefault();var o=t(this).data("message");if(o&&!confirm(o)){return}a(t(this),false);t.ajax({url:window._tutorobject.ajaxurl,data:{action:"tutor_gc_credential_upgrade"},type:"POST",success:u,error:u})}))}))})();