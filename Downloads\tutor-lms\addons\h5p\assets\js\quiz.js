(()=>{var t={};window.jQuery(document).ready((function(t){var e=window.wp.i18n.__;t("button[name='quiz_answer_submit_btn']").on("click",(function(){var n=[];var i=false;var a=t("button[name='quiz_answer_submit_btn']");var o=document.querySelectorAll("[data-h5p-quiz-content-id]");if(o&&o.length>0){o.forEach((function(e){var i=parseInt(t(e).attr("id").match(/\d+/)[0],10);var a=parseInt(t(e).data("h5p-quiz-content-id"),10);n.push({question_id:i,content_id:a})}))}t.ajax({url:_tutorobject.ajaxurl,type:"POST",async:false,data:{action:"check_h5p_question_answered",question_ids:JSON.stringify(n),attempt_id:parseInt(t("input[name='attempt_id']").val(),10),quiz_id:parseInt(t("#tutor_quiz_id").val(),10)},beforeSend:function t(){a.addClass("is-loading").attr("disabled",true)},success:function n(a){if(a.success){var s=a.data.required_answers;var r=JSON.parse(s);o.forEach((function(e){var n=t(e).children(".answer-help-block")[0];if(t(n).children("p").length>0){t(n).children("p").remove()}}));if(r.length>0){t("#quiz-attempt-single-question-".concat(r[0].question_id)).get(0).scrollIntoView();r.forEach((function(n){var i=t("#quiz-attempt-single-question-"+n.question_id).children(".answer-help-block");t(i).html("<p class='answer-required' style=\"color: #dc3545\">".concat(e("The answer for this question is required","tutor_pro"),"</p>"))}))}else{i=true}}},complete:function t(){a.removeClass("is-loading").attr("disabled",false)}});return i}));var n=null;if(H5P||H5P.externalDispatcher){var i=function e(i){var a=parseInt(t("#tutor_quiz_id").val(),10);var o="h5p-local-content-id";var s="h5p-local-question-id";n=i.data.statement;var r=n.object.definition.extensions["http://h5p.org/x-api/"+o];if(n.object.definition.extensions["http://h5p.org/x-api/"+s]!==undefined){var c=n.object.definition.extensions["http://h5p.org/x-api/"+s];t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{action:"save_h5p_question_xAPI_statement",quiz_id:a,question_id:c,statement:JSON.stringify(n),content_id:r,attempt_id:t("input[name='attempt_id']").val()}})}};H5P.externalDispatcher.on("xAPI",i)}var a=document.querySelectorAll(".h5p-content");a.forEach((function(e){var n=parseInt(t(e).closest(".quiz-attempt-single-question").attr("id").match(/\d+/)[0],10);if(H5P){H5P.XAPIEvent.prototype.setObject=function(t){if(t.contentId){this.data.statement.object={id:this.getContentXAPIId(t),objectType:"Activity",definition:{extensions:{"http://h5p.org/x-api/h5p-local-content-id":t.contentId,"http://h5p.org/x-api/h5p-local-question-id":n}}};if(t.subContentId){this.data.statement.object.definition.extensions["http://h5p.org/x-api/h5p-subContentId"]=t.subContentId;if(typeof t.getTitle==="function"){this.data.statement.object.definition.name={"en-US":t.getTitle()}}}else{var e=H5P.getContentForInstance(t.contentId);if(e&&e.metadata&&e.metadata.title){this.data.statement.object.definition.name={"en-US":H5P.createTitle(e.metadata.title)}}}}else{this.data.statement.object={definition:{}}}}}}));var o=document.querySelectorAll(".h5p-iframe");o.forEach((function(e){var n=parseInt(t(e).closest(".quiz-attempt-single-question").attr("id").match(/\d+/)[0],10);var i=setInterval((function(){var a=e.contentDocument||e.contentWindow.document;if(a.readyState==="complete"||a.readyState==="interactive"){e.contentWindow.postMessage({action:"set_iframe",selector:".h5p-content",question_id:n,content_id:t(e).closest(".quiz-attempt-single-question").data("h5p-quiz-content-id")},"*");clearInterval(i)}}),500)}))}))})();