(()=>{var t={};var e=document.querySelector("#complete_lesson_enabled")?document.querySelector("#complete_lesson_enabled").value:0;var n=document.querySelector('button[name="complete_lesson_btn"]');var a=[];document.querySelectorAll("[data-content-id]").forEach((function(t){a.push(t.dataset.contentId)}));var o=document.querySelector(".tutor-spotlight-h5p-lesson-content");window.jQuery(document).ready((function(t){var o=wp.i18n.__;if(e){t(n).attr("disabled",true);t(n).wrap('<div class="tooltip-wrap"></div>').after('<span class="tooltip-txt tooltip-bottom">'.concat(o("Finish the content to complete the lesson ","tutor-pro"),"</span>"))}if(H5P||H5P.externalDispatcher){var r=t(".tutor-spotlight-h5p-lesson-content");var i=parseInt(r.data("course-id"),10);var s=parseInt(r.data("topic-id"),10);var c=parseInt(r.data("lesson-id"),10);var d="h5p-local-content-id";var l=function e(n){var a=n.data.statement.object.definition.extensions["http://h5p.org/x-api/"+d];t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{action:"save_h5p_lesson_xAPI_statement",course_id:i,content_id:a,topic_id:s,lesson_id:c,statement:JSON.stringify(n.data.statement)}})};H5P.externalDispatcher.on("xAPI",l);H5P.externalDispatcher.on("initialized",(function(){if(H5P.instances.length>0){H5P.instances.forEach((function(t){H5P.on(t,"xAPI",(function(t){var e=this;if((t.getVerb()==="completed"||t.getVerb()==="answered")&&!t.getVerifiedStatementValue(["context","contextActivities","parent"])){a=a.filter((function(t){return parseInt(t)!==parseInt(e.contentId)}));if(a.length===0){n.attributes.removeNamedItem("disabled");n.parentNode.removeChild(n.nextSibling);jQuery.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{action:"set_h5p_lesson_finished",_tutor_nonce:_tutorobject._tutor_nonce,lesson_id:c}})}}}))}))}}))}}));window.addEventListener("message",(function(t){if(t.data&&t.data.action==="h5p_setFinished"){a=a.filter((function(e){return e!==t.data.contentId}));var e=jQuery(o).data("lesson-id");if(a.length===0){n.attributes.removeNamedItem("disabled");n.parentNode.removeChild(n.nextSibling);jQuery.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{action:"set_h5p_lesson_finished",_tutor_nonce:_tutorobject._tutor_nonce,lesson_id:e}})}}}))})();