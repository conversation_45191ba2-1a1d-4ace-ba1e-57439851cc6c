(()=>{"use strict";var t={};function e(t){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function e(){return t};var t={},n=Object.prototype,o=n.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function t(e,r,n){return e[r]=n}}function s(t,e,r,n){var o=e&&e.prototype instanceof d?e:d,a=Object.create(o.prototype),u=new O(n||[]);return i(a,"_invoke",{value:_(t,r,u)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var p={};function d(){}function y(){}function v(){}var m={};f(m,u,(function(){return this}));var g=Object.getPrototypeOf,w=g&&g(g(k([])));w&&w!==n&&o.call(w,u)&&(m=w);var b=v.prototype=d.prototype=Object.create(m);function x(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,r){function n(i,a,u,c){var l=h(t[i],t,a);if("throw"!==l.type){var f=l.arg,s=f.value;return s&&"object"==e(s)&&o.call(s,"__await")?r.resolve(s.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(s).then((function(t){f.value=t,u(f)}),(function(t){return n("throw",t,u,c)}))}c(l.arg)}var a;i(this,"_invoke",{value:function t(e,o){function i(){return new r((function(t,r){n(e,o,t,r)}))}return a=a?a.then(i,i):i()}})}function _(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return P()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=E(a,r);if(u){if(u===p)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=h(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var o=h(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,p;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function k(t){if(t){var e=t[u];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(o.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=undefined,e.done=!0,e};return n.next=n}}return{next:P}}function P(){return{value:undefined,done:!0}}return y.prototype=v,i(b,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:y,configurable:!0}),y.displayName=f(v,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,f(t,l,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(L.prototype),f(L.prototype,c,(function(){return this})),t.AsyncIterator=L,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new L(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(b),f(b,l,"Generator"),f(b,u,(function(){return this})),f(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,O.prototype={constructor:O,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var r=this;function n(t,n){return u.type="throw",u.arg=e,r.next=t,n&&(r.method="next",r.arg=undefined),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),l=o.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function t(e,r){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),p},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),j(n),p}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;j(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),p}},t}function n(t,e,r,n,o,i,a){try{var u=t[i](a);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,o)}}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var a=t.apply(e,r);function u(t){n(a,o,i,u,c,"next",t)}function c(t){n(a,o,i,u,c,"throw",t)}u(undefined)}))}}function i(t){return a.apply(this,arguments)}function a(){a=o(r().mark((function t(e){var n;return r().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;r.next=3;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:e});case 3:n=r.sent;return r.abrupt("return",n);case 7:r.prev=7;r.t0=r["catch"](0);tutor_toast(__("Operation failed","tutor-pro"),r.t0,"error");case 10:case"end":return r.stop()}}),t,null,[[0,7]])})));return a.apply(this,arguments)}function u(t){"@babel/helpers - typeof";return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function t(e,r,n){return e[r]=n}}function s(t,e,r,o){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),u=new O(o||[]);return n(a,"_invoke",{value:_(t,r,u)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var p={};function d(){}function y(){}function v(){}var m={};f(m,i,(function(){return this}));var g=Object.getPrototypeOf,w=g&&g(g(k([])));w&&w!==e&&r.call(w,i)&&(m=w);var b=v.prototype=d.prototype=Object.create(m);function x(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function o(n,i,a,c){var l=h(t[n],t,i);if("throw"!==l.type){var f=l.arg,s=f.value;return s&&"object"==u(s)&&r.call(s,"__await")?e.resolve(s.__await).then((function(t){o("next",t,a,c)}),(function(t){o("throw",t,a,c)})):e.resolve(s).then((function(t){f.value=t,a(f)}),(function(t){return o("throw",t,a,c)}))}c(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function _(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return P()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=E(a,r);if(u){if(u===p)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=h(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var o=h(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,p;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:P}}function P(){return{value:undefined,done:!0}}return y.prototype=v,n(b,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:y,configurable:!0}),y.displayName=f(v,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,f(t,l,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(L.prototype),f(L.prototype,a,(function(){return this})),t.AsyncIterator=L,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new L(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(b),f(b,l,"Generator"),f(b,i,(function(){return this})),f(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,O.prototype={constructor:O,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),p},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),j(n),p}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;j(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),p}},t}function l(t,e,r,n,o,i,a){try{var u=t[i](a);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,o)}}function f(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){l(i,n,o,a,u,"next",t)}function u(t){l(i,n,o,a,u,"throw",t)}a(undefined)}))}}function s(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=h(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var o=function t(){};return{s:o,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();i=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!i&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function h(t,e){if(!t)return;if(typeof t==="string")return p(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}function p(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}jQuery(document).ready((function(t){"use strict";var e=wp.i18n,r=e.__,n=e._x,o=e._n,a=e._nx;var u=document.getElementById("tutor-common-confirmation-modal");var l=document.getElementById("tutor-common-confirmation-form");t(document).on("click",".tutor-quiz-attempt-delete-btn",(function(e){e.preventDefault();var r=t(this);t.ajax({url:ajaxurl,type:"POST",data:{attempt_id:r.attr("data-attempt-id"),action:"treport_quiz_atttempt_delete"},beforeSend:function t(){r.addClass("updating-message")},success:function t(e){if(e.success){r.closest("tr").remove()}},complete:function t(){r.removeClass("updating-message")}})}));function h(t,e){var r=new URL(window.location.href);var n=r.searchParams;n.set(t,e);r.search=n.toString();n.set("paged",1);r.search=n.toString();return r.toString()}t(".tutor-report-category").on("change",(function(e){window.location=h("cat",t(this).val())}));t(".tutor-report-sort").on("change",(function(e){window.location=h("order",t(this).val())}));t(".tutor-report-date").on("change",(function(e){window.location=h("date",t(this).val())}));t(document).on("click",".tutor-report-search-btn",(function(e){window.location=h("search",t(".tutor-report-search").val())}));t(document).on("click",".tutor-report-search-action",(function(e){e.preventDefault();window.location=h("search",t(".tutor-report-search").val())}));t(document).on("click",".details-link",(function(e){e.preventDefault();if(t(this).hasClass("active")){t(this).removeClass("active")}else{t(this).addClass("active")}var r=t("#table-toggle-"+t(this).data("count"));if(r.hasClass("open")){r.removeClass("open")}else{r.addClass("open")}}));var p=document.querySelectorAll(".tutor-delete-recent-reviews");var d=document.getElementById("tutor-admin-reviews-table");var y=s(p),v;try{for(y.s();!(v=y.n()).done;){var m=v.value;if(m){m.onclick=function(){var t=f(c().mark((function t(e){var r;return c().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:r=e.currentTarget.dataset.id;if(l){l.elements.action.value="tutor_delete_review";l.elements.id.value=r}case 2:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}}}catch(t){y.e(t)}finally{y.f()}if(l){l.onsubmit=function(){var t=f(c().mark((function t(e){var n,o,a,f,s;return c().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:e.preventDefault();n=new FormData(l);o=l.querySelector("[data-tutor-modal-submit]");o.classList.add("is-loading");c.next=6;return i(n);case 6:a=c.sent;if(u.classList.contains("tutor-is-active")){u.classList.remove("tutor-is-active");document.body.classList.remove("tutor-modal-open")}if(!a.ok){c.next=15;break}c.next=11;return a.json();case 11:f=c.sent;if(f){if(d&&n.get("action")=="tutor_delete_review"){tutor_toast(r("Delete","tutor-pro"),r("Review has been deleted ","tutor-pro"),"success");if(d.rows.length<3){location.reload()}else{s=d.querySelector('a[data-id="'.concat(n.get("id"),'"]'));if(s){s.closest("tr").remove()}}}else{location.reload()}}else{tutor_toast(r("Failed","tutor-pro"),r("Review delete failed ","tutor-pro"),"error")}c.next=16;break;case 15:tutor_toast(r("Failed","tutor-pro"),r("Review delete failed ","tutor-pro"),"error");case 16:case"end":return c.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}}))})();