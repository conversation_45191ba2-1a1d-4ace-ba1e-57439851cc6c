(()=>{var t={};window.jQuery(document).ready((function(t){t(document).on("click",".open-tutor-h5p-statement-result-modal-btn",(function(e){e.preventDefault();var a=t(this);var n=parseInt(a.data("statement-id"),10);var r=parseInt(a.data("content-id"),10);var o=a.data("is-lesson");t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{action:"view_h5p_statement_result",statement_id:n,content_id:r,is_lesson:o},beforeSend:function t(){a.addClass("is-loading").attr("disabled",true)},success:function e(a){if(!a.success){tutor_toast(__("Error","tutor-pro"),get_response_message(a),"error");return}t(".h5p-statement-result-modal .tutor-modal-container").html(a.data.output);t(".h5p-statement-result-modal").addClass("tutor-is-active");window.dispatchEvent(new Event(_tutorobject.content_change_event))},complete:function t(){a.removeClass("is-loading").attr("disabled",false)}})}));t(document).on("click",".open-tutor-h5p-quiz-result-modal-btn",(function(e){e.preventDefault();var a=t(this);var n=parseInt(a.data("question-id"),10);var r=parseInt(a.data("user-id"),10);var o=parseInt(a.data("quiz-id"),10);var s=parseInt(a.data("attempt-id"),10);var i=parseInt(a.data("content-id"),10);t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{action:"view_h5p_quiz_result",question_id:n,user_id:r,quiz_id:o,attempt_id:s,content_id:i},beforeSend:function t(){a.addClass("is-loading").attr("disabled",true)},success:function e(a){if(!a.success){tutor_toast(__("Error","tutor-pro"),get_response_message(a),"error");return}t(".h5p-quiz-result-modal .tutor-modal-container").html(a.data.output);t(".h5p-quiz-result-modal").addClass("tutor-is-active");window.dispatchEvent(new Event(_tutorobject.content_change_event))},complete:function t(){a.removeClass("is-loading").attr("disabled",false)}})}))}))})();