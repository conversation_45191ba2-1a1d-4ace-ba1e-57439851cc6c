(()=>{var e={};jQuery(document).ready((function(e){var _=e('select[name="tutor_option[spam_protection_method]"]');var c=e('#field_enable_spam_protection input[type="checkbox"]');setTimeout((function(){return _.trigger("change")}));c.change((function(){_.trigger("change")}));function t(_){if(_==="recaptcha_v3"){e("#field_recaptcha_v3_secret_key").show(0);e("#field_recaptcha_v3_site_key").show(0);e("#field_recaptcha_v2_secret_key").hide(0);e("#field_recaptcha_v2_site_key").hide(0)}else if(_==="recaptcha_v2"){e("#field_recaptcha_v3_secret_key").hide(0);e("#field_recaptcha_v3_site_key").hide(0);e("#field_recaptcha_v2_secret_key").show(0);e("#field_recaptcha_v2_site_key").show(0)}else{e("#field_recaptcha_v3_secret_key").hide(0);e("#field_recaptcha_v3_site_key").hide(0);e("#field_recaptcha_v2_secret_key").hide(0);e("#field_recaptcha_v2_site_key").hide(0)}}t(_.val());_.change((function(){var _=e(this).val();t(_)}))}))})();