(()=>{"use strict";var t={};var e=function t(e,a){var o=wp.i18n.__;var n=e||{},r=n.data,i=r===void 0?{}:r;var s=i.message,c=s===void 0?a||o("Something Went Wrong!","tutor-pro"):s;return c};jQuery(document).ready((function(t){"use strict";var a=wp.i18n.__;t(document).on("click",".btn-csv-download",(function(o){o.preventDefault();var n=t(this);t.ajax({url:ajaxurl,type:"POST",data:{quiz_id:t(this).data("id"),action:"quiz_export_data"},beforeSend:function t(){n.addClass("is-loading")},success:function t(o){if(!o.success){tutor_toast(a("Error!","tutor-pro"),e(o),"error");return}var n="";o.data.output_quiz_data.forEach((function(t){var e=t.join(",");n+=e+"\r\n"}));var r=new Blob([n],{type:"text/csv"});var i=window.webkitURL.createObjectURL(r);var s=document.createElement("a");s.setAttribute("href",i);s.setAttribute("download","tutor-quiz-"+o.data.title+".csv");document.body.appendChild(s);s.click()},complete:function t(){n.removeClass("is-loading")}})}));t(document).on("change",'#tutor-course-content-builder-root input[name="csv_file"]',(function(o){var n=t(this).prop("files");var r=t(this);var i=t(this).parent().find("button");if(n[0]){if(n[0].size>0){var s=tutor_get_nonce_data(true);var c=new FormData;c.append("action","quiz_import_data");c.append("csv_file",n[0]);c.append("topic_id",t(this).parent().find("input[name='csv_file']").data("topic"));c.append(s.key,s.value);t.ajax({url:ajaxurl,type:"POST",data:c,cache:false,contentType:false,processData:false,beforeSend:function t(){i.addClass("is-loading")},success:function t(o){if(o.success){r.val("");r.closest(".tutor-topics-wrap").find(".tutor-lessons").append(o.data.html)}else{tutor_toast(a("Error","tutor-pro"),e(o),"error")}},complete:function t(){i.removeClass("is-loading")}})}else{alert("File is Empty.")}}else{alert("No File Selected.")}t(this).val("")}));t(document).on("click",".tutor-import-quiz-button button",(function(e){e.preventDefault();t(this).parent().find(".tutor-csv-file").click()}))}))})();