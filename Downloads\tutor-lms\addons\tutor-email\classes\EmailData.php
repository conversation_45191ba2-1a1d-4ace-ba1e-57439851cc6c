<?php
/**
 * E-mail data
 *
 * @package TutorPro
 * @subpackage Addons\TutorEmail
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 2.0.0
 */

namespace TUTOR_EMAIL;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class E-mail Data
 *
 * @since 2.0.0
 */
class EmailData {
	/**
	 * Get recipient data.
	 *
	 * @return array
	 */
	public function get_recipients() {
		$email_array = array(
			'email_to_students' => array(
				'welcome_student'             => array(
					'label'        => __( 'Welcome Email After Registration', 'tutor-pro' ),
					'default'      => 'on',
					'template'     => 'to_student_welcome',
					'tooltip'      => __( 'Enable to send welcome email after registration', 'tutor-pro' ),
					'subject'      => __( 'Welcome to {site_name} - Let the Learning Begin', 'tutor-pro' ),
					'heading'      => __( 'Thanks for Your Registration!', 'tutor-pro' ),
					'message'      => json_encode( 'Hi {user_name}! <br/>We are thrilled to have you on board and embark on this learning journey together.<br/><br/>Regards,<br/>{site_name}' ),
					'placeholders' => EmailPlaceholder::only( array( 'user_name', 'site_url', 'site_name', 'dashboard_url' ) ),
				),
				'course_enrolled'             => array(
					'label'               => __( 'Course Enrolled', 'tutor-pro' ),
					'default'             => 'on',
					'template'            => 'to_student_course_enrolled',
					'tooltip'             => __( 'Enable to send course enrolled email notification', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'subject'             => __( 'You are Enrolled in a New Course', 'tutor-pro' ),
					'heading'             => __( 'You are enrolled in New Course', 'tutor-pro' ),
					'course_name'         => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'course_url'          => esc_url( tutor()->url ),
					'message'             => json_encode( 'Welcome to the course, the most popular and modern server at <strong>{site_url}</strong>. Happy learning.' ),
					'placeholders'        => EmailPlaceholder::only( array( 'user_name', 'course_name', 'enroll_time', 'course_url', 'course_start_url', 'site_url', 'site_name' ) ),
				),
				'inactive_student'            => array(
					'label'         => __( 'Reminder to Inactive Students', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_student_inactive_student',
					'tooltip'       => __( 'Enable to send email notification to inactive students', 'tutor-pro' ),
					'subject'       => __( 'Reminder: You are inactive for {inactive_days} days!', 'tutor-pro' ),
					'heading'       => __( 'Don\'t give up!', 'tutor-pro' ),
					'inactive_days' => 10,
					'message'       => json_encode( "We've noticed you are inactive in {site_name} for {inactive_days} days . Please login and continue your eLearning journey." ),
					'placeholders'  => EmailPlaceholder::only( array( 'user_name', 'site_url', 'site_name', 'inactive_days' ) ),
				),
				'lesson_comment_replied'      => array(
					'label'         => __( 'Comments in Lesson', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_student_comment_thread',
					'tooltip'       => __( 'Enable to send email notification for comments in lesson', 'tutor-pro' ),
					'subject'       => __( 'New Comment on {lesson_title}', 'tutor-pro' ),
					'comment_by'    => __( 'Comment Author', 'tutor-pro' ),
					'comment_date'  => __( '1 day ago', 'tutor-pro' ),
					'heading'       => __( 'Email Notification for Comments in Lesson', 'tutor-pro' ),
					'message'       => json_encode( "We wanted to notify you that there's a new comment on the lesson {lesson_title} in {course_name}. Please view and respond to the comment." ),
					'question'      => __( 'I help ambitious graphic designers and hand letterers level-up their skills and creativity.', 'tutor-pro' ),
					'before_button' => __( 'Please click on this link to reply to the comment.', 'tutor-pro' ),
					'placeholders'  => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'comment', 'comment_by', 'comment_date', 'lesson_title' ) ),
				),
				'quiz_completed'              => array(
					'label'        => __( 'Quiz Completed', 'tutor-pro' ),
					'default'      => 'off',
					'template'     => 'to_student_quiz_completed',
					'tooltip'      => __( 'Enable to send quiz completed email notification', 'tutor-pro' ),
					'subject'      => __( 'Your quiz attempt is graded', 'tutor-pro' ),
					'heading'      => __( 'Thank you for attempting the quiz', 'tutor-pro' ),
					'username'     => __( 'Student', 'tutor-pro' ),
					'quiz_name'    => __( 'Quiz Name of a course!', 'tutor-pro' ),
					'message'      => json_encode( 'The grade has been submitted for the quiz <strong>{quiz_name}</strong> for the course <strong>{course_name}</strong>.' ),
					'placeholders' => EmailPlaceholder::only( array( 'user_name', 'course_name', 'quiz_name', 'total_marks', 'earned_marks', 'attempt_result', 'attempt_url', 'submission_time', 'quiz_url', 'site_url', 'site_name' ) ),
				),
				'completed_course'            => array(
					'label'               => __( 'Completed a Course', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_student_course_completed',
					'tooltip'             => __( 'Enable to send course completion email notification', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'subject'             => __( 'Congratulations on Finishing {course_name}', 'tutor-pro' ),
					'heading'             => __( 'Congratulations on Finishing the Course', 'tutor-pro' ),
					'message'             => json_encode( 'Congratulations on completing the course <strong>{course_name}</strong>. We hope that you had a great experience.' ),
					'footer_text'         => __( 'If you have additional feedback, you may reply to this email to communicate with the instructor', 'tutor-pro' ),
					'before_button'       => __( 'We would really appreciate it if you can post a review on the course and the instructor. Your valuable feedback would help us improve the content on our site and improve the learning experience.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'user_name', 'course_name', 'completion_time', 'course_url', 'instructor_email', 'site_url', 'site_name' ) ),
				),
				'remove_from_course'          => array(
					'label'        => __( 'Removed From Course', 'tutor-pro' ),
					'default'      => 'off',
					'template'     => 'to_student_remove_from_course', // ==/
					'tooltip'      => __( 'Enable to send removed from the course email notification', 'tutor-pro' ),
					'subject'      => __( 'Update Regarding Course Enrollment', 'tutor-pro' ),
					'heading'      => __( 'Course Enrollment Removal Notification', 'tutor-pro' ),
					'message'      => json_encode( 'I hope this message finds you well. We regret to inform you that you have been removed from {course_name}.' ),
					'placeholders' => EmailPlaceholder::only( array( 'user_name', 'course_name', 'course_url', 'site_url', 'site_name' ) ),
				),
				'assignment_graded'           => array(
					'label'               => __( 'Assignment Graded', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_student_assignment_evaluate',
					'tooltip'             => __( 'Enable to send assignment graded email notification', 'tutor-pro' ),
					'subject'             => __( 'Assignment Graded - {assignment_name}', 'tutor-pro' ),
					'username'            => __( 'Student', 'tutor-pro' ),
					'assignment_max_mark' => __( '100', 'tutor-pro' ),
					'assignment_score'    => __( '80', 'tutor-pro' ),
					'heading'             => __( 'Assignment has been Graded', 'tutor-pro' ),
					'course_name'         => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'assignment_name'     => __( 'Create your first WordPress site', 'tutor-pro' ),
					'assignment_comment'  => __( 'Comment created by instructor of the course {course_name}', 'tutor-pro' ),
					'message'             => json_encode( 'The instructor submitted the grade for the assignment <strong>{assignment_name}</strong> of the course <strong>{course_name}</strong>.' ),
					'block_heading'       => __( 'Instructor Note', 'tutor-pro' ),
					'block_content'       => __( 'What does it take to be successful? Ask around and you will find different answers to the formula of success. The truth is, success leaves clues and you can achieve.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'user_name', 'course_name', 'course_url', 'site_url', 'site_name', 'assignment_name', 'assignment_url', 'assignment_max_mark', 'assignment_score', 'assignment_comment' ) ),
				),

				'new_announcement_posted'     => array(
					'label'               => __( 'New Announcement Posted', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_student_new_announcement_posted',
					'tooltip'             => __( 'Enable to send new announcement post email notification', 'tutor-pro' ),
					'username'            => __( 'Student', 'tutor-pro' ),
					'assignment_max_mark' => __( '100', 'tutor-pro' ),
					'assignment_score'    => __( '80', 'tutor-pro' ),
					'subject'             => __( 'New Announcement Posted', 'tutor-pro' ),
					'heading'             => __( 'New Announcement: {announcement_title}', 'tutor-pro' ),
					'message'             => json_encode( 'We are excited to share a new announcement with you!' ),
					'placeholders'        => EmailPlaceholder::only( array( 'author_fullname', 'course_name', 'course_url', 'site_url', 'site_name', 'announcement_title', 'announcement_content', 'announcement_date' ) ),
				),
				'announcement_updated'        => array(
					'label'         => __( 'New Announcement Updated', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_student_announcement_updated',
					'tooltip'       => __( 'Enable to send email notification of updated announcement post', 'tutor-pro' ),
					'subject'       => __( 'The announcement at {course_name} is updated', 'tutor-pro' ),
					'heading'       => __( 'The instructor updated the announcement', 'tutor-pro' ),
					'message'       => json_encode( 'The instructor updated the announcement for - {course_name}.' ),
					'footer_text'   => __( 'You may reply to this email to communicate with the instructor', 'tutor-pro' ),
					'block_heading' => __( 'Upcomming Exam Notice & Schedule', 'tutor-pro' ),
					'block_content' => __( '<p>Assertively incentivize prospective users before alternative imperatives. Quickly strategize best-of-breed testing procedures after high-payoff human capital.</p><p>Seamlessly incentivize diverse quality vectors before clicks-and-mortar collaboration and idea-sharing. Dramatically fashion just in time partnerships without distinctive scenarios. Quickly predominate principle-centered results through corporate alignments.</p>', 'tutor-pro' ),
					'placeholders'  => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'announcement_title', 'announcement_content', 'announcement_date', 'author_fullname' ) ),
				),
				'after_question_answered'     => array(
					'label'         => __( 'Q&A Message Answered', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_student_question_answered',
					'tooltip'       => __( 'Enable to send Q&A reply email notification', 'tutor-pro' ),
					'subject'       => __( 'The instructor has replied to your question', 'tutor-pro' ),
					'answer_by'     => __( 'Answer Author', 'tutor-pro' ),
					'answer_date'   => __( '1 day ago', 'tutor-pro' ),
					'heading'       => __( 'Q&A Message Answered', 'tutor-pro' ),
					'message'       => json_encode( 'The instructor has answered your question on the course - {course_name}.' ),
					'question'      => __( 'I help ambitious graphic designers and hand letterers level-up their skills and creativity. Grab freebies + tutorials here! >> https://every-tuesday.com', 'tutor-pro' ),
					'before_button' => __( 'Please click on this link to reply to the answer.', 'tutor-pro' ),
					'placeholders'  => EmailPlaceholder::only( array( 'course_name', 'answer_url', 'course_url', 'site_url', 'site_name', 'answer', 'answer_by', 'answer_date', 'question' ) ),
				),
				'feedback_submitted_for_quiz' => array(
					'label'         => __( 'Feedback Submitted for Quiz Attempt', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_student_feedback_submitted_for_quiz',
					'tooltip'       => __( 'Enable to send quiz feedback email notification', 'tutor-pro' ),
					'subject'       => __( 'Your Quiz attempts feedback', 'tutor-pro' ),
					'username'      => __( 'Student', 'tutor-pro' ),
					'heading'       => __( 'Quiz Answers Reviewed', 'tutor-pro' ),
					'message'       => json_encode( 'You can check the quiz attempts for the {course_name} course from {review_url}.' ),
					'block_heading' => __( 'Instructor Note', 'tutor-pro' ),
					'block_content' => __( 'What does it take to be successful? Ask around and you will find different answers to the formula of success. The truth is, success leaves clues and you can achieve.', 'tutor-pro' ),
					'placeholders'  => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'quiz_name', 'total_marks', 'earned_marks', 'instructor_name', 'user_name', 'instructor_feedback', 'review_url' ) ),
				),
				'enrollment_expired'          => array(
					'label'        => __( 'Course Enrollment Expired', 'tutor-pro' ),
					'default'      => 'off',
					'template'     => 'to_student_enrollment_expired',
					'tooltip'      => __( 'Enable to send course enrollment expiration email notification', 'tutor-pro' ),
					'subject'      => __( 'Course Enrollment Expired', 'tutor-pro' ),
					'heading'      => __( 'Your Course Enrollment Has Expired', 'tutor-pro' ),
					'message'      => json_encode( 'We regret to inform you that your enrollment in the {course_name} has expired.' ),
					'placeholders' => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'user_name' ) ),
				),

				'new_lesson_published'        => array(
					'label'            => __( 'New Lesson Published', 'tutor-pro' ),
					'logo'             => TUTOR_EMAIL()->url . 'assets/images/tutor-logo.png',
					'default'          => 'off',
					'template'         => 'to_student_new_lesson_published',
					'tooltip'          => __( 'Enable to send new lesson published email notification', 'tutor-pro' ),
					'subject'          => __( 'A New Lesson is Added', 'tutor-pro' ),
					'heading'          => __( 'New Lesson Available', 'tutor-pro' ),
					'message'          => json_encode( 'We are delighted to inform you that a new lesson has been added to your course!' ),
					'student_name'     => __( 'Student', 'tutor-pro' ),
					'course_name'      => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'assignment_title' => __( 'Create your first WordPress site', 'tutor-pro' ),
					'before_button'    => __( 'Review the lesson from your instructor dashboard and submit the score at your earliest convenience.', 'tutor-pro' ),
					'footer_text'      => __( 'You may reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'     => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'lesson_title', 'user_name', 'dashboard_url' ) ),
				),

				'new_quiz_published'          => array(
					'label'            => __( 'New Quiz Published', 'tutor-pro' ),
					'logo'             => TUTOR_EMAIL()->url . 'assets/images/tutor-logo.png',
					'default'          => 'off',
					'template'         => 'to_student_new_quiz_published',
					'tooltip'          => __( 'Enable to send new quiz published email notification', 'tutor-pro' ),
					'subject'          => __( 'New Quiz Published', 'tutor-pro' ),
					'heading'          => __( 'New Quiz Published', 'tutor-pro' ),
					'message'          => json_encode( 'A new quiz has been added on course <strong>{course_name}</strong>.' ),
					'student_name'     => __( 'Student', 'tutor-pro' ),
					'course_name'      => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'assignment_title' => __( 'Create your first WordPress site', 'tutor-pro' ),
					'before_button'    => __( 'Review the quiz from your instructor dashboard and submit the score at your earliest convenience.', 'tutor-pro' ),
					'footer_text'      => __( 'Reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'     => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'quiz_title', 'user_name', 'dashboard_url' ) ),
				),

				'new_assignment_published'    => array(
					'label'           => __( 'New Assignment Published', 'tutor-pro' ),
					'logo'            => TUTOR_EMAIL()->url . 'assets/images/tutor-logo.png',
					'default'         => 'off',
					'template'        => 'to_student_new_assignment_published',
					'tooltip'         => __( 'Enable to send new assignment published email notification', 'tutor-pro' ),
					'subject'         => __( 'A New Assignment is Published', 'tutor-pro' ),
					'student_name'    => __( 'Student', 'tutor-pro' ),
					'course_name'     => __( 'Course name', 'tutor-pro' ),
					'assignment_name' => __( 'Create your first WordPress site', 'tutor-pro' ),
					'before_button'   => __( 'Review the assignment from your instructor dashboard and submit the score at your earliest convenience.', 'tutor-pro' ),
					'heading'         => __( 'New Assignment Published', 'tutor-pro' ),
					'message'         => json_encode( 'A new assignment has been added to the course <strong>{course_name}</strong>.' ),
					'footer_text'     => __( 'Reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'assignment_title', 'user_name', 'dashboard_url' ) ),
				),
			),
			'email_to_teachers' => array(
				'a_student_enrolled_in_course'    => array(
					'label'               => __( 'A Student Enrolled in Course', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_course_enrolled',
					'tooltip'             => __( 'Enable to send new student enrollment email notification', 'tutor-pro' ),
					'subject'             => __( 'New Student Enrolled at {course_name}', 'tutor-pro' ),
					'heading'             => __( 'New Student Enrolled', 'tutor-pro' ),
					'course_name'         => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'student_username'    => __( 'John Doe', 'tutor-pro' ),
					'student_email'       => __( '<EMAIL>', 'tutor-pro' ),
					'footer_text'         => __( 'You may reply to this email to communicate with the student', 'tutor-pro' ),
					'message'             => json_encode( 'We are excited to inform you that a new student has enrolled in the course {course_name}.' ),
					'placeholders'        => EmailPlaceholder::only( array( 'profile_url', 'course_url', 'site_url', 'site_name', 'student_username', 'user_name', 'student_email', 'enroll_time' ) ),
				),

				'a_student_completed_course'      => array(
					'label'         => __( 'A Student Completed Course', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_instructor_course_completed',
					'tooltip'       => __( 'Enable to send student course completion email notification', 'tutor-pro' ),
					'student_name'  => __( 'James Andy', 'tutor-pro' ),
					'subject'       => __( 'Congratulations! "{student_name}" Completed A Course.', 'tutor-pro' ),
					'heading'       => __( 'Student Just Completed a Course!', 'tutor-pro' ),
					'message'       => json_encode( 'Your student <strong>{student_name}</strong> has just completed the course <strong>{course_name}</strong>. This is a great milestone for you as a teacher and we want you to celebrate this moment.' ),
					'before_button' => __( 'You can view the students progress reports and course details by clicking the button at the bottom of this email.', 'tutor-pro' ),
					'footer_text'   => __( 'You may reply to this email to communicate with the student.', 'tutor-pro' ),
					'placeholders'  => EmailPlaceholder::only( array( 'student_username', 'course_url', 'site_url', 'site_name', 'student_name', 'user_name', 'student_email', 'student_report_url', 'completion_time' ) ),
				),

				'a_student_completed_lesson'      => array(
					'label'        => __( 'A Student Completed Lesson', 'tutor-pro' ),
					'default'      => 'off',
					'template'     => 'to_instructor_lesson_completed',
					'tooltip'      => __( 'Enable to send student lesson completion email notification', 'tutor-pro' ),
					'subject'      => __( '{student_name} completed the lesson {lesson_name}', 'tutor-pro' ),
					'heading'      => __( 'Lesson Completed', 'tutor-pro' ),
					'message'      => json_encode( "Exciting news! A student has successfully completed a lesson in the course {course_name}. Your guidance and the student's commitment to learning have made this achievement possible." ),
					// 'before_button' => __( 'You can view the students progress reports and lesson details by clicking the button at the bottom of this email.', 'tutor-pro' ),
					'footer_text'  => __( 'You may reply to this email to communicate with the student', 'tutor-pro' ),
					'placeholders' => EmailPlaceholder::only( array( 'lesson_name', 'course_url', 'site_url', 'site_name', 'student_name', 'user_name', 'student_email', 'lesson_url', 'completion_time' ) ),
				),

				'a_student_placed_question'       => array(
					'label'               => __( 'New Q&A Message', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_asked_question_by_student',
					'tooltip'             => __( 'Enable to send Q&A new question email notification', 'tutor-pro' ),
					'student_name'        => __( 'James Andy', 'tutor-pro' ),
					'subject'             => __( 'New Question from {student_name} on {course_name}', 'tutor-pro' ),
					'heading'             => __( 'New Q&A Message', 'tutor-pro' ),
					'instructor_username' => __( 'Teacher Name', 'tutor-pro' ),
					'course_name'         => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'enroll_time'         => __( '1 days ago', 'tutor-pro' ),
					'message'             => json_encode( 'Student - {student_name} has asked a question on the course {course_name}' ),
					'question'            => __( 'I help ambitious graphic designers and hand letterers level-up their skills and creativity. Grab freebies + tutorials here! >> https://every-tuesday.com.', 'tutor-pro' ),
					'footer_text'         => __( 'Please click on this button to reply to the question', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'question_title', 'question_url', 'course_url', 'site_url', 'site_name', 'student_username', 'user_name', 'question' ) ),
				),

				'new_lesson_comment_posted'       => array(
					'label'         => __( 'Comments in Lesson', 'tutor-pro' ),
					'default'       => 'off',
					'template'      => 'to_instructor_commented_student',
					'tooltip'       => __( 'Enable to send email notification for comments in lesson', 'tutor-pro' ),
					'subject'       => __( 'New Comment on {lesson_title}', 'tutor-pro' ),
					'comment_by'    => __( 'Comment Author', 'tutor-pro' ),
					'comment_date'  => __( '1 day ago', 'tutor-pro' ),
					'heading'       => __( 'Email Notification for Comments in Lesson', 'tutor-pro' ),
					'message'       => json_encode( "We wanted to notify you that there's a new comment on the lesson {lesson_title} in {course_name}. Please view and respond to the comment." ),
					'question'      => __( 'I help ambitious graphic designers and hand letterers level-up their skills and creativity.', 'tutor-pro' ),
					'before_button' => __( 'Please click on this link to reply to the comment.', 'tutor-pro' ),
					'placeholders'  => EmailPlaceholder::only( array( 'course_name', 'course_url', 'site_url', 'site_name', 'comment', 'comment_by', 'comment_date', 'lesson_title' ) ),
				),

				'student_submitted_quiz'          => array(
					'label'               => __( 'Student Submitted Quiz', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_quiz_completed',
					'tooltip'             => __( 'Enable to send student quiz submission email notification', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'student_name'        => __( 'James Andy', 'tutor-pro' ),
					'subject'             => __( '{student_name} submitted the quiz- {quiz_name} from the course- {course_name}', 'tutor-pro' ),
					'heading'             => __( 'Student Attempted a Quiz', 'tutor-pro' ),
					'message'             => json_encode( '{student_name} has attempted a quiz.' ),
					'footer_text'         => __( 'You may reply to this email to communicate with the student.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'attempt_url', 'course_url', 'site_url', 'site_name', 'student_name', 'user_name', 'quiz_review_url', 'submission_time', 'quiz_name' ) ),
				),

				'student_submitted_assignment'    => array(
					'label'           => __( 'Student Submitted Assignment', 'tutor-pro' ),
					'default'         => 'off',
					'template'        => 'to_instructor_student_submitted_assignment',
					'tooltip'         => __( 'Enable to send student assignment submission email notification', 'tutor-pro' ),
					'student_name'    => __( 'James Andy', 'tutor-pro' ),
					'subject'         => __( '{student_name} submitted the assignment- {assignment_name} from the course- {course_name}', 'tutor-pro' ),
					'instructor_name' => __( 'Instructor', 'tutor-pro' ),
					'course_name'     => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'assignment_name' => __( 'Create your first WordPress site', 'tutor-pro' ),
					'before_button'   => __( 'Review the assignment from your instructor dashboard and submit the score at your earliest convenience.', 'tutor-pro' ),
					'heading'         => __( 'New Assignment Submitted', 'tutor-pro' ),
					'message'         => json_encode( 'An assignment has been submitted.' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the student.', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'assignment_name', 'review_link', 'site_url', 'site_name', 'student_name', 'user_name' ) ),
				),

				'withdrawal_request_approved'     => array(
					'label'           => __( 'Withdrawal Request Approved', 'tutor-pro' ),
					'default'         => 'off',
					'template'        => 'to_instructor_withdrawal_request_approved',
					'tooltip'         => __( 'Enable to send withdrawal request approved email notification', 'tutor-pro' ),
					'subject'         => __( 'Congratulations! Withdrawal Request Successful!', 'tutor-pro' ),
					'heading'         => __( 'Withdrawal Request Successful!', 'tutor-pro' ),
					'withdraw_amount' => __( '9XX USD', 'tutor-pro' ),
					'message'         => json_encode( 'Congratulations! We have sent your requested withdrawal amount via <strong>{withdraw_method_name}</strong> on {withdraw_approve_time}.' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the admin', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'withdraw_amount', 'withdraw_method_name', 'total_amount', 'withdraw_approve_time', 'site_url', 'site_name', 'user_name', 'instructor_username' ) ),
				),

				'withdrawal_request_rejected'     => array(
					'label'           => __( 'Withdrawal Request Rejected', 'tutor-pro' ),
					'default'         => 'off',
					'template'        => 'to_instructor_withdrawal_request_rejected',
					'tooltip'         => __( 'Enable to send withdrawal request rejected email notification', 'tutor-pro' ),
					'subject'         => __( 'Withdrawal Request Rejected!', 'tutor-pro' ),
					'heading'         => __( 'Withdrawal Request Rejected!', 'tutor-pro' ),
					'withdraw_amount' => __( '20 USD', 'tutor-pro' ),
					'message'         => json_encode( 'We are sorry to inform you that we could not process the request for withdrawal via <strong>{withdraw_method_name}</strong> on {withdraw_reject_time}. Please reply to this email for further details.' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the admin', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'withdraw_amount', 'withdraw_method_name', 'withdraw_reject_time', 'site_url', 'site_name', 'user_name', 'instructor_username' ) ),
				),

				'withdrawal_request_received'     => array(
					'label'               => __( 'Withdrawal Request Received', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_withdrawal_request_received',
					'tooltip'             => __( 'Enable to send withdrawal request received email notification', 'tutor-pro' ),
					'subject'             => __( 'Withdrawal Request Received!', 'tutor-pro' ),
					'withdraw_amount'     => __( '20 USD', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'instructor_email'    => __( '<EMAIL>', 'tutor-pro' ),
					'heading'             => __( 'Withdrawal Request Processing', 'tutor-pro' ),
					'message'             => json_encode( 'We have received a withdrawal request via {withdraw_method} on {withdraw_time}. Rest assured, we are in the process of reviewing and will initiate the withdrawal very soon.' ),
					'footer_text'         => __( 'You may reply to this email to communicate with the admin.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'withdraw_amount', 'withdraw_method', 'total_amount', 'withdraw_time', 'site_url', 'site_name', 'user_name', 'instructor_username' ) ),
				),

				'instructor_application_accepted' => array(
					'label'        => __( 'Instructor Application Accepted', 'tutor-pro' ),
					'default'      => 'off',
					'template'     => 'to_instructor_become_application_approved',
					'tooltip'      => __( 'Enable to send instructor application acceptance email notification', 'tutor-pro' ),
					'subject'      => __( 'Congratulations! Your Application to Become an Instructor at {site_name} is Approved!', 'tutor-pro' ),
					'heading'      => __( 'Welcome you on Board', 'tutor-pro' ),
					'message'      => json_encode( 'Welcome Aboard! Your application to become an instructor at {site_url} is approved! Your dashboard is ready and you can start creating courses right away!' ),
					'footer_text'  => __( 'You may reply to this email to communicate with the admin.', 'tutor-pro' ),
					'placeholders' => EmailPlaceholder::only( array( 'dashboard_url', 'site_url', 'site_name', 'user_name', 'instructor_username' ) ),
				),

				'instructor_application_rejected' => array(
					'label'        => __( 'Instructor Application Rejected', 'tutor-pro' ),
					'default'      => 'off',
					'template'     => 'to_instructor_become_application_rejected',
					'tooltip'      => __( 'Enable to send instructor application rejected email notification', 'tutor-pro' ),
					'subject'      => __( 'Feedback on your application to become an instructor on {site_name}', 'tutor-pro' ),
					'heading'      => __( 'Instructor Application Rejected!', 'tutor-pro' ),
					'message'      => json_encode( 'We are sorry to inform you that we are unable to process your application to become an instructor on our website. You are always welcome to try again.' ),
					'footer_text'  => __( 'You may reply to this email to communicate with the admin', 'tutor-pro' ),
					'placeholders' => EmailPlaceholder::only( array( 'site_url', 'site_name', 'user_name', 'instructor_username' ) ),
				),

				'instructor_application_received' => array(
					'label'               => __( 'Instructor Application Received', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_become_application_received',
					'tooltip'             => __( 'Enable to send instructor application received email notification', 'tutor-pro' ),
					'subject'             => __( 'Application Received - {site_name}', 'tutor-pro' ),
					'heading'             => __( 'Received Application', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'instructor_email'    => __( '<EMAIL>', 'tutor-pro' ),
					'message'             => json_encode( 'Application Received! We are happy that you have considered TutorLMS.com to host your courses. Please allow us some time to review your credentials. If we need any clarification on anything, we will reach out to you. Please expect an email from us in a very short time!.' ),
					'footer_text'         => __( 'Reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'site_url', 'site_name', 'user_name', 'instructor_username', 'instructor_email' ) ),
				),

				'instructor_course_publish'       => array(
					'label'               => __( 'Instructor Course Published', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_course_accepted',
					'tooltip'             => __( 'Enable to send instructor course published email notification', 'tutor-pro' ),
					'subject'             => __( 'Congratulations! {course_name} is published at {site_name}', 'tutor-pro' ),
					'heading'             => __( 'Your Course is Published', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'course_name'         => __( 'Course name', 'tutor-pro' ),
					'student_username'    => __( 'John Doe', 'tutor-pro' ),
					'student_email'       => __( '<EMAIL>', 'tutor-pro' ),
					'message'             => json_encode( 'We are glad to inform you that your course is now live on <strong>{site_url}</strong>.' ),
					'footer_text'         => __( 'Reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'site_url', 'site_name', 'user_name', 'course_edit_url', 'course_url', 'course_name' ) ),
				),

				'a_instructor_course_rejected'    => array(
					'label'               => __( 'An Instructor\'s Course Rejected', 'tutor-pro' ),
					'default'             => 'off',
					'template'            => 'to_instructor_course_rejected',
					'tooltip'             => __( 'Enable to send course rejection email notification to instructor', 'tutor-pro' ),
					'subject'             => __( 'Course Rejection Notification', 'tutor-pro' ),
					'heading'             => __( 'Course Rejected by Administration', 'tutor-pro' ),
					'course_title'        => __( 'Course Content Review Request title.', 'tutor-pro' ),
					'instructor_username' => __( 'Instructor', 'tutor-pro' ),
					'message'             => json_encode( 'We regret to inform you that, after careful review, your submitted {course_name} has been rejected by our administration. We understand the effort you put into creating your course and appreciate your commitment to our platform.' ),
					'footer_text'         => __( 'You may reply to this email to communicate with the admin.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'site_url', 'site_name', 'user_name', 'course_edit_url', 'course_url', 'course_name' ) ),
				),
			),
			'email_to_admin'    => array(
				'new_instructor_signup'  => array(
					'label'           => __( 'New Instructor Signup', 'tutor-pro' ),
					'default'         => 'on',
					'template'        => 'to_admin_new_instructor_signup',
					'tooltip'         => __( 'Enable to get new instructor signup email notification', 'tutor-pro' ),
					'instructor_name' => __( 'Instructor', 'tutor-pro' ),
					'subject'         => __( 'New Instructor Signed up at {site_url}', 'tutor-pro' ),
					'heading'         => __( 'New Instructor Sign Up', 'tutor-pro' ),
					'message'         => json_encode( 'A new instructor has signed up on <strong>{site_url}</strong>.' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'site_url', 'site_name', 'instructor_name', 'review_url', 'instructor_email', 'signup_time' ) ),
				),

				'new_student_signup'     => array(
					'label'           => __( 'New Student Signup', 'tutor-pro' ),
					'default'         => 'on',
					'template'        => 'to_admin_new_student_signup',
					'tooltip'         => __( 'Enable to get new student signup email notification', 'tutor-pro' ),
					'instructor_name' => __( 'Instructor', 'tutor-pro' ),
					'student_name'    => __( 'Student', 'tutor-pro' ),
					'subject'         => __( 'New Student Signed up at {site_url}', 'tutor-pro' ),
					'heading'         => __( 'New Student Sign Up', 'tutor-pro' ),
					'message'         => json_encode( 'A new student has signed up to your site <strong>{site_url}</strong>' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the student.', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'site_url', 'site_name', 'student_name', 'profile_url', 'student_email', 'signup_time' ) ),
				),

				'new_course_submitted'   => array(
					'label'           => __( 'New Course Submitted for Review', 'tutor-pro' ),
					'default'         => 'on',
					'template'        => 'to_admin_new_course_submitted_for_review',
					'tooltip'         => __( 'Enable to get course review email notification', 'tutor-pro' ),
					'subject'         => __( '{instructor_name} Submitted a New Course For Review', 'tutor-pro' ),
					'heading'         => __( 'New Course Submitted for Review', 'tutor-pro' ),
					'course_name'     => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'instructor_name' => __( 'Instructor', 'tutor-pro' ),
					'message'         => json_encode( 'A new course has been created by <strong>{instructor_name}</strong> on your site  <strong>{site_url}</strong> and is waiting for approval.' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the instructor', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'site_url', 'site_name', 'course_name', 'course_url', 'course_edit_url', 'instructor_name', 'submitted_time' ) ),
				),

				'new_course_published'   => array(
					'label'           => __( 'New Course Published', 'tutor-pro' ),
					'default'         => 'on',
					'template'        => 'to_admin_new_course_published',
					'tooltip'         => __( 'Enable to get new course published email notification', 'tutor-pro' ),
					'subject'         => __( '{instructor_name} Published a new course {course_name}', 'tutor-pro' ),
					'heading'         => __( 'New Course Published', 'tutor-pro' ),
					'course_name'     => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'instructor_name' => __( 'Jhon Doe', 'tutor-pro' ),
					'message'         => json_encode( 'The instructor - <strong>{instructor_name}</strong> has published a new course on your site <strong>{site_url}</strong>.' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'    => EmailPlaceholder::only( array( 'site_url', 'site_name', 'course_name', 'course_url', 'course_edit_url', 'instructor_name', 'published_time' ) ),
				),

				'course_updated'         => array(
					'label'           => __( 'Course Edited/Updated', 'tutor-pro' ),
					'default'         => 'on',
					'template'        => 'to_admin_course_updated',
					'tooltip'         => __( 'Enable to get course edit or update email notification', 'tutor-pro' ),
					'subject'         => __( '{course_name} Updated by {instructor_name}', 'tutor-pro' ),
					'heading'         => __( 'Course Updated', 'tutor-pro' ),
					'course_name'     => __( 'Mastering WordPress-From Beginner to Advance', 'tutor-pro' ),
					'student_name'    => __( 'James Andy', 'tutor-pro' ),
					'instructor_name' => __( 'Jhon Doe', 'tutor-pro' ),
					'student_email'   => __( '<EMAIL>', 'tutor-pro' ),
					'footer_text'     => __( 'You may reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'message'         => json_encode( 'The Instructor - <strong>{instructor_name}</strong> has updated a course on <strong>{site_url}</strong>.' ),
					'placeholders'    => EmailPlaceholder::only( array( 'site_url', 'site_title', 'course_name', 'course_url', 'instructor_name', 'updated_time' ) ),
				),

				'new_withdrawal_request' => array(
					'label'               => __( 'New Withdrawal Request', 'tutor-pro' ),
					'default'             => 'on',
					'template'            => 'to_admin_new_withdrawal_request',
					'tooltip'             => __( 'Enable to get new withdrawal request email notification', 'tutor-pro' ),
					'subject'             => __( 'New withdrawal request from {instructor_username} for {withdraw_amount}', 'tutor-pro' ),
					'withdraw_amount'     => __( '20 USD', 'tutor-pro' ),
					'instructor_username' => __( 'Jhon Doe', 'tutor-pro' ),
					'instructor_email'    => __( 'instructor@{site_url}', 'tutor-pro' ),
					'heading'             => __( 'New Withdrawal Request', 'tutor-pro' ),
					'message'             => json_encode( 'Instructor <strong>{instructor_username}</strong>  has sent a withdrawal request  to your site <strong>{site_url}</strong>' ),
					'footer_text'         => __( 'You may reply to this email to communicate with the instructor.', 'tutor-pro' ),
					'placeholders'        => EmailPlaceholder::only( array( 'site_url', 'site_name', 'instructor_username', 'instructor_email', 'withdraw_amount', 'withdraw_method_name', 'request_time', 'approved_url', 'rejected_url' ) ),
				),
			),
		);

		return apply_filters( 'tutor_pro/email/list', $email_array );
	}
}

