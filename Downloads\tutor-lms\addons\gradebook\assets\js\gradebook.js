(()=>{var t={};function e(t){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function e(){return t};var t={},n=Object.prototype,o=n.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function f(t,e,r,n){var o=e&&e.prototype instanceof p?e:p,i=Object.create(o.prototype),u=new O(n||[]);return a(i,"_invoke",{value:_(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function p(){}function v(){}function m(){}var y={};l(y,u,(function(){return this}));var g=Object.getPrototypeOf,w=g&&g(g(S([])));w&&w!==n&&o.call(w,u)&&(y=w);var b=m.prototype=p.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,r){function n(a,i,u,c){var s=d(t[a],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==e(f)&&o.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(f).then((function(t){l.value=t,u(l)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function t(e,o){function a(){return new r((function(t,r){n(e,o,t,r)}))}return i=i?i.then(a,a):a()}})}function _(t,e,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return P()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var u=E(i,r);if(u){if(u===h)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=d(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=d(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var a=o.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function S(t){if(t){var e=t[u];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(o.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=undefined,e.done=!0,e};return n.next=n}}return{next:P}}function P(){return{value:undefined,done:!0}}return v.prototype=m,a(b,"constructor",{value:m,configurable:!0}),a(m,"constructor",{value:v,configurable:!0}),v.displayName=l(m,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(L.prototype),l(L.prototype,c,(function(){return this})),t.AsyncIterator=L,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new L(f(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(b),l(b,s,"Generator"),l(b,u,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=S,O.prototype={constructor:O,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var r=this;function n(t,n){return u.type="throw",u.arg=e,r.next=t,n&&(r.method="next",r.arg=undefined),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),s=o.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function t(e,r){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),h},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),j(n),h}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;j(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:S(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),h}},t}function n(t,e,r,n,o,a,i){try{var u=t[a](i);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,o)}}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,a){var i=t.apply(e,r);function u(t){n(i,o,a,u,c,"next",t)}function c(t){n(i,o,a,u,c,"throw",t)}u(undefined)}))}}jQuery(document).ready((function(t){"use strict";var e=wp.i18n,n=e.__,a=e._x,i=e._n,u=e._nx;t(document).on("click",".tutor-gradebook-filter",(function(e){var r="",n=window.location.href,o=t(".tutor-gradebook-filter-select option").filter(":selected").val();if(n.includes("courseid")){var a=new URL(n);a.searchParams.set("courseid",o);r=a.toString()}else{r=n+"&courseid="+o}window.location.href=r}));t(document).on("submit","#tutor-add-new-gradebook-form",function(){var e=o(r().mark((function e(o){var a,i,u,c,l,f,d,h;return r().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:o.preventDefault();a=t(this);i=new FormData(document.getElementById("tutor-add-new-gradebook-form"));u=t("#tutor-add-new-grad-form-response");r.next=6;return s(i,o.target);case 6:c=r.sent;r.next=9;return c.json();case 9:l=r.sent;if(!l.success){u.html("");for(f=0,d=Object.values(l.data);f<d.length;f++){h=d[f];u.append("<div><li class='tutor-alert tutor-alert-warning'>".concat(h,"</li></div>"))}}else{a.trigger("reset");tutor_toast(n("Success","tutor-pro"),n("New Grade Added","tutor-pro"),"success");location.reload()}case 11:case"end":return r.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}());var c="#tutor-update-gradebook-form";t(document).on("click",".tutor-open-grade-update-modal",(function(e){var r=e.target;t("".concat(c," input[name=gradebook_id]")).val(r.dataset.id);t("".concat(c," input[name=grade_name]")).val(r.dataset.name);t("".concat(c," input[name=grade_point]")).val(r.dataset.point);t("".concat(c," input[name=percent_to]")).val(r.dataset.maximum);t("".concat(c," input[name=percent_from]")).val(r.dataset.minimum);document.getElementById("tutor-update-grade-color").value=r.dataset.color;t("".concat(c," .button.wp-color-result")).css("background-color",r.dataset.color)}));t(c).on("submit",function(){var e=o(r().mark((function e(o){var a,i,u,c,l,f,d,h,p;return r().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:o.preventDefault();a=new FormData(document.getElementById("tutor-update-gradebook-form"));i=t("#tutor-update-gradebook-form .grade-percent-to").val();u=t("#tutor-update-gradebook-form .grade-percent-from").val();i=Number(i);u=Number(u);if(!(u>i||i<u)){r.next=9;break}tutor_toast(n("Operation failed","tutor-pro"),n("Minimum Percentile must be lower then the Maximum Percentile","tutor-pro"),"error");return r.abrupt("return");case 9:c=document.getElementById("tutor-update-grade-form-response");r.next=12;return s(a,o.target);case 12:l=r.sent;r.next=15;return l.json();case 15:f=r.sent;if(!f.success){if(f.data){for(d=0,h=Object.values(f.data);d<h.length;d++){p=h[d];c.innerHTML+="<div><li class='tutor-alert tutor-alert-warning'>".concat(p,"</li></div>")}}}else{tutor_toast(n("Success","tutor-pro"),n("Grade updated","tutor-pro"),"success");location.reload()}case 17:case"end":return r.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());function s(t,e){return l.apply(this,arguments)}function l(){l=o(r().mark((function t(e,o){var a,i;return r().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;a=o.querySelector("[data-tutor-modal-submit]");a.classList.add("is-loading");r.next=5;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:e});case 5:i=r.sent;a.classList.remove("is-loading");return r.abrupt("return",i);case 10:r.prev=10;r.t0=r["catch"](0);submitButton.classList.remove("is-loading");tutor_toast(n("Operation failed","tutor-pro"),r.t0,"error");case 14:case"end":return r.stop()}}),t,null,[[0,10]])})));return l.apply(this,arguments)}t("[data-delete_url]").click((function(e){var r=t(this).data("delete_url");t(this).prop("disabled",true).addClass("is-loading");window.location.href=r}))}))})();