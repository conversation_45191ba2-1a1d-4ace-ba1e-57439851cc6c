(()=>{"use strict";var t={};var e=function t(e,n){var a=wp.i18n.__;var r=e||{},o=r.data,i=o===void 0?{}:o;var c=i.message,d=c===void 0?n||a("Something Went Wrong!","tutor-pro"):c;return d};jQuery(document).ready((function(t){var n=window.navigator.userAgent.match(/Firefox\/([0-9]+)\./);var a=n?parseInt(n[1]):0;var r=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);var o=/Chrome/.test(navigator.userAgent)&&/Google Inc/.test(navigator.vendor);var i=wp.i18n,c=i.__,d=i._x,s=i._n,u=i._nx;t("body").append('<svg id="tutor_svg_font_id" width="0" height="0" style="background-color:white;"></svg>');var l=function e(n,a){var r=new XMLHttpRequest;r.open("get","?tutor_action=get_fonts&course_id="+n);r.responseType="text";r.send();r.onloadend=function(){var e=r.response;var n=e.match(/https?:\/\/[^ \)]+/g);var o=0;n.forEach((function(r){var i=new XMLHttpRequest;i.open("get",r);i.responseType="blob";i.onloadend=function(){var c=new FileReader;c.onloadend=function(){e=e.replace(new RegExp(r),c.result);o++;if(o==n.length){t("#tutor_svg_font_id").prepend("<style>".concat(e,"</style>"));a()}};c.readAsDataURL(i.response)};i.send()}))}};var f=function t(e){var n=document.createElement("canvas");var a=n.getContext("2d");n.width=e.naturalWidth;n.height=e.naturalHeight;a.drawImage(e,0,0);return n.toDataURL("image/png")};var p=function t(e,n){var a=e.getElementsByTagName("img");var r=0;function o(){if(r>=a.length){n();return}a[r].onload=o;a[r].src=f(a[r]);r=r+1}o()};var v=function n(i,d,s){var u=this;this.view=function(t){window.location.assign(s)};this.download=function(t,e,n){var a=new jspdf.jsPDF(e>n?"l":"p","px",[e,n]);a.addImage(t,"jpeg",0,0,e,n);a.save("certificate-"+(new Date).getTime()+".pdf")};this.reload=function(){window.location.reload()};this.dataURItoBlob=function(t,e){var n=atob(t.split(",")[1]);var a=new ArrayBuffer(n.length);var r=new Uint8Array(a);for(var o=0;o<n.length;o++){r[o]=n.charCodeAt(o)}return new Blob([a],{type:e})};this.store_certificate=function(e,n){var a=tutor_get_nonce_data(true);var r=new FormData;r.append("action","tutor_store_certificate_image");r.append("cert_hash",d);r.append("certificate_image",u.dataURItoBlob(e,"image/jpeg"),"certificate.jpg");r.append(a.key,a.value);t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:r,processData:false,contentType:false,success:function t(e){var a=(e.data||{}).message;n(e&&e.success,a)},error:function t(){n(false)}})};this.dispatch_conversion_methods=function(t,e,n){var a=e.getElementsByTagName("body")[0];var r=e.getElementById("watermark");var i=r.offsetWidth;var d=r.offsetHeight;a.style.display="inline-block";a.style.overflow="hidden";a.style.width=i+"px";a.style.height=d+"px";a.style.padding="0px";a.style.margin="0px";var s=e.getElementsByTagName("body")[0];var l={scale:3,letterRendering:true,logging:true,foreignObjectRendering:o,allowTaint:true,useCORS:true,x:0,y:0,width:i,height:d,windowWidth:i,windowHeight:d};p(e,(function(){html2canvas(s,l).then((function(e){var a=e.toDataURL("image/jpeg",1);u.store_certificate(a,(function(r,o){console.log("Store cert image");!r?alert(o||c("Something Went Wrong","tutor-pro")):0;r&&typeof u[t]=="function"?u[t](a,e.width,e.height):0;typeof n=="function"?n(r):0}))}))}))};this.load_certificate_builder=function(t){var e=document.createElement("iframe");e.width="1920";e.height="1080";e.style.position="absolute";e.style.left="-999999px";e.src=t;document.getElementsByTagName("body")[0].appendChild(e)};this.init_render_certificate=function(n,o){var s={action:"tutor_generate_course_certificate",course_id:i,certificate_hash:d||"",format:n=="download"?"pdf":"jpg"};t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:s,success:function d(s){if(!s.success){tutor_toast(c("Error","tutor-pro"),e(s),"error");return}var f=s.data.certificate_builder_url;if(f){window.tutor_certificate_after_build=function(){return o(true)};u.load_certificate_builder(f);return}var p=s.success?s.data.html:"";var v=document.createElement("iframe");var g=function e(n){n.write(p);n.write(t("<div></div>").append(t("#tutor_svg_font_id").clone()).html());if(a){var r=window.document.createElement("style");r.innerHTML="*{word-spacing:3px !important; letter-spacing:2px !important;}";n.getElementsByTagName("head")[0].appendChild(r)}};if(a||r){v.addEventListener("load",(function(){var t=v.contentWindow||v.contentDocument.document||v.contentDocument;t=t.document;g(t);l(i,(function(){return u.dispatch_conversion_methods(n,t,o)}))}))}else{l(i,(function(){var t=v.contentWindow||v.contentDocument.document||v.contentDocument;t=t.document;t.open();g(t);t.close();v.onload=function(){return u.dispatch_conversion_methods(n,t,o)}}))}v.style.position="absolute";v.style.left="-999999px";document.getElementsByTagName("body")[0].appendChild(v)}})}};var g=t(".tutor-certificate-pdf");var h=g.data("course_id");var m=g.data("cert_hash");var w=g.data("href");var _=new v(h,m,w);g.click((function(e){e.preventDefault();var n=t(this);n.closest("button").addClass("is-loading");_.init_render_certificate("download",(function(){n.closest("button").removeClass("is-loading")}))}));var y=t("#tutor-pro-certificate-download-image");y.click((function(e){e.preventDefault();var n=t("#tutor-pro-certificate-preview");var a=document.createElement("A");a.href=n.attr("src");a.download="certificate-"+(new Date).getTime()+".jpg";document.body.appendChild(a);a.click();document.body.removeChild(a)}));var b=new URL(window.location.href).searchParams.get("regenerate")==1;if(b||t("#tutor-pro-certificate-preview").data("is_generated")=="no"){_.init_render_certificate("",(function(){window.location.replace(t("#tutor-pro-certificate-preview").data("certificate_url"))}))}this.PrintDiv=function(){var t=document.querySelector("#div-to-print");var e=window.open("","_blank","width=800,height=500");e.document.open();e.document.write('<html><body onload="window.print()">'+t.innerHTML+"</html>");e.document.close()}}))})();