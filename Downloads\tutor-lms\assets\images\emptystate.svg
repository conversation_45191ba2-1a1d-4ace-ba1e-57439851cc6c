<svg width="824" height="242" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M0 241.266s369.993-203.2255 824-.957l-824 .957Z" fill="url(#a)"/>
  <path d="M281.32 167.026v20.072" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M291.09 156.888c-6.818-23.128-9.617-25.522-9.617-25.522s-2.137 2.547-9.567 25.522c-6.513 20.021 4.987 20.123 8.091 19.817-.051 0 17.708 2.649 11.093-19.817Z" fill="#E5E7EA"/>
  <path d="M502.926 152.1v20.785" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M513.002 141.605c-7.074-23.943-9.974-26.388-9.974-26.388s-2.188 2.649-9.923 26.388c-6.717 20.734 5.191 20.836 8.346 20.53.05 0 18.42 2.7 11.551-20.53Z" fill="#E5E7EA"/>
  <path d="M534.322 167.077v15.793" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M542.006 159.13c-5.394-18.187-7.531-20.072-7.531-20.072s-1.679 1.987-7.531 20.072c-5.089 15.69 3.918 15.792 6.361 15.588-.051 0 13.891 2.038 8.701-15.588Z" fill="#E5E7EA"/>
  <path d="m349.761 45.1695 100.397-8.9151s24.272 1.1717 26.868 31.0754c2.595 29.9038 5.037 47.3262 5.037 47.3262l-92.153 11.106-5.954-50.3322c-.763-6.5208-3.256-12.6849-7.327-17.8302-4.987-6.266-13.281-12.7358-26.868-12.4301Z" fill="#E3E5EA"/>
  <path d="m392.963 125.202-61.928 7.03-6.259-52.2679c-2.086-17.4226 10.534-33.1133 27.987-34.8453 16.894-1.6302 32.007 10.5453 33.992 27.4585l6.208 52.6247ZM404.616 123.469v18.391h16.385l-.204-19.868-16.181 1.477Z" fill="#C1C3CA"/>
  <path d="m330.374 132.792-7.887 26.185c-1.221 3.617-1.73 7.438-1.476 11.258.509 7.183 3.918 16.251 18.064 15.895 8.753-.255 16.182-3.515 22.085-7.438 8.243-5.502 14.604-13.347 18.573-22.415l13.739-30.464-63.098 6.979Z" fill="#D0D2D6"/>
  <path d="M422.324 140.994h-18.777v43.149h18.777v-43.149Z" fill="#CDCFD4"/>
  <path d="m381.768 60.045-54.447 39.0227M382.989 60.8607l-51.75 70.6073M360.396 128.666l22.441-65.2582M350.27 82.7662s11.042-3.0058 10.991 6.2659c0 0 10.635-2.3942 10.025 8.253M335.615 93.5153s13.994-3.0056 14.248 11.6657c0 0 11.195-4.126 15.52 7.744" stroke="#CDCFD4" stroke-width="3" stroke-miterlimit="10"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="m273.789 22.3978-.814-.051v1.3246l.814.0509v-1.3245ZM273.942 20.0038l-.814-.0509-.102 1.3244.814.051.102-1.3245ZM274.044 17.6093l-.814-.0508-.051 1.3244.814.051.051-1.3246ZM273.84 29.5302l-.814.0509c.051.4584.051.917.102 1.3245l.814-.051c-.102-.4075-.102-.8659-.102-1.3244ZM273.738 27.1358h-.814c0 .4584 0 .917.051 1.3245h.814c0-.4075-.051-.8661-.051-1.3245ZM273.738 24.7925h-.814v1.3245h.814v-1.3245ZM274.705 36.611l-.763.2037.305 1.3246.763-.2037-.305-1.3246ZM274.298 34.2678l-.814.102c.051.4585.153.9169.203 1.3244l.815-.1528c-.102-.3566-.153-.8151-.204-1.2736ZM273.993 31.9246l-.814.1019c.051.4585.101.917.152 1.3245l.815-.1019c-.051-.4585-.102-.866-.153-1.3245ZM276.995 43.3356l-.712.3566c.203.4076.407.8152.61 1.2227l.713-.4075c-.255-.3566-.407-.7642-.611-1.1718ZM276.079 41.1959l-.763.2547.509 1.2736.712-.3057-.458-1.2226ZM275.316 38.9547l-.763.2547c.102.4585.254.8661.407 1.2736l.763-.2547c-.153-.4585-.305-.8661-.407-1.2736ZM280.863 49.3468l-.662.5095.916 1.0188.611-.5604-.865-.9679ZM279.387 47.4623l-.662.4585c.255.3566.56.7132.814 1.0698l.662-.5094c-.255-.3057-.56-.6623-.814-1.0189ZM278.115 45.4754l-.713.4075c.204.4076.458.7642.713 1.1718l.661-.4585c-.203-.3566-.458-.7642-.661-1.1208ZM286.002 54.2377l-.509.6622c.356.3056.763.5094 1.12.7641l.458-.6621c-.357-.2548-.713-.5095-1.069-.7642ZM284.17 52.7094l-.56.6113c.357.3057.662.6113 1.018.866l.509-.6113c-.305-.2547-.662-.5603-.967-.866ZM282.44 51.0791l-.611.5604c.305.3057.611.6622.967.9679l.56-.6113c-.306-.2547-.611-.6113-.916-.917ZM292.159 57.7525l-.305.7641c.407.1529.814.3566 1.272.5095l.305-.7642c-.458-.1528-.865-.3056-1.272-.5094ZM290.022 56.7336l-.407.7642 1.221.6113.356-.7642-1.17-.6113ZM287.986 55.562l-.458.7133 1.171.6622.407-.6622-1.12-.7133ZM298.978 59.7395l-.102.8151c.458.051.916.1529 1.374.1529l.051-.8152c-.458-.0509-.916-.1019-1.323-.1528ZM296.637 59.2808l-.203.8152 1.373.2547.153-.8152-1.323-.2547ZM294.398 58.6186l-.254.7642c.407.1528.865.2547 1.272.4075l.203-.7641c-.407-.1528-.814-.2547-1.221-.4076ZM306 59.5355l.153.8152c.458-.1019.916-.2038 1.323-.3057l-.204-.7642c-.407.051-.814.1529-1.272.2547ZM303.659 59.8923l.051.8152c.458-.051.916-.1019 1.374-.1529l-.102-.8151c-.407.0509-.865.1019-1.323.1528ZM301.318 59.9435v.815c.458 0 .916.0509 1.374 0v-.815c-.458.0509-.916 0-1.374 0ZM312.36 56.6318l.509.6113c.356-.3057.662-.6622 1.018-.9679l-.611-.5604c-.305.3056-.61.6113-.916.917ZM310.427 57.9565l.407.7133c.407-.2548.763-.5096 1.17-.7133l-.458-.6622c-.407.2038-.763.4584-1.119.6622ZM308.29 58.9246l.254.7641c.407-.1528.865-.3566 1.272-.5094l-.356-.7642c-.356.1529-.763.3566-1.17.5095ZM315.566 50.6717l.814.0508c.051-.4585 0-.9679-.051-1.4264l-.814.102c.102.4075.102.866.051 1.2736ZM315.058 52.9131l.763.3057c.203-.4585.305-.9171.407-1.3755l-.814-.1528c-.051.4076-.204.8151-.356 1.2226ZM313.938 54.8997l.662.4586c.254-.4075.508-.7642.763-1.1717l-.713-.4076c-.254.4076-.458.7641-.712 1.1207ZM311.953 45.6282l.204-.815c-.458-.1019-.916-.2038-1.425-.2548l-.051.8151c.458.1018.865.1528 1.272.2547ZM313.989 46.6468l.508-.6113c-.407-.3056-.814-.5604-1.272-.7641l-.356.7641c.407.1528.763.3566 1.12.6113ZM315.312 48.4301l.763-.3056c-.153-.4585-.407-.917-.712-1.2736l-.611.5095c.203.3056.407.6622.56 1.0697ZM305.287 46.8508l-.407-.7133c-.203.1019-.407.2547-.559.4076-.204.1528-.408.2547-.56.4075l.509.6623c.152-.1528.356-.2547.508-.4075.153-.1529.306-.2547.509-.3566ZM307.425 45.8321l-.255-.7642c-.254.051-.458.1528-.661.2547-.204.1019-.407.2037-.662.3056l.356.7133c.204-.1019.407-.2038.611-.2547.204-.1019.356-.2038.611-.2547ZM309.664 45.4244l-.051-.8152c-.255 0-.458.051-.713.051-.254 0-.458.0509-.712.1019l.153.8151c.203-.0509.407-.102.61-.102.204 0 .509-.0508.713-.0508ZM300.758 52.047l-.763-.3057c-.203.4076-.356.866-.509 1.2736l.764.2547c.203-.4075.356-.815.508-1.2226ZM301.929 50.0094l-.662-.4586c-.152.2038-.254.4076-.407.5605-.102.2037-.254.4075-.356.6113l.712.4075c.102-.2038.204-.3566.357-.5604.101-.2038.203-.3565.356-.5603ZM303.456 48.2771l-.56-.6113c-.153.1529-.356.3057-.509.5095-.152.1528-.305.3566-.458.5094l.611.5094c.152-.1528.254-.3565.407-.4584.203-.1528.356-.3057.509-.4586Z" fill="#C1C3CA"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="m299.588 58.9753-.814.051c0 .4585.051.917.102 1.3755l.814-.1019c-.051-.4585-.102-.917-.102-1.3246ZM299.639 56.5808l-.814-.1019c-.051.4585-.051.917-.102 1.3754h.814c.051-.4075.051-.8151.102-1.2735ZM299.995 54.2887l-.763-.2037c-.102.4585-.204.9169-.254 1.3754l.814.1019c.051-.4075.101-.866.203-1.2736ZM300.962 65.9034l-.763.2547c.152.4076.305.8661.458 1.2736l.763-.3057c-.153-.4075-.305-.815-.458-1.2226ZM300.25 63.611l-.764.2037c.102.4585.255.8661.357 1.3246l.763-.2548c-.102-.4075-.255-.815-.356-1.2735ZM299.792 61.3185l-.814.1019c.05.4585.152.917.254 1.3246l.814-.1529c-.102-.4075-.203-.8151-.254-1.2736ZM304.168 72.2206l-.662.4586.764 1.1207.661-.5094-.763-1.0699ZM302.947 70.1829l-.713.4076.662 1.1717.712-.4076-.661-1.1717ZM301.878 68.0944l-.763.3566c.203.4075.356.815.559 1.2226l.713-.3566-.509-1.2226ZM308.849 77.5696l-.559.6115 1.017.9168.509-.6113-.967-.917ZM307.17 75.9394l-.61.5604.916.9679.61-.6113-.916-.917ZM305.593 74.1564l-.611.5094.865 1.0189.611-.5095-.865-1.0188ZM314.599 81.7471l-.407.7132 1.171.6623.407-.7133-1.171-.6622ZM312.564 80.5243l-.458.6623 1.17.7641.407-.7131-1.119-.7133ZM310.681 79.1487l-.509.6113 1.069.8151.458-.6622-1.018-.7642ZM321.113 84.4978l-.204.8151 1.323.3566.153-.8152-1.272-.3565ZM318.874 83.7849l-.254.7641 1.272.4584.254-.8151-1.272-.4074ZM316.686 82.868l-.306.7131 1.222.5605.305-.7642-1.221-.5094ZM281.371 8.49081s-.712 4.17739 3.257 4.83959c0 0 2.392-1.732 1.272-4.07544-1.068-2.29245-4.529-.76415-4.529-.76415Z" fill="#C1C3CA"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M280.812 8.79593s-.407 4.33027 3.409 4.94157c0 0-1.628 1.834-3.155.7642-1.425-.9679-2.442-2.8019-2.086-4.4831.101-.40751.305-.8151.661-1.06982.306-.10189.713-.25474 1.171-.15285Z" fill="#C1C3CA"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M278.115 10.3243c0-.1018-1.985 2.1397-1.527 3.7699 0 0-1.17.6113-1.119 1.3245 0 0 .865-.866 1.628-.4585 0 0 1.527 1.7321 3.918.4585.051-.051-2.697-1.5793-2.9-5.0944ZM281.117 7.77717c-1.221-.96792-2.137-2.2924-2.697-3.76976-.153-.35661-.254-.71318-.254-1.12073-.051-.61132.152-1.22264.509-1.78302.254-.407546.508-.764224.966-.967998.865-.407547 1.985.15283 2.443 1.018868.458.86604.407 1.88495.254 2.85288-.152 1.12075-.458 2.2925-.865 3.36231-.102.20378-.203.40745-.356.40745-.153.05094-.305-.10184-.458-.20372-1.425-1.47736-3.358-2.29241-5.241-3.00561-.407-.15283-.865-.30576-1.272-.35671-.509 0-.967.15293-1.425.35671-.611.30566-1.17.76405-1.425 1.42631-.254.66226-.102 1.42647.407 1.83401.306.25472.713.30569 1.12.40757 2.697.4585 5.444.35663 8.091-.35657" fill="#C1C3CA"/>
  <path d="m392.352 124.998-61.266 6.979-6.208-51.7582c-2.086-17.2189 10.432-32.8076 27.682-34.4378 16.741-1.6302 31.701 10.4434 33.635 27.1529l6.157 52.0641Z" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M457.995 24.5374 474.329 36.56c.661.4584.458 1.5283-.356 1.7321l-6.565 1.5283.56 6.8774c.051.866-.916 1.3754-1.577.815l-16.487-14.4169 8.091-8.5585Z" fill="#3E64DE"/>
  <path d="M443.492 60.0443c1.996 0 3.613-1.6422 3.613-3.6679 0-2.0258-1.617-3.6679-3.613-3.6679-1.995 0-3.613 1.6421-3.613 3.6679 0 2.0257 1.618 3.6679 3.613 3.6679Z" fill="#949BA9"/>
  <path d="M444.815 54.5424c.611-3.0566 1.222-6.1132 1.832-9.1698.407-2.1397.865-4.2793 1.272-6.4698.204-.917.713-2.2925.611-3.2095.051.6623-.814 1.019.102.2039.509-.4585 1.017-1.0189 1.475-1.5283 1.527-1.5283 3.054-3.1076 4.529-4.6359 2.188-2.2415 4.427-4.534 6.615-6.7755 1.629-1.6302-.916-4.1773-2.493-2.4962-2.341 2.3943-4.631 4.7377-6.971 7.1321-1.476 1.5283-2.952 3.0565-4.478 4.5339-.713.7132-1.73 1.4774-2.188 2.4453-.407.917-.458 2.1396-.662 3.0566-.407 2.0887-.814 4.2282-1.272 6.3169-.661 3.2095-1.272 6.4698-1.934 9.6793-.305 2.1905 3.155 3.1585 3.562.917Z" fill="#949BA9"/>
  <path d="M558.035 22.0415c2.952 0 2.952-4.5849 0-4.5849-2.951 0-2.951 4.5849 0 4.5849Z" fill="url(#b)"/>
  <defs>
    <linearGradient id="a" x1="406.358" y1="69.2193" x2="408.386" y2="355.63" gradientUnits="userSpaceOnUse">
      <stop offset=".1653" stop-color="#E3E5EA"/>
      <stop offset=".3741" stop-color="#F4F5F7" stop-opacity=".6199"/>
      <stop offset=".5962" stop-color="#F6F7F8" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="b" x1="558.006" y1="22.0271" x2="558.065" y2="17.4425" gradientUnits="userSpaceOnUse">
      <stop offset=".1653" stop-color="#E3E5EA"/>
      <stop offset=".2826" stop-color="#EDEFF1" stop-opacity=".8497"/>
      <stop offset=".4662" stop-color="#F4F5F7" stop-opacity=".6143"/>
      <stop offset=".9455" stop-color="#F6F7F8" stop-opacity="0"/>
    </linearGradient>
  </defs>
</svg>
