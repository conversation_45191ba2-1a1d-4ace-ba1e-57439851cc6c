(()=>{"use strict";var e={948:(e,t,r)=>{var o=r(449);document.addEventListener("DOMContentLoaded",(function(){var e=document.querySelector("#tutor-email-settings-form")||document.querySelector("#tutor-mailer-form");if(e){var t;var r=e.querySelectorAll("input");r.forEach((function(e){e.addEventListener("input",(function(e){var t=e.target,r=t.name,n=t.value;var a=document.querySelector(".template-preview iframe");if(a){var i=a.contentWindow.document;var c=i.querySelector(".tutor-email-logo img");var l=i.querySelector(".tutor-email-header");var u=i.querySelector(".tutor-email-content");var s=i.querySelector(".tutor-email-footer");var d=i.querySelector(".tutor-email-footer-text");var f=i.querySelector(".tutor-email-heading");var p=i.querySelector(".tutor-email-buttons");var m=i.querySelector(".tutor-email-button");var v=i.querySelector(".tutor-email-button-bordered");switch(r){case"email_logo_position":(0,o.Kd)(c.parentElement,"text-align",n);break;case"email_logo_height":(0,o.Kd)(c,"height",n+"px");break;case"email_logo_alt_text":c.setAttribute("alt",n);break;case"email_template_button_position":(0,o.Kd)(p,"text-align",n);break;case"email_template_colors[header_background_color]":(0,o.Kd)(l,"background-color",n);break;case"email_template_colors[header_divider_color]":(0,o.Kd)(l,"border-color",n);break;case"email_template_colors[body_background_color]":(0,o.Kd)(u,"background-color",n);(0,o.Kd)(s,"background-color",n);break;case"email_template_colors[email_title_color]":(0,o.Kd)(f,"color",n);break;case"email_template_colors[email_text_color]":(0,o.Kd)(u,"color",n);break;case"email_template_colors[email_short_code_color]":i.querySelectorAll("strong").forEach((function(e){(0,o.Kd)(e,"color",n)}));break;case"email_template_colors[footnote_color]":(0,o.Kd)(d,"color",n);break;case"email_template_colors[primary_button_color]":(0,o.Kd)(m,"--primary-button-color",n);break;case"email_template_colors[primary_button_hover_color]":(0,o.Kd)(m,"--primary-button-hover-color",n);break;case"email_template_colors[primary_button_text_color]":(0,o.Kd)(m,"color",n,"important");break;case"email_template_colors[secondary_button_color]":(0,o.Kd)(v,"--secondary-button-color",n);break;case"email_template_colors[secondary_button_hover_color]":(0,o.Kd)(v,"--secondary-button-hover-color",n);break;case"email_template_colors[secondary_button_text_color]":(0,o.Kd)(v,"color",n,"important");break;default:break}}}))}));(t=document.querySelector(".tutor-thumbnail-uploader"))===null||t===void 0?void 0:t.addEventListener("tutor_settings_media_selected",(function(e){var t=document.querySelector(".template-preview iframe");if(t){var r=t.contentWindow.document;var o=r.querySelector(".tutor-email-logo img");o.setAttribute("src",e.detail.attachment.url)}}));var n=document.querySelector(".tutor-email-colors");var a=document.querySelector(".tutor-color-restore-button");a.addEventListener("click",(function(e){var t=JSON.parse(e.target.dataset.defaults);var r=n.querySelectorAll("input[type=color]");r.forEach((function(e){e.value=t[e.dataset.picker];e.dispatchEvent(new Event("input"))}))}));jQuery(document).on("input","input.email-logo-url",(function(){var e=jQuery(this),t=e.val(),r=e.parent().parent().find(".thumbnail-preview img").eq(0);if(r){r.attr("src",t)}}))}jQuery(".tutor-card-collapse-button").click((function(e){if(jQuery(this).find("i").hasClass("tutor-icon-angle-up")){jQuery(this).find("i").removeClass("tutor-icon-angle-up").addClass("tutor-icon-angle-down")}else{jQuery(this).find("i").removeClass("tutor-icon-angle-down").addClass("tutor-icon-angle-up")}jQuery(e.target.closest(".tutor-card")).find(".tutor-card-body").slideToggle()}))}))},333:(e,t,r)=>{var o=r(449);window.addEventListener("DOMContentLoaded",(function(){var e=document.querySelectorAll(".tutor-option-field-input.tutor-email-placeholders");e.forEach((function(e){var t=e.dataset.placeholders;var r=document.createElement("div");r.setAttribute("class","tutor-dropdown-parent");r.innerHTML=(0,o.RW)(t?JSON.parse(t):window._tutorEmailPlaceholders);var n=e.classList.contains("tutor-has-tinymce-editor");e.insertBefore(r,n?e.firstChild:e.lastChild)}));var t=document.querySelectorAll('.tutor-option-field-input.tutor-email-placeholders input[type="text"], .tutor-option-field-input.tutor-email-placeholders textarea');t.forEach((function(e){e.addEventListener("input",(function(e){var t=e.target.parentNode.querySelector(".tutor-dropdown-parent");if(t){if(e.data==="{"){e.target.blur();t.classList.add("is-open");t.querySelectorAll(".tutor-dropdown-item")[0].classList.add("is-active");t.querySelectorAll(".tutor-dropdown-item")[0].focus();document.querySelector("body").style.overflow="hidden";setTimeout((function(){t.querySelector(".tutor-dropdown").scrollTop=0}),100)}else{t.classList.remove("is-open");document.querySelector("body").style.overflow="auto"}}}))}));var r=document.querySelectorAll(".tutor-option-field-input.tutor-email-placeholders .tutor-dropdown-item");r.forEach((function(e){e.addEventListener("click",(function(t){t.preventDefault();var r=e.getAttribute("data-state-text");r=r.slice(1,r.length);var o=e.closest(".tutor-option-field-input").querySelector(":scope > input, :scope > textarea");if(o){var n=o.selectionStart;var a=n+r.length;o.value=o.value.substring(0,n)+r+o.value.substring(n);o.dispatchEvent(new Event("input",{bubbles:true}));o.setSelectionRange(a,a);o.focus()}else if(tinymce.activeEditor){tinymce.activeEditor.insertContent(r);tinymce.triggerSave()}}))}));var n=0;document.addEventListener("keydown",(function(e){var t=document.querySelector(".tutor-email-placeholders .tutor-dropdown-parent.is-open");if(t){var r=t.querySelectorAll(".tutor-dropdown-item");if(e.key==="ArrowUp"){if(n>0){n-=1;r.forEach((function(e){e.classList.remove("is-active");e.blur()}));r[n].classList.add("is-active");r[n].focus()}}else if(e.key==="ArrowDown"){if(n<r.length-1){n+=1;r.forEach((function(e){e.classList.remove("is-active");e.blur()}));r[n].classList.add("is-active");r[n].focus()}}else if(e.key==="Enter"){var o=document.querySelector(".tutor-email-placeholders .tutor-dropdown-item.is-active");if(o){o.click();n=0;r.forEach((function(e){e.classList.remove("is-active");e.blur()}))}}else{n=0}if(e.key==="Escape"){t.classList.remove("is-open");r.forEach((function(e){e.classList.remove("is-active");e.blur()}));var a=t.closest(".tutor-option-field-input").querySelector(":scope > input, :scope > textarea");if(a){a.focus()}else if(tinymce.activeEditor){tinymce.activeEditor.focus()}}}}));document.querySelectorAll(".tutor-email-placeholders .tutor-dropdown-parent").forEach((function(e){e.addEventListener("tutor_dropdown_closed",(function(e){document.querySelector("body").style.overflow="auto";e.target.querySelectorAll(".tutor-dropdown-item").forEach((function(e){return e.classList.remove("is-active")}))}))}))}));document.addEventListener("readystatechange",(function(){if(typeof tinymce!="undefined"&&tinymce.activeEditor){tinymce.activeEditor.on("input",(function(e){var t=(0,o.fW)(tinymce.activeEditor),r=t.offsetTop,n=t.offsetLeft;var a=document.querySelector(".wp-editor-wrap");var i=a.parentNode.querySelector(".tutor-dropdown-parent");if(i){if(e.data==="{"){var c=document.createElement("input");i.appendChild(c);c.focus();i.removeChild(c);i.classList.add("is-open");i.querySelector(".tutor-dropdown").style.top=r+"px";i.querySelectorAll(".tutor-dropdown-item")[0].classList.add("is-active");i.querySelectorAll(".tutor-dropdown-item")[0].focus();document.querySelector("body").style.overflow="hidden";setTimeout((function(){i.querySelector(".tutor-dropdown").scrollTop=0}),100)}else{i.classList.remove("is-open");document.querySelector("body").style.overflow="auto"}}}))}}))},449:(e,t,r)=>{r.d(t,{Hc:()=>f,Iy:()=>l,Kd:()=>v,Kz:()=>m,MC:()=>u,RW:()=>p,WD:()=>h,fW:()=>y,rX:()=>d,up:()=>s});var o=wp.i18n,n=o.__,a=o._x,i=o._n,c=o._nx;function l(e){var t=e.split(",");var r=/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;for(var o=0;o<t.length;o++){var n=t[o].trim();if(!r.test(n)){return false}}return true}function u(e,t){return new Promise((function(r,o){var a=new FormData(e);a.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);var i=a.get("action");if(i==="save_email_template"){a.append(t.name,t.value)}if(i==="save_email_settings"){var c=a.get("email_from_address");var u=a.get("email_from_name");if(!c||!u){e.reportValidity();return o(n("Sender Email Address and Name are required!","tutor-pro"))}if(!l(c)){e.reportValidity();return o(n("Please provide a valid Email Address","tutor-pro"))}}else{var s=a.get("email-subject")||a.get("email_subject");var d=a.get("email-heading")||a.get("email_heading");if(!s||!d){e.reportValidity();return o(n("Email subject and heading are required!","tutor-pro"))}}var f=new XMLHttpRequest;f.open("POST",_tutorobject.ajaxurl,true);f.send(a);f.onreadystatechange=function(e){if(f.readyState===4){var t=JSON.parse(f.response);if(t.success){r(t.message)}else{o(t.message)}}}}))}function s(e,t){if(e){if(e.src){e.src=t}else{e.innerHTML=t}}}function d(e){var t=document.createElement("iframe");t.width="100%";t.height="800px";t.src="".concat(_tutorobject.home_url,"?page=tutor-email-preview&template=").concat(e);return t}function f(e){var t=e.querySelector(".tutor-email-body");var r=e.querySelector(".tutor-email-footer-text");var o=e.querySelector(".tutor-email-footer-content");var n=e.querySelectorAll("a");t.style.paddingTop="0px";t.style.paddingLeft="0px";t.style.paddingRight="0px";t.style.backgroundColor="transparent";o.style.backgroundColor="transparent";n.forEach((function(e){e.classList.add("preview-only");e.addEventListener("click",(function(e){return e.preventDefault()}))}));if(r){var a=new URLSearchParams(window.location.href);var i=a.get("edit");if(i==="settings"){r.innerText="Example of a no-reply or instructional footnote"}}var c=t.querySelectorAll(".user-avatar");c.forEach((function(e){e.src="http://2.gravatar.com/avatar/bc78e9eeca4abe2043bb671018d504ef?s=96&d=mm&r=g"}))}function p(e){if(!e||e.length===0){return""}return'\t\n        <ul class="tutor-dropdown" style="width: 100%; margin: 0; max-height: 200px; overflow: scroll;">\n            '.concat(e.map((function(e){return'\n                    <li>\n                        <a class="tutor-dropdown-item tutor-d-flex tutor-justify-between" href="#" data-state-text="'.concat(e.placeholder,'">\n                            <span class="tutor-color-muted">').concat(e.placeholder,"</span>\n                            <span>").concat(e.label,"</span>\n                        </a>\n                    </li>\n                ")})).join(""),"\n        </ul>\n    ")}function m(e,t){var r=new FormData;r.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);r.append("action","tutor_manual_email_receiver_count_help_text");r.append("receiver_type",e);if(t){t.forEach((function(e){r.append("course_ids[]",e)}))}return jQuery.ajax({url:_tutorobject.ajaxurl,method:"POST",data:r,processData:false,contentType:false})}function v(e,t,r,o){if(e){e.style.setProperty(t,r,o)}}function y(e){var t=e.selection.getRng();var r=t.getClientRects()[0];if(r){var o={offsetTop:r.top+124,offsetLeft:r.left};return o}return{}}function h(e){var t={subject:"email-subject",heading:"email-heading",message:"email-additional-message",before_button:"email-before-button",footer_text:"email-footer-text"};Object.keys(e).map((function(r){if(r==="message"){var o=tinymce.get(t[r]);if(o){o.setContent(JSON.parse(e[r]));tinymce.triggerSave()}}else{var n=document.querySelector("[name=".concat(t[r],"]"));if(n){n.value=e[r];n.dispatchEvent(new Event("input",{bubbles:true}))}}}))}}};var t={};function r(o){var n=t[o];if(n!==undefined){return n.exports}var a=t[o]={exports:{}};e[o](a,a.exports,r);return a.exports}(()=>{r.d=(e,t)=>{for(var o in t){if(r.o(t,o)&&!r.o(e,o)){Object.defineProperty(e,o,{enumerable:true,get:t[o]})}}}})();(()=>{r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})();var o={};(()=>{var e=r(948);var t=r(333);var o=r(449);function n(e){"@babel/helpers - typeof";return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function t(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function e(t,r,o){return t[r]=o}}function d(e,t,r,n){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),c=new k(n||[]);return o(i,"_invoke",{value:E(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var p={};function m(){}function v(){}function y(){}var h={};s(h,c,(function(){return this}));var g=Object.getPrototypeOf,_=g&&g(g(j([])));_&&_!==t&&r.call(_,c)&&(h=_);var b=y.prototype=m.prototype=Object.create(h);function w(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function a(o,i,c,l){var u=f(e[o],e,i);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==n(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,c,l)}),(function(e){a("throw",e,c,l)})):t.resolve(d).then((function(e){s.value=e,c(s)}),(function(e){return a("throw",e,c,l)}))}l(u.arg)}var i;o(this,"_invoke",{value:function e(r,o){function n(){return new t((function(e,t){a(r,o,e,t)}))}return i=i?i.then(n,n):n()}})}function E(e,t,r){var o="suspendedStart";return function(n,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===n)throw a;return T()}for(r.method=n,r.arg=a;;){var i=r.delegate;if(i){var c=x(i,r);if(c){if(c===p)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===o)throw o="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o="executing";var l=f(e,t,r);if("normal"===l.type){if(o=r.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(o="completed",r.method="throw",r.arg=l.arg)}}}function x(e,t){var r=t.method,o=e.iterator[r];if(undefined===o)return t.delegate=null,"throw"===r&&e.iterator["return"]&&(t.method="return",t.arg=undefined,x(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var n=f(o,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,p;var a=n.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=undefined),t.delegate=null,p):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function q(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(q,this),this.reset(!0)}function j(e){if(e){var t=e[c];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,n=function t(){for(;++o<e.length;)if(r.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=undefined,t.done=!0,t};return n.next=n}}return{next:T}}function T(){return{value:undefined,done:!0}}return v.prototype=y,o(b,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:v,configurable:!0}),v.displayName=s(y,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,s(e,u,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},w(S.prototype),s(S.prototype,l,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,o,n,a){void 0===a&&(a=Promise);var i=new S(d(t,r,o,n),a);return e.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},w(b),s(b,u,"Generator"),s(b,c,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=j,k.prototype={constructor:k,reset:function e(t){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(L),!t)for(var o in this)"t"===o.charAt(0)&&r.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=undefined)},stop:function e(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function e(t){if(this.done)throw t;var o=this;function n(e,r){return c.type="throw",c.arg=t,o.next=e,r&&(o.method="next",o.arg=undefined),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function e(t,o){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=o&&o<=i.finallyLoc&&(i=null);var c=i?i.completion:{};return c.type=t,c.arg=o,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(c)},complete:function e(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),p},finish:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),L(o),p}},catch:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc===t){var n=o.completion;if("throw"===n.type){var a=n.arg;L(o)}return a}}throw new Error("illegal catch attempt")},delegateYield:function e(t,r,o){return this.delegate={iterator:j(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=undefined),p}},e}function i(e,t,r,o,n,a,i){try{var c=e[a](i);var l=c.value}catch(e){r(e);return}if(c.done){t(l)}else{Promise.resolve(l).then(o,n)}}function c(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var a=e.apply(t,r);function c(e){i(a,o,n,c,l,"next",e)}function l(e){i(a,o,n,c,l,"throw",e)}c(undefined)}))}}var l=wp.i18n,u=l.__,s=l._x,d=l._n,f=l._nx,p=l.sprintf;var m=["instructors_of_courses","instructors_except_courses","students_of_courses","students_except_courses","students_completed_courses"];var v=u("Success","tutor-pro");var y=u("Error","tutor-pro");document.addEventListener("DOMContentLoaded",c(a().mark((function e(){var t,r,n,i,l,s,d,f,h,g,_,b,w,S,E,x,q,L,k,j,T;return a().wrap((function e(A){while(1)switch(A.prev=A.next){case 0:t=document.querySelector("#email_template_title");r=document.querySelector("#email_option_data");n=document.querySelector("#tutor-email-template-form");i=document.querySelector("#tutor-email-settings-form");l=document.querySelector("#tutor-mailer-form");if(n){s=n}else if(i){s=i}else if(l){s=l}d=document.querySelector("[action-tutor-email-template-save]");f=document.querySelector("#tutor-btn-test-mail");h=document.querySelector("#tutor-btn-save-draft");g=document.querySelector("#tutor-btn-confirm-manual-mail");_=document.querySelector("#tutor-btn-send-manual-mail");b=document.querySelector(".template-preview");w=b.dataset.email_template;S=(0,o.rX)(w);b.append(S);S.addEventListener("load",(function(e){var t;(t=document.querySelector(".loading-spinner"))===null||t===void 0?void 0:t.remove();var r=e.target.contentWindow.document;(0,o.Hc)(r);var n=document.querySelectorAll(".tutor-email-preview-responsive-buttons button");n.forEach((function(e){e.addEventListener("click",(function(t){t.stopImmediatePropagation();n.forEach((function(e){return e.classList.remove("active")}));e.classList.add("active");if(e.dataset.previewMode==="mobile"){S.width="375px"}else{S.width="100%"}}))}));if(typeof tinymce!=="undefined"&&tinymce.activeEditor){tinymce.activeEditor.on("change",(function(e){var t=r.querySelector("[data-source=".concat(e.target.id,"]"));var o=tinymce.activeEditor.getContent().replace("/<[/]{0,1}div[^>]*>/i","");if(t){t.innerHTML=o}tinymce.triggerSave()}))}var a=document.querySelectorAll('.tutor-email-edit-form input[type="hidden"], .tutor-email-edit-form input[type="text"], .tutor-email-edit-form textarea');a.forEach((function(e){var t=e.name,n=e.value;if(t){var a=r.querySelector("[data-source=".concat(t,"]"));(0,o.up)(a,n);e.addEventListener("input",(function(e){(0,o.up)(a,e.target.value)}))}}));if(l){var i=r.querySelector(".tutor-email-buttons");var c=document.querySelector(".mailer-page input[name=email_action_button]");var u=document.querySelector(".mailer-page input[name=email_action_position]");if(c.value==="off"){i.style.display="none";c.closest(".tutor-card").querySelector(".tutor-card-body").style.display="none"}c.nextElementSibling.addEventListener("change",(function(e){jQuery(e.target.closest(".tutor-card")).find(".tutor-card-body").slideToggle();if(c.value==="on"){i.style.display="block"}else{i.style.display="none"}}));u.addEventListener("input",(function(e){i.style.textAlign=e.target.value}));var s=l.querySelector("input[name=email_footer]");if(s){if(s.value){r.querySelector(".tutor-email-footer").style.display="block"}else{r.querySelector(".tutor-email-footer").style.display="none"}s.addEventListener("input",(function(e){if(e.target.value){r.querySelector(".tutor-email-footer").style.display="block"}else{r.querySelector(".tutor-email-footer").style.display="none"}}))}}}));if(!l){A.next=34;break}E=l.querySelector("select[name=receiver_type]");x=l.querySelector("#tutor-mailer-course-ids");q=l.querySelector(".tutor-receiver-count-message");if(!(E&&x)){A.next=34;break}jQuery(x).select2();if(x.dataset.selected){jQuery(x).val(JSON.parse(x.dataset.selected)).trigger("change")}if(!m.includes(E.value)){x.parentElement.style.display="none"}if(!E.value){A.next=32;break}L=[];k=jQuery(x).select2("data");if(k.length){L=k.map((function(e){return e.id}))}A.next=30;return(0,o.Kz)(E.value,L);case 30:j=A.sent;if(j.success&&q){q.style.setProperty("display","flex","important");q.querySelector("div").innerText=j.data.message;document.querySelector(".tutor-email-modal-title strong").innerText=j.data.count}case 32:E.addEventListener("change",function(){var e=c(a().mark((function e(t){var r,n;return a().wrap((function e(a){while(1)switch(a.prev=a.next){case 0:r=t.target.value;if(m.includes(r)){x.parentElement.style.display="block"}else{x.parentElement.style.display="none"}if(jQuery(x).select2("data").length){jQuery(x).val(null).trigger("change")}if(m.includes(E.value)){a.next=8;break}a.next=6;return(0,o.Kz)(r);case 6:n=a.sent;if(n.success&&q){q.style.setProperty("display","flex","important");q.querySelector("div").innerText=n.data.message;document.querySelector(".tutor-email-modal-title strong").innerText=n.data.count}case 8:case"end":return a.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());jQuery(x).on("change",function(){var e=c(a().mark((function e(t){var r,n,i;return a().wrap((function e(t){while(1)switch(t.prev=t.next){case 0:r=jQuery(x).select2("data");if(!r.length){t.next=7;break}n=r.map((function(e){return e.id}));t.next=5;return(0,o.Kz)(E.value,n);case 5:i=t.sent;if(i.success&&q){q.style.setProperty("display","flex","important");q.querySelector("div").innerText=i.data.message;document.querySelector(".tutor-email-modal-title strong").innerText=i.data.count}case 7:case"end":return t.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 34:T=document.querySelector(".tutor-trigger-restore-button");if(T){T.addEventListener("click",(function(e){if(e.target.dataset.defaults){(0,o.WD)(JSON.parse(e.target.dataset.defaults))}}))}if(f){f.addEventListener("click",(function(e){e.preventDefault();var a=document.getElementsByName("testing_email")[0].value;if(!(0,o.Iy)(a)){tutor_toast(u("Sorry","tutor-pro"),u("Invalid e-mail address","tutor-pro"),"error");return}var c;if(n){c="trigger_template"}else if(i){c="email_settings"}else if(l){c="mailer"}var d=new FormData;d.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);d.append("test_type",c);d.append("email_template",w);d.append("action","send_test_email_ajax");d.append("testing_email",a);if(n){d.append("email_to",document.getElementsByName("to")[0].value);d.append("email_key",document.getElementsByName("key")[0].value)}f.classList.add("is-loading");f.disabled=true;(0,o.MC)(s,r).then((function(){jQuery.ajax({url:_tutorobject.ajaxurl,method:"POST",data:d,processData:false,contentType:false,success:function e(r){var o=f.closest(".tutor-dropdown-parent");if(o){o.classList.remove("is-open")}tutor_toast(v,p(u("A test email for %s has been sent to admin!","tutor-pro"),'"'.concat(t.innerText,'"')),"success")},complete:function e(){f.classList.remove("is-loading");f.disabled=false}})}))["catch"]((function(e){tutor_toast(y,e,"error");f.classList.remove("is-loading");f.disabled=false}))}))}if(d){d.addEventListener("click",(function(e){d.classList.add("is-loading");d.disabled=true;if(!e.detail||e.detail==1){e.preventDefault();(0,o.MC)(s,r).then((function(){tutor_toast(v,p(u("%s email template updated!","tutor-pro"),'"'.concat(t.innerText,'"')),"success")}))["catch"]((function(e){tutor_toast(y,e,"error")}))["finally"]((function(){d.classList.remove("is-loading");d.disabled=false}))}}))}if(h){h.addEventListener("click",(function(e){e.preventDefault();var t=e.target;t.classList.add("is-loading");t.disabled=true;(0,o.MC)(l).then((function(){tutor_toast(v,u("Email saved successfully!","tutor-pro"),"success")}))["catch"]((function(e){tutor_toast(y,e,"error")}))["finally"]((function(){t.classList.remove("is-loading");t.disabled=false}))}))}if(g){g.addEventListener("click",(function(e){var t;e.preventDefault();var r=l.querySelector("#tutor-mailer-course-ids");var o=jQuery(r).select2("data");var n=(t=l.querySelector("select[name=receiver_type]"))===null||t===void 0?void 0:t.value;if(!n){return tutor_toast(y,u("Email receiver type is required!","tutor-pro"),"error")}if(m.includes(n)&&o.length===0){return tutor_toast(y,u("Select courses is required!","tutor-pro"),"error")}document.querySelector("#tutor-email-confirmation-modal").classList.add("tutor-is-active")}))}if(_){_.addEventListener("click",(function(e){e.preventDefault();var t=e.target;var r=new FormData;r.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);r.append("email_template",w);r.append("action","tutor_sent_manual_email");t.classList.add("is-loading");t.disabled=true;(0,o.MC)(l).then((function(){jQuery.ajax({url:_tutorobject.ajaxurl,method:"POST",data:r,processData:false,contentType:false,success:function e(t){if(t.success){tutor_toast(v,t.message,"success")}else{tutor_toast(y,t.message,"error")}},complete:function e(){t.classList.remove("is-loading");t.disabled=false;document.querySelector("#tutor-email-confirmation-modal").classList.remove("tutor-is-active")}})}))["catch"]((function(e){tutor_toast(y,e,"error");t.classList.remove("is-loading");t.disabled=false}))}))}case 41:case"end":return A.stop()}}),e)}))))})()})();