(()=>{"use strict";var e={449:(e,t,r)=>{r.d(t,{RW:()=>p,fW:()=>y});var o=wp.i18n,n=o.__,a=o._x,i=o._n,c=o._nx;function l(e){var t=e.split(",");var r=/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;for(var o=0;o<t.length;o++){var n=t[o].trim();if(!r.test(n)){return false}}return true}function u(e,t){return new Promise((function(r,o){var a=new FormData(e);a.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);var i=a.get("action");if(i==="save_email_template"){a.append(t.name,t.value)}if(i==="save_email_settings"){var c=a.get("email_from_address");var u=a.get("email_from_name");if(!c||!u){e.reportValidity();return o(n("Sender Email Address and Name are required!","tutor-pro"))}if(!l(c)){e.reportValidity();return o(n("Please provide a valid Email Address","tutor-pro"))}}else{var s=a.get("email-subject")||a.get("email_subject");var d=a.get("email-heading")||a.get("email_heading");if(!s||!d){e.reportValidity();return o(n("Email subject and heading are required!","tutor-pro"))}}var f=new XMLHttpRequest;f.open("POST",_tutorobject.ajaxurl,true);f.send(a);f.onreadystatechange=function(e){if(f.readyState===4){var t=JSON.parse(f.response);if(t.success){r(t.message)}else{o(t.message)}}}}))}function s(e,t){if(e){if(e.src){e.src=t}else{e.innerHTML=t}}}function d(e){var t=document.createElement("iframe");t.width="100%";t.height="800px";t.src="".concat(_tutorobject.home_url,"?page=tutor-email-preview&template=").concat(e);return t}function f(e){var t=e.querySelector(".tutor-email-body");var r=e.querySelector(".tutor-email-footer-text");var o=e.querySelector(".tutor-email-footer-content");var n=e.querySelectorAll("a");t.style.paddingTop="0px";t.style.paddingLeft="0px";t.style.paddingRight="0px";t.style.backgroundColor="transparent";o.style.backgroundColor="transparent";n.forEach((function(e){e.classList.add("preview-only");e.addEventListener("click",(function(e){return e.preventDefault()}))}));if(r){var a=new URLSearchParams(window.location.href);var i=a.get("edit");if(i==="settings"){r.innerText="Example of a no-reply or instructional footnote"}}var c=t.querySelectorAll(".user-avatar");c.forEach((function(e){e.src="http://2.gravatar.com/avatar/bc78e9eeca4abe2043bb671018d504ef?s=96&d=mm&r=g"}))}function p(e){if(!e||e.length===0){return""}return'\t\n        <ul class="tutor-dropdown" style="width: 100%; margin: 0; max-height: 200px; overflow: scroll;">\n            '.concat(e.map((function(e){return'\n                    <li>\n                        <a class="tutor-dropdown-item tutor-d-flex tutor-justify-between" href="#" data-state-text="'.concat(e.placeholder,'">\n                            <span class="tutor-color-muted">').concat(e.placeholder,"</span>\n                            <span>").concat(e.label,"</span>\n                        </a>\n                    </li>\n                ")})).join(""),"\n        </ul>\n    ")}function v(e,t){var r=new FormData;r.append(_tutorobject.nonce_key,_tutorobject._tutor_nonce);r.append("action","tutor_manual_email_receiver_count_help_text");r.append("receiver_type",e);if(t){t.forEach((function(e){r.append("course_ids[]",e)}))}return jQuery.ajax({url:_tutorobject.ajaxurl,method:"POST",data:r,processData:false,contentType:false})}function m(e,t,r,o){if(e){e.style.setProperty(t,r,o)}}function y(e){var t=e.selection.getRng();var r=t.getClientRects()[0];if(r){var o={offsetTop:r.top+124,offsetLeft:r.left};return o}return{}}function h(e){var t={subject:"email-subject",heading:"email-heading",message:"email-additional-message",before_button:"email-before-button",footer_text:"email-footer-text"};Object.keys(e).map((function(r){if(r==="message"){var o=tinymce.get(t[r]);if(o){o.setContent(JSON.parse(e[r]));tinymce.triggerSave()}}else{var n=document.querySelector("[name=".concat(t[r],"]"));if(n){n.value=e[r];n.dispatchEvent(new Event("input",{bubbles:true}))}}}))}}};var t={};function r(o){var n=t[o];if(n!==undefined){return n.exports}var a=t[o]={exports:{}};e[o](a,a.exports,r);return a.exports}(()=>{r.d=(e,t)=>{for(var o in t){if(r.o(t,o)&&!r.o(e,o)){Object.defineProperty(e,o,{enumerable:true,get:t[o]})}}}})();(()=>{r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})();var o={};(()=>{var e=r(449);window.addEventListener("DOMContentLoaded",(function(){var t=document.querySelectorAll(".tutor-option-field-input.tutor-email-placeholders");t.forEach((function(t){var r=t.dataset.placeholders;var o=document.createElement("div");o.setAttribute("class","tutor-dropdown-parent");o.innerHTML=(0,e.RW)(r?JSON.parse(r):window._tutorEmailPlaceholders);var n=t.classList.contains("tutor-has-tinymce-editor");t.insertBefore(o,n?t.firstChild:t.lastChild)}));var r=document.querySelectorAll('.tutor-option-field-input.tutor-email-placeholders input[type="text"], .tutor-option-field-input.tutor-email-placeholders textarea');r.forEach((function(e){e.addEventListener("input",(function(e){var t=e.target.parentNode.querySelector(".tutor-dropdown-parent");if(t){if(e.data==="{"){e.target.blur();t.classList.add("is-open");t.querySelectorAll(".tutor-dropdown-item")[0].classList.add("is-active");t.querySelectorAll(".tutor-dropdown-item")[0].focus();document.querySelector("body").style.overflow="hidden";setTimeout((function(){t.querySelector(".tutor-dropdown").scrollTop=0}),100)}else{t.classList.remove("is-open");document.querySelector("body").style.overflow="auto"}}}))}));var o=document.querySelectorAll(".tutor-option-field-input.tutor-email-placeholders .tutor-dropdown-item");o.forEach((function(e){e.addEventListener("click",(function(t){t.preventDefault();var r=e.getAttribute("data-state-text");r=r.slice(1,r.length);var o=e.closest(".tutor-option-field-input").querySelector(":scope > input, :scope > textarea");if(o){var n=o.selectionStart;var a=n+r.length;o.value=o.value.substring(0,n)+r+o.value.substring(n);o.dispatchEvent(new Event("input",{bubbles:true}));o.setSelectionRange(a,a);o.focus()}else if(tinymce.activeEditor){tinymce.activeEditor.insertContent(r);tinymce.triggerSave()}}))}));var n=0;document.addEventListener("keydown",(function(e){var t=document.querySelector(".tutor-email-placeholders .tutor-dropdown-parent.is-open");if(t){var r=t.querySelectorAll(".tutor-dropdown-item");if(e.key==="ArrowUp"){if(n>0){n-=1;r.forEach((function(e){e.classList.remove("is-active");e.blur()}));r[n].classList.add("is-active");r[n].focus()}}else if(e.key==="ArrowDown"){if(n<r.length-1){n+=1;r.forEach((function(e){e.classList.remove("is-active");e.blur()}));r[n].classList.add("is-active");r[n].focus()}}else if(e.key==="Enter"){var o=document.querySelector(".tutor-email-placeholders .tutor-dropdown-item.is-active");if(o){o.click();n=0;r.forEach((function(e){e.classList.remove("is-active");e.blur()}))}}else{n=0}if(e.key==="Escape"){t.classList.remove("is-open");r.forEach((function(e){e.classList.remove("is-active");e.blur()}));var a=t.closest(".tutor-option-field-input").querySelector(":scope > input, :scope > textarea");if(a){a.focus()}else if(tinymce.activeEditor){tinymce.activeEditor.focus()}}}}));document.querySelectorAll(".tutor-email-placeholders .tutor-dropdown-parent").forEach((function(e){e.addEventListener("tutor_dropdown_closed",(function(e){document.querySelector("body").style.overflow="auto";e.target.querySelectorAll(".tutor-dropdown-item").forEach((function(e){return e.classList.remove("is-active")}))}))}))}));document.addEventListener("readystatechange",(function(){if(typeof tinymce!="undefined"&&tinymce.activeEditor){tinymce.activeEditor.on("input",(function(t){var r=(0,e.fW)(tinymce.activeEditor),o=r.offsetTop,n=r.offsetLeft;var a=document.querySelector(".wp-editor-wrap");var i=a.parentNode.querySelector(".tutor-dropdown-parent");if(i){if(t.data==="{"){var c=document.createElement("input");i.appendChild(c);c.focus();i.removeChild(c);i.classList.add("is-open");i.querySelector(".tutor-dropdown").style.top=o+"px";i.querySelectorAll(".tutor-dropdown-item")[0].classList.add("is-active");i.querySelectorAll(".tutor-dropdown-item")[0].focus();document.querySelector("body").style.overflow="hidden";setTimeout((function(){i.querySelector(".tutor-dropdown").scrollTop=0}),100)}else{i.classList.remove("is-open");document.querySelector("body").style.overflow="auto"}}}))}}))})()})();