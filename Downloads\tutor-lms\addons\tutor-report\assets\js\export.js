(()=>{var n={};jQuery(document).ready((function(n){n(document).on("click","#download_analytics",(function(t){t.preventDefault();var a=n(this);if(a.hasClass("is-loading")){return}n.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:{action:"export_analytics"},beforeSend:function n(){a.addClass("is-loading")},success:function n(t){if(t.success){e(t.data)}},complete:function n(){a.removeClass("is-loading")}})}));function e(n){var e=n.students;var t=n.earnings;var a=n.discounts.length;var i=n.refunds;var o=new JSZip;if(e.length){var r=Object.keys(e[0]);var c=[r.join(","),e.map((function(n){return r.map((function(e){return n[e]})).join(",")})).join("\n")].join("\n");var s=new Blob([c]);o.file("students.csv",s)}if(t.length){var u=Object.keys(t[0]);var l=[u.join(","),t.map((function(n){return u.map((function(e){return n[e]})).join(",")})).join("\n")].join("\n");var f=new Blob([l]);o.file("earnings.csv",f)}if(a.length){var d=Object.keys(a[0]);var v=[d.join(","),a.map((function(n){return d.map((function(e){return n[e]})).join(",")})).join("\n")].join("\n");var j=new Blob([v]);o.file("discounts.csv",j)}if(i.length){var p=Object.keys(i[0]);var b=[p.join(","),i.map((function(n){return p.map((function(e){return n[e]})).join(",")})).join("\n")].join("\n");var m=new Blob([b]);o.file("refunds.csv",m)}try{o.generateAsync({type:"blob"}).then((function(n){var e=new Blob([n],{type:"application/zip"});var t=document.createElement("a");document.body.appendChild(t);t.download="analytics-data.zip";t.href=URL.createObjectURL(e);t.click();URL.revokeObjectURL(t.href)}))}catch(n){alert(n)}}}))})();