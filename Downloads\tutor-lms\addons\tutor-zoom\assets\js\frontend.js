(()=>{var n={};(function(n){"use strict";n(document).ready((function(){var t=wp.i18n,a=t.__,o=t._x,s=t._n,i=t._nx;n(".tutor-zoom-meeting-countdown").each((function(){var t=n(this).data("timer");var o=n(this).data("timezone");var s=moment.tz(t,o);n(this).countdown(s.toDate(),(function(t){n(this).html(t.strftime("<div>\n                        <h3>%D</h3>\n                        <p>".concat(a("Days","tutor-pro"),"</p>\n                    </div>\n                    <div>\n                        <h3>%H</h3>\n                        <p>").concat(a("Hours","tutor-pro"),"</p>\n                    </div>\n                    <div>\n                        <h3>%M</h3>\n                        <p>").concat(a("Minutes","tutor-pro"),"</p>\n                    </div>\n                    <div>\n                        <h3>%S</h3>\n                        <p>").concat(a("Seconds","tutor-pro"),"</p>\n                    </div>")))}))}));n(".tutor-zoom-lesson-countdown").each((function(){var t=n(this).data("timer");var a=n(this).data("timezone");var o=moment.tz(t,a);n(this).countdown(o.toDate(),(function(t){n(this).html(t.strftime("<span>%D <span>d</span></span> <span>%H <span>h</span></span> <span>%M <span>m</span></span> <span>%S <span>s</span></span>"))}))}))}))})(jQuery)})();