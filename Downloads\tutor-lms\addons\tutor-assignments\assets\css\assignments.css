.tutor-assignment-details-wrap{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;background:#dbdbdb}.tutor-assignment-details{width:60%;padding:32px}.tutor-assignment-evaluation{width:40%;padding:32px;background:#eff1f7}.tutor-assignment-details{border-right:1px solid #dcdbdc;background:#e9eaed}.tutor-assignment-details h4,.tutor-assignment-evaluation h4{font-weight:500;font-size:20px;color:#161616;margin:0px;padding-bottom:22px}.tutor-assignment-details p{font-weight:normal;font-size:16px;line-height:26px;color:#525252;margin:0px}.assignment-files h4{margin-bottom:20px}.tutor-assignment-files{display:-webkit-box;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;margin:-9px}.tutor-assignment-files .uploaded-files{background-color:#fff;border:1px solid #bababa;border-radius:6px;margin:9px;-webkit-transition:300ms;transition:300ms}.tutor-assignment-files .uploaded-files:hover{-webkit-box-shadow:0 5px 10px rgba(0,0,0,.1);box-shadow:0 5px 10px rgba(0,0,0,.1)}.tutor-assignment-files .uploaded-files a{color:var(--tutor-color-primary);display:block;overflow:hidden;line-height:33px;padding:10px 15px;text-decoration:none;color:#353535;font-size:14px}.tutor-assignment-files .uploaded-files a:hover{color:var(--tutor-color-primary-hover)}.tutor-assignment-files .uploaded-files a i{margin-left:12px;color:#1973aa;float:right;font-size:18px;line-height:18px;background:#e9edfb;border-radius:50%;padding:8px}.tutor-assignment-evaluation form{margin-top:22px}.tutor-assignment-evaluation form input{margin:0px !important}.tutor-assignment-evaluation label{color:#525252;font-weight:400;font-size:16px;cursor:auto}.tutor-assignment-filter-box .alignright{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:10px}.tutor-assignment-filter-box .alignright{margin-left:30px}.tutor-assignment-filter-box label{color:#353535;font-size:15px;font-weight:500}.tutor-assignment-filter-box input,.tutor-assignment-filter-box select{min-width:227px;max-width:100%;height:40px;border-radius:3px;border:solid 1px #dcdce1;background-color:#fff;padding:0 14px;-webkit-transition:.2s;transition:.2s}.tutor-assignment-filter-box .assignment-search-box,.tutor-assignment-filter-box .assignment-date-box{position:relative}.assignment-search-box i,.assignment-date-box i{position:absolute;right:10px;top:60%;color:#1973aa;cursor:pointer;font-size:17px}.assignment-date-box i{cursor:auto !important}#assignments-filter table th{padding:16px 20px;font-weight:normal;font-size:13px;color:#525252;background-color:#f3f3f3}#assignments-filter table tr:nth-child(odd){background-color:#fff}#assignments-filter table tr:nth-child(even){background-color:#f3f3f3}#assignments-filter table td,#assignments-filter table th{vertical-align:middle;padding:20px 15px}#assignments-filter table th.column-title{width:20%}#assignments-filter table th.column-student{width:14%}#assignments-filter table th.column-mark,#assignments-filter table th.column-passing_mark,#assignments-filter table th.column-duration{width:10%}#assignments-filter table th.column-date{width:18%}#assignments-filter table th.column-action{width:18%}#assignments-filter table .title a,#assignments-filter .student{font-size:14px;font-weight:500}#assignments-filter .student{text-transform:capitalize}#assignments-filter .mark,#assignments-filter .passing_mark,#assignments-filter .duration{font-size:15px;font-weight:500}#assignments-filter td.column-action a{margin:5px}#assignments-filter td.column-action .button-delete{color:#7a7a7a;border:1px solid #bababa}#assignments-filter .tutor-assignment-action{font-size:12px;font-weight:600;padding:4px 14px}#assignments-filter .assignment-submitter-avatar{width:34px;height:34px;border-radius:50%;overflow:hidden;display:inline-block;vertical-align:middle;margin-right:12px}#assignments-filter a.tutor-assignment-course-title{display:inline-block;margin-bottom:6px;color:#161616}#assignments-filter a.tutor-assignment-course-title:hover{color:var(--tutor-color-primary)}#assignment-search::-webkit-search-cancel-button{position:relative;right:20px}@media only screen and (max-width: 768px){.tutor-assignment-details-wrap{-ms-flex-wrap:wrap;flex-wrap:wrap}.tutor-assignment-evaluation{width:100%}.tutor-assignment-details{border:none;height:auto;width:100%}.tutor-assignment-filter-box .alignright{margin-left:0px}.tutor-assignment-filter-box .alignright{float:left !important}.tutor-assignment-filter-box input,.tutor-assignment-filter-box select{max-width:50% !important}}@media only screen and (min-width: 1600px){#assignments-filter table th.column-date{width:20%}}