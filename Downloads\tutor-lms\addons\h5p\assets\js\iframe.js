(()=>{var t={};var e=XMLHttpRequest.prototype.open;var n=XMLHttpRequest.prototype.send;var a,i="";XMLHttpRequest.prototype.open=function(t,n,o,s,d){i=n;a=t;return e.apply(this,arguments)};XMLHttpRequest.prototype.send=function(t){var e=new URLSearchParams(i);var a=e.get("action");if(t){var o=new URLSearchParams(t);window.parent.postMessage({action:a,contentId:o.get("contentId")},"*")}return n.apply(this,arguments)};window.addEventListener("message",(function(t){if(t.data&&t.data.action==="set_iframe"){var e=this.document.querySelector(t.data.selector);if(e){if(H5P){H5P.XAPIEvent.prototype.setObject=function(e){if(e.contentId){this.data.statement.object={id:this.getContentXAPIId(e),objectType:"Activity",definition:{extensions:{"http://h5p.org/x-api/h5p-local-content-id":e.contentId,"http://h5p.org/x-api/h5p-local-question-id":t.data.question_id}}};if(e.subContentId){this.data.statement.object.definition.extensions["http://h5p.org/x-api/h5p-subContentId"]=e.subContentId;if(typeof e.getTitle==="function"){this.data.statement.object.definition.name={"en-US":e.getTitle()}}}else{var n=H5P.getContentForInstance(e.contentId);if(n&&n.metadata&&n.metadata.title){this.data.statement.object.definition.name={"en-US":H5P.createTitle(n.metadata.title)}}}}else{this.data.statement.object={definition:{}}}}}}}}),false)})();