(()=>{"use strict";var t={};window.jQuery(document).ready((function(t){var e=wp.i18n.__;function o(e,o){var r=e.getBoundingClientRect();setTimeout((function(){var a=t("body").scrollTop();o.dpDiv.css({top:r.top+e.offsetHeight+a})}),0)}function r(){t(".tutor_zoom_datepicker").datepicker({dateFormat:_tutorobject.wp_date_format});t(".tutor_zoom_timepicker").timepicker({timeFormat:"hh:mm TT",beforeShow:function t(e,r){o(e,r)}})}r();window.addEventListener(_tutorobject.content_change_event,r);t(document).on("click",".update_zoom_meeting_modal_btn",(function(r){r.preventDefault();var a=t(this);var n=a.closest(".tutor-modal");n.find("[data-name]").each((function(){t(this).attr("name",t(this).attr("data-name"))}));var i=n.find(":input").serializeObject();i.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone;n.find("[data-name]").removeAttr("name");for(var s in i){if(i[s]!=="timezone"&&!i[s]){tutor_toast(e("Warning!","tutor-pro"),e("Please fill all the fields","tutor-pro"),"warning");return}}t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:i,beforeSend:function t(){a.attr("disabled","disabled").addClass("is-loading")},success:function r(i){var s=i||{},r=s.success,c=s.data,d=c===void 0?{}:c;var u=d.selector,m=d.replace_selector,l=d.course_contents,_=d.editor_modal_html;if(i.status_code!==200){tutor_toast(e("Error!","tutor-pro"),i.message,"error");return}tutor_toast(e("Success","tutor-pro"),i.message,"success");if(!l){window.location.reload();return}if(u=="course-builder"){if(m){t(m).replaceWith(l)}else{a.closest(".tutor-topics-body").find(".tutor-lessons").append(l)}enable_sorting_topic_lesson()}else{t(i.data.selector).html(i.data.course_contents)}if(_){n.replaceWith(_)}t(".tutor_zoom_timepicker").timepicker({timeFormat:"hh:mm TT",beforeShow:function t(e,r){o(e,r)}});t(".tutor_zoom_datepicker").datepicker({dateFormat:_tutorobject.wp_date_format,minDate:0});n.removeClass("tutor-is-active");t("body").removeClass("tutor-modal-open");window.dispatchEvent(new Event(_tutorobject.content_change_event))},complete:function t(){a.removeAttr("disabled").removeClass("is-loading")}})}))}))})();