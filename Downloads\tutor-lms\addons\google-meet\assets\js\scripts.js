(()=>{"use strict";var t={};function e(t){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function e(){return t};var t={},n=Object.prototype,o=n.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function f(t,e,r,n){var o=e&&e.prototype instanceof p?e:p,i=Object.create(o.prototype),u=new j(n||[]);return a(i,"_invoke",{value:L(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function p(){}function v(){}function m(){}var g={};l(g,u,(function(){return this}));var y=Object.getPrototypeOf,w=y&&y(y(O([])));w&&w!==n&&o.call(w,u)&&(g=w);var b=m.prototype=p.prototype=Object.create(g);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,r){function n(a,i,u,c){var s=d(t[a],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==e(f)&&o.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(f).then((function(t){l.value=t,u(l)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function t(e,o){function a(){return new r((function(t,r){n(e,o,t,r)}))}return i=i?i.then(a,a):a()}})}function L(t,e,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return F()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var u=E(i,r);if(u){if(u===h)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=d(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=d(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var a=o.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function O(t){if(t){var e=t[u];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(o.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=undefined,e.done=!0,e};return n.next=n}}return{next:F}}function F(){return{value:undefined,done:!0}}return v.prototype=m,a(b,"constructor",{value:m,configurable:!0}),a(m,"constructor",{value:v,configurable:!0}),v.displayName=l(m,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(_.prototype),l(_.prototype,c,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new _(f(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(b),l(b,s,"Generator"),l(b,u,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,j.prototype={constructor:j,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var r=this;function n(t,n){return u.type="throw",u.arg=e,r.next=t,n&&(r.method="next",r.arg=undefined),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),s=o.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function t(e,r){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),h},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),h}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;S(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),h}},t}function n(t,e,r,n,o,a,i){try{var u=t[a](i);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,o)}}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,a){var i=t.apply(e,r);function u(t){n(i,o,a,u,c,"next",t)}function c(t){n(i,o,a,u,c,"throw",t)}u(undefined)}))}}function a(t){return i.apply(this,arguments)}function i(){i=o(r().mark((function t(e){var n;return r().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;r.next=3;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:e});case 3:n=r.sent;return r.abrupt("return",n);case 7:r.prev=7;r.t0=r["catch"](0);tutor_toast(__("Operation failed","tutor-pro"),r.t0,"error");case 10:case"end":return r.stop()}}),t,null,[[0,7]])})));return i.apply(this,arguments)}function u(t){"@babel/helpers - typeof";return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function f(t,e,r,o){var a=e&&e.prototype instanceof p?e:p,i=Object.create(a.prototype),u=new j(o||[]);return n(i,"_invoke",{value:L(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function p(){}function v(){}function m(){}var g={};l(g,a,(function(){return this}));var y=Object.getPrototypeOf,w=y&&y(y(O([])));w&&w!==e&&r.call(w,a)&&(g=w);var b=m.prototype=p.prototype=Object.create(g);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function o(n,a,i,c){var s=d(t[n],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==u(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){o("next",t,i,c)}),(function(t){o("throw",t,i,c)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return o("throw",t,i,c)}))}c(s.arg)}var a;n(this,"_invoke",{value:function t(r,n){function i(){return new e((function(t,e){o(r,n,t,e)}))}return a=a?a.then(i,i):i()}})}function L(t,e,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return F()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var u=E(i,r);if(u){if(u===h)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=d(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function E(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,E(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=d(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var a=o.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function O(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:F}}function F(){return{value:undefined,done:!0}}return v.prototype=m,n(b,"constructor",{value:m,configurable:!0}),n(m,"constructor",{value:v,configurable:!0}),v.displayName=l(m,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(_.prototype),l(_.prototype,i,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new _(f(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(b),l(b,s,"Generator"),l(b,a,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,j.prototype={constructor:j,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=n&&n<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=n,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),h},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),h}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;S(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),h}},t}function s(t,e,r,n,o,a,i){try{var u=t[a](i);var c=u.value}catch(t){r(t);return}if(u.done){e(c)}else{Promise.resolve(c).then(n,o)}}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){s(a,n,o,i,u,"next",t)}function u(t){s(a,n,o,i,u,"throw",t)}i(undefined)}))}}var f=wp.i18n,d=f.__,h=f.sprintf;window.jQuery(document).ready((function(t){var e=document.getElementById("tutor-google-meet-meta-box-wrapper");var r=document.getElementById("tutor-common-confirmation-modal");var n=d("Something went wrong, please refresh the page & try again!","tutor-pro");var o=document.getElementById("tutor-course-content-builder-root");var i="tutor-gm-create-new-meeting";var u="tutor-gm-update-meeting";if(e){e.onclick=function(t){var e=t.target;if(e.classList.contains("tutor-google-meet-list-delete")){t.preventDefault();s(e)}if(e.classList.contains("tutor-gm-delete")){t.preventDefault();e=e.closest("a.tutor-google-meet-list-delete");s(e)}if(e.classList.contains(i)||e.classList.contains(u)){t.preventDefault();try{p(e)}catch(t){tutor_toast(d("Failed","tutor-pro"),n,"warning")}}if(e.hasAttribute("type")&&e.getAttribute("type")==="checkbox"){if(e.hasAttribute("checked")){e.removeAttribute("checked")}else{e.setAttribute("checked","checked")}}return};t(document.body).on("change",'#tutor-google-meet-meta-box-wrapper input[type="checkbox"]',(function(){if(t(this).is(":checked")){t(this).closest(".tutor-modal").find("input[name=attendees]").val("Yes")}else{t(this).closest(".tutor-modal").find("input[name=attendees]").val("No")}}))}if(o){o.onclick=function(t){var e=t.target;if(e.classList.contains("tutor-google-meet-list-delete")){t.preventDefault();s(e)}if(e.classList.contains(i)||e.classList.contains(u)){t.preventDefault();try{p(e)}catch(t){tutor_toast(d("Failed","tutor-pro"),n,"warning")}}return};t(document.body).on("change",'#tutor-course-content-builder-root input[type="checkbox"]',(function(){if(t(this).is(":checked")){t(this).closest(".tutor-modal").find("input[name=attendees]").val("Yes")}else{t(this).closest(".tutor-modal").find("input[name=attendees]").val("No")}}))}function s(t){var e=t.dataset.eventId;var n=t.dataset.meetingPostId;var o=t.dataset.itemReference;r.querySelector("[name=id]").value=n;r.querySelector("[name=event-id]").value=e;r.querySelector("[name=item-reference]").value=o}if(r){var f=r.querySelector("button[data-tutor-modal-submit]");f.onclick=function(){var e=l(c().mark((function e(n){var o,i,u,s,l;return c().wrap((function e(c){while(1)switch(c.prev=c.next){case 0:n.preventDefault();o=n.target;i=new FormData;u=r.querySelector("[name=item-reference]").value;i.set("event-id",r.querySelector("[name=event-id]").value);i.set("post-id",r.querySelector("[name=id]").value);i.set("action","tutor_google_meet_delete");i.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);o.setAttribute("disabled",true);o.classList.add("is-loading");c.next=12;return a(i);case 12:s=c.sent;if(!s.ok){c.next=32;break}c.next=16;return s.json();case 16:l=c.sent;if(!(l.status_code===200||l.status_code===201)){c.next=24;break}tutor_toast(d("Success","tutor-pro"),l.message,"success");if(!(_tutorobject.current_page==="google-meet")){c.next=22;break}window.location.reload();return c.abrupt("return");case 22:c.next=25;break;case 24:tutor_toast(d("Failed","tutor-pro"),l.message,"warning");case 25:o.classList.remove("is-loading");o.removeAttribute("disabled");r.classList.remove("tutor-is-active");t("body").removeClass("tutor-modal-open");window.dispatchEvent(new Event(_tutorobject.content_change_event));c.next=35;break;case 32:tutor_toast(d("Error","tutor-pro"),d("Something went wrong, please try after refreshing page","tutor-pro"),"error");o.classList.remove("is-loading");o.removeAttribute("disabled");case 35:case"end":return c.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function p(t){return v.apply(this,arguments)}function v(){v=l(c().mark((function e(r){var n,o,i,u,s,l,f,p,v=arguments;return c().wrap((function e(c){while(1)switch(c.prev=c.next){case 0:n=v.length>1&&v[1]!==undefined?v[1]:[];o=r.closest(".tutor-modal");i=o.getAttribute("id");u=o.querySelectorAll("[name]");s=new FormData;l=[];u.forEach((function(t){if(t.value===""&&t.name!=="attendees"){l.push(h(d("%s is required","tutor-pro"),t.name))}if(t.name==="attendees"){s.set(t.name,t.value===""?"Yes":t.value)}else{s.set(t.name,t.value)}}));s.set("action","tutor_google_meet_new_meeting");n.forEach((function(t){s.set(t.name,t.value)}));r.classList.add("is-loading");r.setAttribute("disabled",true);if(!l.length){c.next=16;break}l.forEach((function(t){tutor_toast(d("Validation Error","tutor-pro"),m(t.replace(/_/g," ")),"warning")}));r.removeAttribute("disabled");r.classList.remove("is-loading");return c.abrupt("return");case 16:c.next=18;return a(s);case 18:f=c.sent;if(!f.ok){c.next=40;break}c.next=22;return f.json();case 22:p=c.sent;if(!(p.status_code===200||p.status_code===201)){c.next=37;break}if(!(i==="tutor-google-meet-create-modal"||o.classList.contains("tutor-gm-topic-create-modal"))){c.next=28;break}tutor_toast(d("Success","tutor-pro"),p.message,"success");c.next=32;break;case 28:tutor_toast(d("Success","tutor-pro"),p.message,"success");if(!(_tutorobject.current_page==="google-meet")){c.next=32;break}window.location.reload();return c.abrupt("return");case 32:o.classList.remove("tutor-is-active");t("body").removeClass("tutor-modal-open");window.dispatchEvent(new Event(_tutorobject.content_change_event));c.next=38;break;case 37:tutor_toast(d("Failed","tutor-pro"),p.message,"warning");case 38:c.next=41;break;case 40:tutor_toast(d("Failed","tutor-pro"),d("Something went wrong, please try again!","tutor-pro"),"warning");case 41:r.classList.remove("is-loading");r.removeAttribute("disabled");case 43:case"end":return c.stop()}}),e)})));return v.apply(this,arguments)}function m(t){var e=t.substr(0,1);return e.toUpperCase()+t.substr(1)}function g(e,r){var n=e.getBoundingClientRect();setTimeout((function(){var o=t("body").scrollTop();r.dpDiv.css({top:n.top+e.offsetHeight+o})}),0)}function y(){t(".tutor-google-meet-timepicker").timepicker({timeFormat:"hh:mm TT",beforeShow:function t(e,r){g(e,r)}})}function w(){t(".tutor-google-meet-timepicker").timepicker({timeFormat:"hh:mm TT",beforeShow:function t(e,r){g(e,r)}})}y();window.addEventListener(_tutorobject.content_change_event,y);var b=document.querySelectorAll(".tutor-google-meet-credential-form .drag-drop-zone input[type=file]");if(b.length>0){b.forEach((function(t){var e=t.closest(".drag-drop-zone");["dragover","dragleave","dragend"].forEach((function(t){if(t==="dragover"){e.addEventListener(t,(function(t){t.preventDefault();e.classList.add("dragover")}))}else{e.addEventListener(t,(function(t){e.classList.remove("dragover")}))}}));e.addEventListener("drop",(function(r){r.preventDefault();var n=r.dataTransfer.files;x(n,t,e);e.classList.remove("dragover")}));t.addEventListener("change",(function(r){var n=r.target.files;x(n,t,e)}))}))}var x=function t(e,r,n){if(e.length){r.files=e;n.classList.add("file-attached");n.querySelector(".file-info").innerHTML="<strong>File attached</strong> - ".concat(e[0].name)}else{n.classList.remove("file-attached");n.querySelector(".file-info").innerHTML=""}};var _=document.getElementById("tutor-google-meet-credential-upload");var L=document.getElementById("tutor-google-meet-choose-label");var E=document.querySelector(".tutor-google-meet-credential-form .drag-drop-zone");if(L){L.onclick=function(t){_.click()}}if(_){_.onchange=function(t){var e=t.target.files[0];k(e)}}if(E){E.addEventListener("drop",(function(t){t.preventDefault();var e=t.dataTransfer.files[0];k(e)}))}function k(t){return S.apply(this,arguments)}function S(){S=l(c().mark((function t(e){var r,n,o,i;return c().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:r=new FormData;n=document.querySelector(".file-info");r.set("file",e);r.set("action","tutor_pro_google_meet_credential_upload");r.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);u.prev=5;if(!(e.type==="application/json")){u.next=19;break}L.classList.add("is-loading");u.next=10;return a(r);case 10:o=u.sent;if(!o.ok){u.next=17;break}u.next=14;return o.json();case 14:i=u.sent;if(i.success){tutor_toast(d("Success","tutor-pro"),i.data,"success");window.location.reload()}else{tutor_toast(d("Error","tutor-pro"),i.data,"warning")}L.classList.remove("is-loading");case 17:u.next=23;break;case 19:L.classList.remove("is-loading");tutor_toast(d("Error","tutor-pro"),d("Invalid file type!","tutor-pro"),"warning");_.value="";if(n){n.innerHTML=""}case 23:u.next=28;break;case 25:u.prev=25;u.t0=u["catch"](5);tutor_toast(d("Error","tutor-pro"),d("Something went wrong, please try again!","tutor-pro"),"warning");case 28:case"end":return u.stop()}}),t,null,[[5,25]])})));return S.apply(this,arguments)}var j=document.getElementById("tutor-google-meet-settings");if(j){j.onchange=function(){var t=l(c().mark((function t(e){var r,o,i,u,s,l,f;return c().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:r=false;o=e.target;if(!(o.hasAttribute("type")&&o.getAttribute("type")==="search")){c.next=4;break}return c.abrupt("return");case 4:if(!o.hasAttribute("data-value")){c.next=10;break}r=true;i=e.target.dataset.value;u=o.value;if(!(i===u)){c.next=10;break}return c.abrupt("return");case 10:s=new FormData(j);c.prev=11;c.next=14;return a(s);case 14:l=c.sent;if(!l.ok){c.next=22;break}c.next=18;return l.json();case 18:f=c.sent;if(f.success){tutor_toast(d("Success","tutor-pro"),f.data,"success");if(r){o.setAttribute("data-value",o.value)}}else{tutor_toast(d("Failed","tutor-pro"),f.data,"error")}c.next=23;break;case 22:tutor_toast(d("Error","tutor-pro"),d(l.statusText),"error");case 23:c.next=28;break;case 25:c.prev=25;c.t0=c["catch"](11);tutor_toast(d("Error","tutor-pro"),n,"error");case 28:case"end":return c.stop()}}),t,null,[[11,25]])})));return function(e){return t.apply(this,arguments)}}()}var O=document.getElementById("tutor-meet-confirmation-form");if(O){O.onsubmit=function(){var t=l(c().mark((function t(e){var r,n,o,i,u,s;return c().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:e.preventDefault();r=new FormData(O);n=O.querySelector("[data-tutor-modal-submit]");n.classList.add("is-loading");n.setAttribute("disabled",true);c.prev=5;c.next=8;return a(r);case 8:o=c.sent;c.next=11;return o.json();case 11:i=c.sent;u=i.success,s=i.data;if(u){tutor_toast(d("Success","tutor-pro"),s,"success");location.reload()}else{tutor_toast(d("Failed","tutor-pro"),s,"error")}c.next=19;break;case 16:c.prev=16;c.t0=c["catch"](5);tutor_toast(d("Failed","tutor-pro"),d("Something went wrong, please try again","tutor-pro"),"error");case 19:c.prev=19;n.classList.remove("is-loading");n.removeAttribute("disabled");return c.finish(19);case 23:case"end":return c.stop()}}),t,null,[[5,16,19,23]])})));return function(e){return t.apply(this,arguments)}}()}}))})();